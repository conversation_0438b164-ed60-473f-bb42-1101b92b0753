# ChatGPT Forward Plugin - Main Makefile
# Comprehensive build system for browser extension and project management

.PHONY: help install build build-extension build-controller clean lint format check dev-setup
.PHONY: package package-extension package-project package-all manifest manifest-dev manifest-prod
.PHONY: archive archive-git archive-full dev run-controller run-gui test docs

# Default target
help:
	@echo "ChatGPT Forward Plugin - Available Commands"
	@echo "=============================================="
	@echo ""
	@echo "🚀 Quick Start:"
	@echo "  dev-setup       - Setup complete development environment"
	@echo "  build-all       - Build everything (extension + controller)"
	@echo "  package-all     - Package everything for distribution"
	@echo ""
	@echo "🔧 Development:"
	@echo "  install         - Install all dependencies (npm + python)"
	@echo "  dev             - Start development mode with hot reload"
	@echo "  lint            - Run linting for all components"
	@echo "  format          - Format all code"
	@echo "  check           - Run all quality checks"
	@echo "  test            - Run all tests"
	@echo ""
	@echo "🏗️  Building:"
	@echo "  build           - Build browser extension only"
	@echo "  build-extension - Build browser extension (alias)"
	@echo "  build-controller- Build controller component"
	@echo "  build-all       - Build everything"
	@echo ""
	@echo "📦 Packaging:"
	@echo "  package         - Package browser extension for distribution"
	@echo "  package-extension - Package extension as .crx and .zip"
	@echo "  package-controller - Package controller as .tar.gz"
	@echo "  package-project - Package entire project using git archive"
	@echo "  package-all     - Package everything"
	@echo ""
	@echo "📋 Manifest Management:"
	@echo "  manifest        - Generate production manifest.json"
	@echo "  manifest-dev    - Generate development manifest.json"
	@echo "  manifest-prod   - Generate production manifest.json (alias)"
	@echo ""
	@echo "📁 Archive:"
	@echo "  archive         - Create git archive of entire project"
	@echo "  archive-git     - Create git archive (alias)"
	@echo "  archive-full    - Create full project archive with dependencies"
	@echo ""
	@echo "🎮 Running:"
	@echo "  run-controller  - Run controller server"
	@echo "  run-gui         - Run controller with GUI"
	@echo ""
	@echo "🧹 Maintenance:"
	@echo "  clean           - Clean all build artifacts"
	@echo "  clean-all       - Deep clean everything"
	@echo ""

# Variables
VERSION := $(shell node -p "require('./package.json').version")
PROJECT_NAME := $(shell node -p "require('./package.json').name")
BUILD_DATE := $(shell date +%Y%m%d_%H%M%S)
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
DIST_DIR := dist
PACKAGE_DIR := packages
ARCHIVE_DIR := archives

# Colors for output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# Installation and setup
install:
	@echo "$(BLUE)📦 Installing dependencies...$(RESET)"
	@echo "Installing npm dependencies..."
	@pnpm install
	@echo "Installing Python dependencies for controller..."
	@cd controller && make install
	@echo "Installing Python dependencies for GUI..."
	@cd gui && pip install -r requirements.txt
	@echo "$(GREEN)✅ All dependencies installed successfully!$(RESET)"

dev-setup: install
	@echo "$(BLUE)🔧 Setting up development environment...$(RESET)"
	@mkdir -p $(DIST_DIR) $(PACKAGE_DIR) $(ARCHIVE_DIR)
	@cd controller && make dev-setup
	@echo "$(GREEN)✅ Development environment ready!$(RESET)"

# Development
dev:
	@echo "$(BLUE)🚀 Starting development mode...$(RESET)"
	@echo "Starting Vite dev server..."
	@pnpm run dev

# Code quality
lint:
	@echo "$(BLUE)🔍 Running linting checks...$(RESET)"
	@echo "Linting TypeScript/JavaScript..."
	@pnpm run lint
	@echo "Linting Python controller..."
	@cd controller && make lint
	@echo "$(GREEN)✅ Linting completed!$(RESET)"

format:
	@echo "$(BLUE)✨ Formatting code...$(RESET)"
	@echo "Formatting TypeScript/JavaScript..."
	@pnpm run lint --fix 2>/dev/null || echo "ESLint fix completed"
	@echo "Formatting Python controller..."
	@cd controller && make format
	@echo "$(GREEN)✅ Code formatting completed!$(RESET)"

check: lint
	@echo "$(GREEN)✅ All quality checks passed!$(RESET)"

test:
	@echo "$(BLUE)🧪 Running tests...$(RESET)"
	@echo "Running frontend tests..."
	@if [ -f "package.json" ] && grep -q '"test"' package.json; then \
		pnpm run test; \
	else \
		echo "No frontend tests configured"; \
	fi
	@echo "Running controller tests..."
	@cd controller && if [ -f "pyproject.toml" ]; then \
		uv run pytest tests/ 2>/dev/null || echo "No controller tests found"; \
	fi
	@echo "$(GREEN)✅ Tests completed!$(RESET)"

# Building
build: build-extension

build-extension:
	@echo "$(BLUE)🏗️  Building browser extension...$(RESET)"
	@echo "Building TypeScript and bundling..."
	@pnpm run build:extension
	@echo "$(GREEN)✅ Browser extension built successfully!$(RESET)"

build-controller:
	@echo "$(BLUE)🏗️  Building controller...$(RESET)"
	@if [ -f "controller/Makefile" ]; then \
		cd controller && make build; \
		echo "$(GREEN)✅ Controller built successfully!$(RESET)"; \
	else \
		echo "$(YELLOW)⚠️  Controller Makefile not found, skipping controller build$(RESET)"; \
	fi

build-all: build-extension build-controller
	@echo "$(GREEN)✅ All components built successfully!$(RESET)"

# Manifest management
manifest-dev:
	@echo "$(BLUE)📋 Generating development manifest...$(RESET)"
	@cp public/manifest.json $(DIST_DIR)/manifest.json
	@echo "$(GREEN)✅ Development manifest generated!$(RESET)"

manifest-prod:
	@echo "$(BLUE)📋 Generating production manifest...$(RESET)"
	@cp public/manifest.json $(DIST_DIR)/manifest.json
	@# Update version in manifest
	@if command -v jq >/dev/null 2>&1; then \
		jq '.version = "$(VERSION)"' $(DIST_DIR)/manifest.json > $(DIST_DIR)/manifest.tmp && \
		mv $(DIST_DIR)/manifest.tmp $(DIST_DIR)/manifest.json; \
	else \
		echo "$(YELLOW)⚠️  jq not found, version not updated in manifest$(RESET)"; \
	fi
	@echo "$(GREEN)✅ Production manifest generated!$(RESET)"

manifest: manifest-prod

# Packaging
package: package-extension

package-extension: build-extension manifest-prod
	@echo "$(BLUE)📦 Packaging browser extension...$(RESET)"
	@mkdir -p $(PACKAGE_DIR)
	@# Create ZIP package
	@cd $(DIST_DIR) && zip -r ../$(PACKAGE_DIR)/$(PROJECT_NAME)-v$(VERSION)-$(BUILD_DATE).zip . -x "*.map"
	@# Create CRX package (if chrome binary available)
	@if command -v google-chrome >/dev/null 2>&1 || command -v chromium >/dev/null 2>&1; then \
		echo "Creating CRX package..."; \
		cd $(DIST_DIR) && zip -r ../$(PACKAGE_DIR)/$(PROJECT_NAME)-v$(VERSION)-$(BUILD_DATE).crx . -x "*.map"; \
	else \
		echo "$(YELLOW)⚠️  Chrome/Chromium not found, CRX package not created$(RESET)"; \
	fi
	@echo "$(GREEN)✅ Extension packaged: $(PACKAGE_DIR)/$(PROJECT_NAME)-v$(VERSION)-$(BUILD_DATE).zip$(RESET)"

package-controller: build-controller
	@echo "$(BLUE)📦 Packaging controller...$(RESET)"
	@mkdir -p $(PACKAGE_DIR)
	@if [ -f "controller/Makefile" ]; then \
		cd controller && make package 2>/dev/null || echo "$(YELLOW)⚠️  Controller package failed, creating manual package$(RESET)"; \
		if [ -f "controller/dist/chatgpt-controller.tar.gz" ]; then \
			cp controller/dist/chatgpt-controller.tar.gz $(PACKAGE_DIR)/chatgpt-controller-v$(VERSION)-$(BUILD_DATE).tar.gz; \
			echo "$(GREEN)✅ Controller packaged: $(PACKAGE_DIR)/chatgpt-controller-v$(VERSION)-$(BUILD_DATE).tar.gz$(RESET)"; \
		else \
			echo "$(YELLOW)⚠️  Creating manual controller package$(RESET)"; \
			tar -czf $(PACKAGE_DIR)/chatgpt-controller-v$(VERSION)-$(BUILD_DATE).tar.gz controller/; \
			echo "$(GREEN)✅ Manual controller package created$(RESET)"; \
		fi; \
	else \
		echo "$(YELLOW)⚠️  Controller not found, skipping controller packaging$(RESET)"; \
	fi

package-project: archive-git
	@echo "$(GREEN)✅ Project packaged using git archive!$(RESET)"

package-all: package-extension package-controller package-project
	@echo "$(GREEN)✅ All packages created successfully!$(RESET)"
	@echo "$(BLUE)📦 Package contents:$(RESET)"
	@ls -la $(PACKAGE_DIR)/
	@ls -la $(ARCHIVE_DIR)/

# Archive management
archive-git:
	@echo "$(BLUE)📁 Creating git archive of entire project...$(RESET)"
	@mkdir -p $(ARCHIVE_DIR)
	@if git rev-parse --git-dir > /dev/null 2>&1; then \
		git archive --format=tar.gz --prefix=$(PROJECT_NAME)-$(VERSION)-$(GIT_COMMIT)/ HEAD > $(ARCHIVE_DIR)/$(PROJECT_NAME)-source-$(VERSION)-$(BUILD_DATE).tar.gz; \
		echo "$(GREEN)✅ Git archive created: $(ARCHIVE_DIR)/$(PROJECT_NAME)-source-$(VERSION)-$(BUILD_DATE).tar.gz$(RESET)"; \
	else \
		echo "$(RED)❌ Not a git repository, cannot create git archive$(RESET)"; \
		exit 1; \
	fi

archive-full:
	@echo "$(BLUE)📁 Creating full project archive with dependencies...$(RESET)"
	@mkdir -p $(ARCHIVE_DIR)
	@tar --exclude='node_modules' --exclude='.git' --exclude='$(DIST_DIR)' --exclude='$(PACKAGE_DIR)' --exclude='$(ARCHIVE_DIR)' \
		--exclude='__pycache__' --exclude='*.pyc' --exclude='.venv' \
		-czf $(ARCHIVE_DIR)/$(PROJECT_NAME)-full-$(VERSION)-$(BUILD_DATE).tar.gz .
	@echo "$(GREEN)✅ Full archive created: $(ARCHIVE_DIR)/$(PROJECT_NAME)-full-$(VERSION)-$(BUILD_DATE).tar.gz$(RESET)"

archive: archive-git

# Running
run-controller:
	@echo "$(BLUE)🎮 Starting controller server...$(RESET)"
	@cd controller && make run-server

run-gui:
	@echo "$(BLUE)🎮 Starting controller with GUI...$(RESET)"
	@cd controller && make run-gui

# Cleanup
clean:
	@echo "$(BLUE)🧹 Cleaning build artifacts...$(RESET)"
	@rm -rf $(DIST_DIR)
	@rm -rf $(PACKAGE_DIR)
	@rm -rf node_modules/.cache
	@cd controller && make clean
	@echo "$(GREEN)✅ Cleanup completed!$(RESET)"

clean-all: clean
	@echo "$(BLUE)🧹 Deep cleaning everything...$(RESET)"
	@rm -rf $(ARCHIVE_DIR)
	@rm -rf node_modules
	@cd controller && make clean-all
	@echo "$(GREEN)✅ Deep cleanup completed!$(RESET)"

# Documentation
docs:
	@echo "$(BLUE)📚 Generating documentation...$(RESET)"
	@echo "Project documentation available in README.md"
	@cd controller && make docs
	@echo "$(GREEN)✅ Documentation ready!$(RESET)"

# System information
info:
	@echo "$(BLUE)ℹ️  System Information:$(RESET)"
	@echo "==================="
	@echo "Node.js version: $$(node --version 2>&1 || echo 'Not found')"
	@echo "pnpm version: $$(pnpm --version 2>&1 || echo 'Not found')"
	@echo "Python version: $$(python --version 2>&1 || echo 'Not found')"
	@echo "Git version: $$(git --version 2>&1 || echo 'Not found')"
	@echo ""
	@echo "$(BLUE)📊 Project Information:$(RESET)"
	@echo "==================="
	@echo "Project name: $(PROJECT_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Git commit: $(GIT_COMMIT)"
	@echo "Build date: $(BUILD_DATE)"
	@echo "Current directory: $$(pwd)"
	@echo ""
	@echo "$(BLUE)📁 Directory Status:$(RESET)"
	@echo "==================="
	@echo "Dist directory: $$([ -d $(DIST_DIR) ] && echo 'Found' || echo 'Not found')"
	@echo "Package directory: $$([ -d $(PACKAGE_DIR) ] && echo 'Found' || echo 'Not found')"
	@echo "Archive directory: $$([ -d $(ARCHIVE_DIR) ] && echo 'Found' || echo 'Not found')"
	@echo "Node modules: $$([ -d node_modules ] && echo 'Found' || echo 'Not found')"
	@echo ""

# Advanced manifest management
manifest-update-version:
	@echo "$(BLUE)📋 Updating manifest version to $(VERSION)...$(RESET)"
	@if command -v jq >/dev/null 2>&1; then \
		jq '.version = "$(VERSION)"' public/manifest.json > public/manifest.tmp && \
		mv public/manifest.tmp public/manifest.json; \
		echo "$(GREEN)✅ Manifest version updated to $(VERSION)$(RESET)"; \
	else \
		echo "$(RED)❌ jq not found, please install jq to update manifest version$(RESET)"; \
		exit 1; \
	fi

manifest-validate:
	@echo "$(BLUE)📋 Validating manifest.json...$(RESET)"
	@if command -v jq >/dev/null 2>&1; then \
		jq empty public/manifest.json && echo "$(GREEN)✅ Manifest is valid JSON$(RESET)" || (echo "$(RED)❌ Invalid JSON in manifest$(RESET)" && exit 1); \
	else \
		echo "$(YELLOW)⚠️  jq not found, skipping validation$(RESET)"; \
	fi

# Development utilities
watch:
	@echo "$(BLUE)👀 Starting file watcher for development...$(RESET)"
	@pnpm run watch

serve-dist:
	@echo "$(BLUE)🌐 Serving dist directory for testing...$(RESET)"
	@if command -v python3 >/dev/null 2>&1; then \
		echo "Extension available at http://localhost:8080"; \
		cd $(DIST_DIR) && python3 -m http.server 8080; \
	elif command -v python >/dev/null 2>&1; then \
		echo "Extension available at http://localhost:8080"; \
		cd $(DIST_DIR) && python -m http.server 8080; \
	else \
		echo "$(RED)❌ Python not found, cannot serve files$(RESET)"; \
		exit 1; \
	fi

# Extension installation helpers
install-extension:
	@echo "$(BLUE)🔧 Instructions for installing the extension:$(RESET)"
	@echo "1. Open Chrome and go to chrome://extensions/"
	@echo "2. Enable 'Developer mode' in the top right"
	@echo "3. Click 'Load unpacked' and select the $(DIST_DIR) directory"
	@echo "4. The extension should now be loaded and ready to use"
	@echo ""
	@echo "Extension files are in: $$(pwd)/$(DIST_DIR)"

# Quick development workflow
dev-build: clean install build-extension
	@echo "$(GREEN)✅ Development build completed!$(RESET)"

# Release workflow
release: clean install lint test build-all package-all
	@echo "$(GREEN)🚀 Release workflow completed!$(RESET)"
	@echo "$(BLUE)📦 Release artifacts:$(RESET)"
	@ls -la $(PACKAGE_DIR)/
	@ls -la $(ARCHIVE_DIR)/

# Backup and restore
backup:
	@echo "$(BLUE)💾 Creating backup...$(RESET)"
	@mkdir -p backups
	@tar --exclude='node_modules' --exclude='.git' --exclude='$(DIST_DIR)' --exclude='$(PACKAGE_DIR)' --exclude='$(ARCHIVE_DIR)' --exclude='backups' \
		-czf backups/backup-$(BUILD_DATE).tar.gz .
	@echo "$(GREEN)✅ Backup created: backups/backup-$(BUILD_DATE).tar.gz$(RESET)"

# Security and permissions check
check-permissions:
	@echo "$(BLUE)🔒 Checking file permissions...$(RESET)"
	@find . -type f -name "*.sh" -not -executable -exec echo "$(YELLOW)⚠️  {} is not executable$(RESET)" \;
	@find . -type f -name "Makefile" -executable -exec echo "$(YELLOW)⚠️  {} should not be executable$(RESET)" \;
	@echo "$(GREEN)✅ Permission check completed$(RESET)"

# Git helpers
git-status:
	@echo "$(BLUE)📊 Git repository status:$(RESET)"
	@if git rev-parse --git-dir > /dev/null 2>&1; then \
		echo "Branch: $$(git branch --show-current)"; \
		echo "Commit: $$(git rev-parse HEAD)"; \
		echo "Status:"; \
		git status --porcelain; \
		echo "Recent commits:"; \
		git log --oneline -5; \
	else \
		echo "$(RED)❌ Not a git repository$(RESET)"; \
	fi

# Performance and size analysis
analyze-size:
	@echo "$(BLUE)📊 Analyzing build sizes...$(RESET)"
	@if [ -d "$(DIST_DIR)" ]; then \
		echo "Extension size analysis:"; \
		du -sh $(DIST_DIR); \
		echo "Detailed breakdown:"; \
		du -sh $(DIST_DIR)/* | sort -hr; \
	else \
		echo "$(YELLOW)⚠️  Dist directory not found, run 'make build' first$(RESET)"; \
	fi

# All-in-one commands
all: clean install build-all package-all
	@echo "ChatGPT Forward"

quick: build-extension package-extension
	@echo "$(GREEN)⚡ Quick extension build and package completed!$(RESET)"
