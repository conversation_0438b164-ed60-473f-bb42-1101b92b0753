# ChatGPT Forward Plugin Controller - Makefile
# Common development and deployment tasks

.PHONY: help install run run-server run-gui clean lint format check dev-setup build docs

# Default target
help:
	@echo "ChatGPT Forward Plugin Controller - Available Commands"
	@echo "======================================================"
	@echo ""
	@echo "Development:"
	@echo "  install     - Install dependencies using uv"
	@echo "  dev-setup   - Setup development environment"
	@echo "  lint        - Run linting checks"
	@echo "  format      - Format code with black and isort"
	@echo "  check       - Run all checks (lint, format)"
	@echo ""
	@echo "Running:"
	@echo "  run         - Run controller with GUI"
	@echo "  run-server  - Run controller in server-only mode"
	@echo "  run-gui     - Run controller with GUI (explicit)"
	@echo "  run-debug   - Run controller with debug logging"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean       - Clean up temporary files and caches"
	@echo "  clean-logs  - Clean up log files"
	@echo "  clean-all   - Clean everything (cache, logs, build)"
	@echo ""
	@echo "Documentation:"
	@echo "  docs        - Generate documentation"
	@echo "  docs-serve  - Serve documentation locally"
	@echo ""
	@echo "Deployment:"
	@echo "  build       - Build distribution packages"
	@echo "  package     - Create deployment package"
	@echo ""

# Installation and setup
install:
	@echo "Installing dependencies..."
	uv sync
	@echo "Dependencies installed successfully!"

dev-setup: install
	@echo "Setting up development environment..."
	@if command -v pre-commit >/dev/null 2>&1; then \
		echo "Setting up pre-commit hooks..."; \
		pre-commit install; \
	else \
		echo "pre-commit not found, skipping hooks setup"; \
	fi
	@echo "Development environment ready!"

# Code quality
lint:
	@echo "Running linting checks..."
	@if command -v ruff >/dev/null 2>&1; then \
		echo "Running ruff..."; \
		uv run ruff check chatgpt_controller/; \
	else \
		echo "ruff not found, skipping lint checks"; \
	fi
	@if command -v mypy >/dev/null 2>&1; then \
		echo "Running mypy..."; \
		uv run mypy chatgpt_controller/; \
	else \
		echo "mypy not found, skipping type checks"; \
	fi

format:
	@echo "Formatting code..."
	@if command -v black >/dev/null 2>&1; then \
		echo "Running black..."; \
		uv run black chatgpt_controller/; \
	else \
		echo "black not found, skipping formatting"; \
	fi
	@if command -v isort >/dev/null 2>&1; then \
		echo "Running isort..."; \
		uv run isort chatgpt_controller/; \
	else \
		echo "isort not found, skipping import sorting"; \
	fi

check: lint
	@echo "All checks completed!"

# Running the application
run:
	@echo "Starting ChatGPT Controller"
	uv run python main.py

run-api:
	@echo "Starting ChatGPT Controller API Server"
	uv run python api_server.py --mode combined

run-server:
	@echo "Starting ChatGPT Controller in server-only mode..."
	uv run python main.py --no-gui --auto-start

run-gui:
	@echo "Starting ChatGPT Controller with Rich GUI..."
	uv run python main.py

run-debug:
	@echo "Starting ChatGPT Controller with debug logging..."
	uv run python main.py --debug

run-gui-debug:
	@echo "Starting ChatGPT Controller with Rich GUI and debug logging..."
	uv run python main.py --debug

run-custom:
	@echo "Starting ChatGPT Controller with custom settings..."
	@echo "Usage: make run-custom HOST=0.0.0.0 PORT=8765"
	uv run python main.py --host $(or $(HOST),localhost) --port $(or $(PORT),8765)

# Cleanup
clean:
	@echo "Cleaning up temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name ".coverage" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".ruff_cache" -exec rm -rf {} + 2>/dev/null || true
	@echo "Cleanup completed!"

clean-logs:
	@echo "Cleaning up log files..."
	rm -rf logs/
	@echo "Log files cleaned!"

clean-all: clean clean-logs
	@echo "Deep cleaning..."
	rm -rf dist/
	rm -rf build/
	rm -rf .venv/
	@echo "Deep cleanup completed!"

# Documentation
docs:
	@echo "Generating documentation..."
	@if command -v sphinx-build >/dev/null 2>&1; then \
		sphinx-build -b html docs/ docs/_build/html/; \
		echo "Documentation generated in docs/_build/html/"; \
	else \
		echo "Sphinx not found. Install with: pip install sphinx"; \
	fi

docs-serve:
	@echo "Serving documentation locally..."
	@if [ -d "docs/_build/html" ]; then \
		echo "Documentation available at http://localhost:8000"; \
		cd docs/_build/html && python -m http.server 8000; \
	else \
		echo "Documentation not found. Run 'make docs' first."; \
	fi

# Build and packaging
build:
	@echo "Building distribution packages..."
	uv build
	@echo "Build completed! Check dist/ directory."

package: clean build
	@echo "Creating deployment package..."
	@mkdir -p dist/chatgpt-controller
	@cp -r chatgpt_controller dist/chatgpt-controller/
	@cp main.py dist/chatgpt-controller/
	@cp config.json dist/chatgpt-controller/
	@cp README.md dist/chatgpt-controller/
	@cp pyproject.toml dist/chatgpt-controller/
	@cp start_controller.sh dist/chatgpt-controller/
	@cd dist && tar -czf chatgpt-controller.tar.gz chatgpt-controller/
	@echo "Deployment package created: dist/chatgpt-controller.tar.gz"


# Configuration management
config-validate:
	@echo "Validating configuration..."
	uv run python -c "from chatgpt_controller.core.config import Config; c = Config(); errors = c.validate(); print('✓ Configuration valid' if not errors else f'✗ Errors: {errors}')"

config-show:
	@echo "Current configuration:"
	uv run python -c "from chatgpt_controller.core.config import get_config; import json; print(json.dumps(get_config().to_dict(), indent=2))"

# Server management
server-status:
	@echo "Checking server status..."
	@if lsof -i :8765 >/dev/null 2>&1; then \
		echo "✓ Server is running on port 8765"; \
		lsof -i :8765; \
	else \
		echo "✗ No server running on port 8765"; \
	fi

server-stop:
	@echo "Stopping server..."
	@if lsof -t -i :8765 >/dev/null 2>&1; then \
		kill $$(lsof -t -i :8765); \
		echo "✓ Server stopped"; \
	else \
		echo "✗ No server running on port 8765"; \
	fi

# Monitoring and logs
logs:
	@echo "Showing recent logs..."
	@if [ -f "logs/controller.log" ]; then \
		tail -f logs/controller.log; \
	else \
		echo "No log file found. Start the controller to generate logs."; \
	fi

logs-tail:
	@echo "Tailing logs..."
	@if [ -f "logs/controller.log" ]; then \
		tail -n 50 logs/controller.log; \
	else \
		echo "No log file found."; \
	fi

# Quick development workflow
dev: clean install
	@echo "Development workflow completed!"

# Release workflow
release: clean lint build
	@echo "Release workflow completed!"
	@echo "Ready for deployment!"

# System information
info:
	@echo "System Information:"
	@echo "==================="
	@echo "Python version: $$(python --version 2>&1)"
	@echo "UV version: $$(uv --version 2>&1 || echo 'UV not found')"
	@echo "Current directory: $$(pwd)"
	@echo "Virtual environment: $$(echo $$VIRTUAL_ENV || echo 'None')"
	@echo ""
	@echo "Project Information:"
	@echo "==================="
	@echo "Project root: $$(pwd)"
	@echo "Config file: $$([ -f config.json ] && echo 'Found' || echo 'Not found')"
	@echo "Log directory: $$([ -d logs ] && echo 'Found' || echo 'Not found')"
	@echo ""
