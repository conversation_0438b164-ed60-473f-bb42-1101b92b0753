"""
ChatGPT Forward Plugin Controller

A comprehensive remote controller for the ChatGPT Forward Plugin with WebSocket server,
PyQt5 GUI, real-time debugging capabilities, and Python SDK for programmatic interaction.
"""

__version__ = "1.0.0"
__author__ = "ChatGPT Controller Team"
__description__ = "Remote controller for ChatGPT Forward Plugin with Python SDK"

# Package-level imports for convenience
from .core.config import Config, get_config
from .core.message_types import MessageType
from .server.websocket_server import WebSocketServer
from .utils.logging import setup_logging

# SDK imports
from .sdk.client import ChatGPTControllerSDK

# API imports
try:
    from .api.app import create_app
except ImportError:
    create_app = None

__all__ = [
    "Config",
    "get_config",
    "MessageType",
    "WebSocketServer",
    "setup_logging",
    "ChatGPTControllerSDK",
    "create_app",
    "__version__",
    "__author__",
    "__description__",
]
