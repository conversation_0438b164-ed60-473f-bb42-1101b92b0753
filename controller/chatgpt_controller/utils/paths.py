"""
Path utilities for ChatGPT Controller

Provides platform-specific path handling and directory management.
"""

import os
import sys
from pathlib import Path
from typing import Optional

from .helpers import ensure_directory


def get_app_data_dir() -> Path:
    """Get application data directory"""
    if sys.platform == "win32":
        app_data = Path(os.environ.get("APPDATA", ""))
    elif sys.platform == "darwin":
        app_data = Path.home() / "Library" / "Application Support"
    else:
        app_data = Path.home() / ".local" / "share"

    app_dir = app_data / "ChatGPT-Controller"
    ensure_directory(app_dir)
    return app_dir


def get_logs_dir() -> Path:
    """Get logs directory"""
    logs_dir = get_app_data_dir() / "logs"
    ensure_directory(logs_dir)
    return logs_dir


def get_config_dir() -> Path:
    """Get configuration directory"""
    config_dir = get_app_data_dir() / "config"
    ensure_directory(config_dir)
    return config_dir


def get_cache_dir() -> Path:
    """Get cache directory"""
    if sys.platform == "win32":
        cache_dir = (
            Path(os.environ.get("LOCALAPPDATA", "")) / "ChatGPT-Controller" / "cache"
        )
    elif sys.platform == "darwin":
        cache_dir = Path.home() / "Library" / "Caches" / "ChatGPT-Controller"
    else:
        cache_dir = Path.home() / ".cache" / "chatgpt-controller"

    ensure_directory(cache_dir)
    return cache_dir


def get_temp_dir() -> Path:
    """Get temporary directory"""
    import tempfile

    temp_dir = Path(tempfile.gettempdir()) / "chatgpt-controller"
    ensure_directory(temp_dir)
    return temp_dir


def get_user_documents_dir() -> Path:
    """Get user documents directory"""
    if sys.platform == "win32":
        import winreg

        try:
            with winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders",
            ) as key:
                documents_dir = Path(winreg.QueryValueEx(key, "Personal")[0])
        except:
            documents_dir = Path.home() / "Documents"
    elif sys.platform == "darwin":
        documents_dir = Path.home() / "Documents"
    else:
        # Try XDG user dirs first
        try:
            import subprocess

            result = subprocess.run(
                ["xdg-user-dir", "DOCUMENTS"], capture_output=True, text=True
            )
            if result.returncode == 0:
                documents_dir = Path(result.stdout.strip())
            else:
                documents_dir = Path.home() / "Documents"
        except:
            documents_dir = Path.home() / "Documents"

    return documents_dir


def get_desktop_dir() -> Path:
    """Get desktop directory"""
    if sys.platform == "win32":
        desktop_dir = Path.home() / "Desktop"
    elif sys.platform == "darwin":
        desktop_dir = Path.home() / "Desktop"
    else:
        # Try XDG user dirs first
        try:
            import subprocess

            result = subprocess.run(
                ["xdg-user-dir", "DESKTOP"], capture_output=True, text=True
            )
            if result.returncode == 0:
                desktop_dir = Path(result.stdout.strip())
            else:
                desktop_dir = Path.home() / "Desktop"
        except:
            desktop_dir = Path.home() / "Desktop"

    return desktop_dir


def get_project_root() -> Path:
    """Get project root directory"""
    # Start from current file and go up until we find pyproject.toml
    current = Path(__file__).parent
    while current != current.parent:
        if (current / "pyproject.toml").exists():
            return current
        current = current.parent

    # Fallback to current working directory
    return Path.cwd()


def get_relative_path(path: Path, base: Optional[Path] = None) -> Path:
    """Get relative path from base directory"""
    if base is None:
        base = Path.cwd()

    try:
        return path.relative_to(base)
    except ValueError:
        # If path is not relative to base, return absolute path
        return path.resolve()


def normalize_path(path: str) -> Path:
    """Normalize path string to Path object"""
    path_obj = Path(path)

    # Expand user directory
    if str(path_obj).startswith("~"):
        path_obj = path_obj.expanduser()

    # Resolve relative paths
    if not path_obj.is_absolute():
        path_obj = Path.cwd() / path_obj

    return path_obj.resolve()


def safe_path_join(*parts) -> Path:
    """Safely join path parts, preventing directory traversal"""
    result = Path()

    for part in parts:
        part_str = str(part)
        # Remove dangerous path components
        if part_str in ("..", ".", ""):
            continue
        if part_str.startswith("/") or (len(part_str) > 1 and part_str[1] == ":"):
            # Absolute path - only use the filename
            part_str = Path(part_str).name

        result = result / part_str

    return result


def is_safe_path(path: Path, base_dir: Path) -> bool:
    """Check if path is safe (within base directory)"""
    try:
        resolved_path = path.resolve()
        resolved_base = base_dir.resolve()

        # Check if path is within base directory
        resolved_path.relative_to(resolved_base)
        return True
    except ValueError:
        return False


def find_files(directory: Path, pattern: str = "*", recursive: bool = True) -> list:
    """Find files matching pattern in directory"""
    if recursive:
        return list(directory.rglob(pattern))
    else:
        return list(directory.glob(pattern))


def get_file_info(file_path: Path) -> dict:
    """Get file information"""
    if not file_path.exists():
        return {}

    stat = file_path.stat()

    return {
        "name": file_path.name,
        "path": str(file_path),
        "size": stat.st_size,
        "modified": stat.st_mtime,
        "created": stat.st_ctime,
        "is_file": file_path.is_file(),
        "is_dir": file_path.is_dir(),
        "permissions": oct(stat.st_mode)[-3:],
    }


def cleanup_temp_files(max_age_hours: int = 24):
    """Clean up old temporary files"""
    import time

    temp_dir = get_temp_dir()
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600

    cleaned_count = 0

    for file_path in temp_dir.rglob("*"):
        if file_path.is_file():
            try:
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    file_path.unlink()
                    cleaned_count += 1
            except (OSError, FileNotFoundError):
                pass  # File might have been deleted already

    return cleaned_count


def backup_file(
    file_path: Path, backup_dir: Optional[Path] = None, max_backups: int = 5
) -> Optional[Path]:
    """Create backup of file"""
    if not file_path.exists():
        return None

    if backup_dir is None:
        backup_dir = file_path.parent / "backups"

    ensure_directory(backup_dir)

    # Generate backup filename with timestamp
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
    backup_path = backup_dir / backup_name

    # Copy file
    import shutil

    shutil.copy2(file_path, backup_path)

    # Clean up old backups
    backups = sorted(backup_dir.glob(f"{file_path.stem}_*{file_path.suffix}"))
    while len(backups) > max_backups:
        oldest_backup = backups.pop(0)
        try:
            oldest_backup.unlink()
        except OSError:
            pass

    return backup_path


class PathManager:
    """Centralized path management"""

    def __init__(self):
        self._paths = {}
        self._setup_default_paths()

    def _setup_default_paths(self):
        """Setup default application paths"""
        self._paths.update(
            {
                "app_data": get_app_data_dir(),
                "logs": get_logs_dir(),
                "config": get_config_dir(),
                "cache": get_cache_dir(),
                "temp": get_temp_dir(),
                "project_root": get_project_root(),
            }
        )

    def get_path(self, name: str) -> Optional[Path]:
        """Get path by name"""
        return self._paths.get(name)

    def set_path(self, name: str, path: Path):
        """Set custom path"""
        self._paths[name] = Path(path)

    def get_all_paths(self) -> dict:
        """Get all managed paths"""
        return self._paths.copy()


# Global path manager instance
_path_manager = PathManager()


def get_path_manager() -> PathManager:
    """Get global path manager instance"""
    return _path_manager
