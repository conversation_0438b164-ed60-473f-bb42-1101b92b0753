"""
Rich-based logging utilities for ChatGPT Controller

Provides centralized logging configuration with rich formatting and utilities.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Union, Optional

from rich.console import Console
from rich.logging import RichHandler
from rich.traceback import install as install_rich_traceback
from rich.theme import Theme

from ..core.config import get_config
from .paths import get_logs_dir


# Custom theme for rich console
CHATGPT_THEME = Theme(
    {
        "info": "cyan",
        "warning": "yellow",
        "error": "bold red",
        "debug": "dim cyan",
        "success": "bold green",
        "server": "bold blue",
        "client": "magenta",
        "message": "green",
        "command": "bold yellow",
    }
)

# Global console instance
console = Console(theme=CHATGPT_THEME)


def setup_logging(
    level: Union[str, int] = None,
    log_file: Optional[Path] = None,
    console_output: bool = None,
    rich_console: bool = True,
):
    """Setup rich-based logging configuration"""

    # Install rich traceback handler
    install_rich_traceback(show_locals=True)

    config = get_config()
    log_config = config.logging

    # Use provided values or fall back to config
    if level is None:
        level = getattr(logging, log_config.level.upper(), logging.INFO)
    elif isinstance(level, str):
        level = getattr(logging, level.upper(), logging.INFO)

    if log_file is None:
        log_file = get_logs_dir() / log_config.file.split("/")[-1]

    if console_output is None:
        console_output = log_config.console_output

    log_format = log_config.format
    max_size = log_config.max_size
    backup_count = log_config.backup_count

    # Create logs directory
    log_file.parent.mkdir(parents=True, exist_ok=True)

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Rich console handler
    if console_output and rich_console:
        rich_handler = RichHandler(
            console=console,
            show_time=True,
            show_level=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True,
            tracebacks_show_locals=True,
        )
        rich_handler.setLevel(level)
        root_logger.addHandler(rich_handler)
    elif console_output:
        # Fallback to standard console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_formatter = logging.Formatter(log_format)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

    # File handler
    try:
        # Parse max size
        if isinstance(max_size, str):
            if max_size.upper().endswith("MB"):
                max_bytes = int(max_size[:-2]) * 1024 * 1024
            elif max_size.upper().endswith("KB"):
                max_bytes = int(max_size[:-2]) * 1024
            elif max_size.upper().endswith("GB"):
                max_bytes = int(max_size[:-2]) * 1024 * 1024 * 1024
            else:
                max_bytes = int(max_size)
        else:
            max_bytes = max_size

        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8"
        )
        file_handler.setLevel(level)
        file_formatter = logging.Formatter(log_format)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)

        logging.info(
            f"Logging configured - Level: {logging.getLevelName(level)}, File: {log_file}"
        )

    except Exception as e:
        logging.error(f"Failed to setup file logging: {e}")


def get_logger(name: str) -> logging.Logger:
    """Get logger with specified name"""
    return logging.getLogger(name)


def get_console() -> Console:
    """Get the global rich console instance"""
    return console


class RichLogger:
    """Rich-enhanced logger with styled output"""

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.console = console

    def info(self, message: str, **kwargs):
        """Log info message with rich formatting"""
        self.logger.info(message, **kwargs)

    def success(self, message: str):
        """Log success message with green styling"""
        self.logger.info(message)

    def warning(self, message: str):
        """Log warning message with yellow styling"""
        self.logger.warning(message)

    def error(self, message: str):
        """Log error message with red styling"""
        self.logger.error(message)

    def debug(self, message: str):
        """Log debug message with dim styling"""
        self.logger.debug(message)

    def server_event(self, message: str):
        """Log server event with server styling"""
        self.console.print(f"🖥️  {message}", style="server")
        self.logger.info(message)

    def client_event(self, message: str):
        """Log client event with client styling"""
        self.console.print(f"👤 {message}", style="client")
        self.logger.info(message)

    def message_event(self, message: str):
        """Log message event with message styling"""
        self.console.print(f"💬 {message}", style="message")
        self.logger.info(message)

    def command_event(self, message: str):
        """Log command event with command styling"""
        self.console.print(f"⚡ {message}", style="command")
        self.logger.info(message)


class ColoredFormatter(logging.Formatter):
    """Colored console formatter"""

    # Color codes
    COLORS = {
        "DEBUG": "\033[36m",  # Cyan
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[35m",  # Magenta
        "RESET": "\033[0m",  # Reset
    }

    def format(self, record):
        # Add color to level name
        level_color = self.COLORS.get(record.levelname, self.COLORS["RESET"])
        record.levelname = f"{level_color}{record.levelname}{self.COLORS['RESET']}"

        return super().format(record)


def setup_colored_logging(level: Union[str, int] = logging.INFO):
    """Setup colored console logging"""
    if isinstance(level, str):
        level = getattr(logging, level.upper(), logging.INFO)

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Colored console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)

    colored_formatter = ColoredFormatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    console_handler.setFormatter(colored_formatter)
    root_logger.addHandler(console_handler)


class ContextFilter(logging.Filter):
    """Filter to add context information to log records"""

    def __init__(self, context: dict):
        super().__init__()
        self.context = context

    def filter(self, record):
        for key, value in self.context.items():
            setattr(record, key, value)
        return True


class LogCapture:
    """Utility to capture log messages for testing or debugging"""

    def __init__(self, logger_name: str = None, level: int = logging.DEBUG):
        self.logger_name = logger_name
        self.level = level
        self.records = []
        self.handler = None

    def __enter__(self):
        self.handler = logging.Handler()
        self.handler.setLevel(self.level)
        self.handler.emit = self._capture_record

        if self.logger_name:
            logger = logging.getLogger(self.logger_name)
        else:
            logger = logging.getLogger()

        logger.addHandler(self.handler)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.handler:
            if self.logger_name:
                logger = logging.getLogger(self.logger_name)
            else:
                logger = logging.getLogger()

            logger.removeHandler(self.handler)

    def _capture_record(self, record):
        self.records.append(record)

    def get_messages(self, level: int = None) -> list:
        """Get captured messages"""
        if level is None:
            return [record.getMessage() for record in self.records]
        else:
            return [
                record.getMessage()
                for record in self.records
                if record.levelno >= level
            ]

    def clear(self):
        """Clear captured records"""
        self.records.clear()


def log_function_call(func):
    """Decorator to log function calls"""

    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")

        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} returned: {result}")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} raised {type(e).__name__}: {e}")
            raise

    return wrapper


def log_execution_time(func):
    """Decorator to log function execution time"""
    import time

    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} executed in {execution_time:.4f} seconds")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"{func.__name__} failed after {execution_time:.4f} seconds: {e}"
            )
            raise

    return wrapper


class LoggerMixin:
    """Mixin class to add logging capabilities to any class"""

    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class"""
        if not hasattr(self, "_logger"):
            self._logger = logging.getLogger(
                f"{self.__class__.__module__}.{self.__class__.__name__}"
            )
        return self._logger

    def log_debug(self, message: str, *args, **kwargs):
        """Log debug message"""
        self.logger.debug(message, *args, **kwargs)

    def log_info(self, message: str, *args, **kwargs):
        """Log info message"""
        self.logger.info(message, *args, **kwargs)

    def log_warning(self, message: str, *args, **kwargs):
        """Log warning message"""
        self.logger.warning(message, *args, **kwargs)

    def log_error(self, message: str, *args, **kwargs):
        """Log error message"""
        self.logger.error(message, *args, **kwargs)

    def log_critical(self, message: str, *args, **kwargs):
        """Log critical message"""
        self.logger.critical(message, *args, **kwargs)

    def log_exception(self, message: str, *args, **kwargs):
        """Log exception with traceback"""
        self.logger.exception(message, *args, **kwargs)
