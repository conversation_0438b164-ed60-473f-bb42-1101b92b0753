"""
Helper utilities for ChatGPT Controller

Common utility functions used throughout the application.
"""

import re
import json
from datetime import datetime
from typing import Any, Dict, Optional, Union
from pathlib import Path


def format_timestamp(
    timestamp: datetime = None, format_str: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """Format timestamp for display"""
    if timestamp is None:
        timestamp = datetime.now()
    return timestamp.strftime(format_str)


def format_timestamp_iso(timestamp: datetime = None) -> str:
    """Format timestamp in ISO format"""
    if timestamp is None:
        timestamp = datetime.now()
    return timestamp.isoformat()


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
    i = 0
    size = float(size_bytes)

    while size >= 1024 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1

    return f"{size:.1f} {size_names[i]}"


def truncate_string(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate string to maximum length"""
    if len(text) <= max_length:
        return text
    return text[: max_length - len(suffix)] + suffix


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations"""
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', "_", filename)
    # Remove control characters
    filename = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", filename)
    # Remove leading/trailing spaces and dots
    filename = filename.strip(" .")
    # Limit length
    if len(filename) > 255:
        filename = filename[:255]
    # Ensure not empty
    if not filename:
        filename = "unnamed"
    return filename


def ensure_directory(path: Path):
    """Ensure directory exists"""
    path.mkdir(parents=True, exist_ok=True)


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely parse JSON string"""
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "null", **kwargs) -> str:
    """Safely serialize object to JSON"""
    try:
        return json.dumps(obj, **kwargs)
    except (TypeError, ValueError):
        return default


def deep_merge_dicts(base: Dict, override: Dict) -> Dict:
    """Deep merge two dictionaries"""
    result = base.copy()

    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value

    return result


def flatten_dict(d: Dict, parent_key: str = "", sep: str = ".") -> Dict:
    """Flatten nested dictionary"""
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)


def unflatten_dict(d: Dict, sep: str = ".") -> Dict:
    """Unflatten dictionary with dot notation keys"""
    result = {}
    for key, value in d.items():
        keys = key.split(sep)
        current = result
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        current[keys[-1]] = value
    return result


def validate_email(email: str) -> bool:
    """Validate email address format"""
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return re.match(pattern, email) is not None


def validate_url(url: str) -> bool:
    """Validate URL format"""
    pattern = r"^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$"
    return re.match(pattern, url) is not None


def extract_domain(url: str) -> Optional[str]:
    """Extract domain from URL"""
    pattern = r"https?://(?:www\.)?([^/]+)"
    match = re.match(pattern, url)
    return match.group(1) if match else None


def generate_id(prefix: str = "", length: int = 8) -> str:
    """Generate random ID"""
    import uuid

    random_part = str(uuid.uuid4()).replace("-", "")[:length]
    return f"{prefix}{random_part}" if prefix else random_part


def retry_on_exception(
    max_retries: int = 3, delay: float = 1.0, exceptions: tuple = (Exception,)
):
    """Decorator to retry function on exception"""
    import time

    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (2**attempt))  # Exponential backoff
                    else:
                        raise last_exception

            return None

        return wrapper

    return decorator


def timeout_after(seconds: float):
    """Decorator to timeout function after specified seconds"""
    import signal

    def decorator(func):
        def wrapper(*args, **kwargs):
            def timeout_handler(signum, frame):
                raise TimeoutError(
                    f"Function {func.__name__} timed out after {seconds} seconds"
                )

            # Set timeout
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(seconds))

            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # Reset alarm
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)

        return wrapper

    return decorator


def memoize(func):
    """Memoization decorator"""
    cache = {}

    def wrapper(*args, **kwargs):
        # Create cache key
        key = str(args) + str(sorted(kwargs.items()))

        if key not in cache:
            cache[key] = func(*args, **kwargs)

        return cache[key]

    wrapper.cache = cache
    wrapper.clear_cache = lambda: cache.clear()
    return wrapper


def singleton(cls):
    """Singleton decorator"""
    instances = {}

    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance


class Timer:
    """Context manager for timing operations"""

    def __init__(self, description: str = "Operation"):
        self.description = description
        self.start_time = None
        self.end_time = None

    def __enter__(self):
        import time

        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        import time

        self.end_time = time.time()

    @property
    def elapsed(self) -> float:
        """Get elapsed time in seconds"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0

    def __str__(self):
        return f"{self.description}: {self.elapsed:.4f} seconds"


class RateLimiter:
    """Simple rate limiter"""

    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []

    def is_allowed(self) -> bool:
        """Check if call is allowed"""
        import time

        now = time.time()

        # Remove old calls
        self.calls = [
            call_time for call_time in self.calls if now - call_time < self.time_window
        ]

        # Check if we can make another call
        if len(self.calls) < self.max_calls:
            self.calls.append(now)
            return True

        return False

    def wait_time(self) -> float:
        """Get time to wait before next call is allowed"""
        if not self.calls:
            return 0.0

        import time

        oldest_call = min(self.calls)
        return max(0.0, self.time_window - (time.time() - oldest_call))


def debounce(wait_time: float):
    """Debounce decorator - only execute after wait_time of no calls"""
    import threading

    def decorator(func):
        timer = None

        def wrapper(*args, **kwargs):
            nonlocal timer

            def call_func():
                func(*args, **kwargs)

            if timer:
                timer.cancel()

            timer = threading.Timer(wait_time, call_func)
            timer.start()

        return wrapper

    return decorator


def throttle(min_interval: float):
    """Throttle decorator - limit function calls to minimum interval"""
    import time

    def decorator(func):
        last_called = [0.0]

        def wrapper(*args, **kwargs):
            now = time.time()
            if now - last_called[0] >= min_interval:
                last_called[0] = now
                return func(*args, **kwargs)

        return wrapper

    return decorator
