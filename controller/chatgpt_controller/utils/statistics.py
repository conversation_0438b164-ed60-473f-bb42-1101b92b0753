"""
Statistics utilities for ChatGPT Controller

Provides statistics tracking and reporting functionality.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from collections import defaultdict, deque
import threading


class MessageStats:
    """Message statistics tracking"""

    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self.start_time = datetime.now()
        self.lock = threading.Lock()

        # Basic counters
        self.total_messages = 0
        self.total_errors = 0
        self.messages_by_type = defaultdict(int)
        self.messages_by_client = defaultdict(int)
        self.errors_by_type = defaultdict(int)

        # Time-based tracking
        self.messages_by_hour = defaultdict(int)
        self.messages_by_day = defaultdict(int)

        # Recent activity (for rate calculations)
        self.recent_messages = deque(maxlen=1000)

        # Performance metrics
        self.processing_times = deque(maxlen=1000)
        self.message_sizes = deque(maxlen=1000)

    def record_message(
        self,
        message_type: str,
        client_id: str = "unknown",
        is_valid: bool = True,
        processing_time: float = 0.0,
        message_size: int = 0,
    ):
        """Record a message"""
        with self.lock:
            now = datetime.now()

            # Basic counters
            self.total_messages += 1
            self.messages_by_type[message_type] += 1
            self.messages_by_client[client_id] += 1

            # Time-based tracking
            hour_key = now.strftime("%Y-%m-%d %H:00")
            day_key = now.strftime("%Y-%m-%d")
            self.messages_by_hour[hour_key] += 1
            self.messages_by_day[day_key] += 1

            # Recent activity
            self.recent_messages.append(
                {
                    "timestamp": now,
                    "type": message_type,
                    "client_id": client_id,
                    "valid": is_valid,
                }
            )

            # Performance metrics
            if processing_time > 0:
                self.processing_times.append(processing_time)

            if message_size > 0:
                self.message_sizes.append(message_size)

            # Error tracking
            if not is_valid:
                self.total_errors += 1

    def record_error(self, error_type: str, client_id: str = "unknown"):
        """Record an error"""
        with self.lock:
            self.total_errors += 1
            self.errors_by_type[error_type] += 1

    def get_summary(self) -> Dict[str, Any]:
        """Get statistics summary"""
        with self.lock:
            now = datetime.now()
            uptime = now - self.start_time
            uptime_seconds = uptime.total_seconds()

            # Calculate rates
            messages_per_second = self.total_messages / max(uptime_seconds, 1)
            messages_per_minute = messages_per_second * 60
            messages_per_hour = messages_per_minute * 60

            # Recent activity (last 5 minutes)
            five_minutes_ago = now - timedelta(minutes=5)
            recent_count = sum(
                1 for msg in self.recent_messages if msg["timestamp"] > five_minutes_ago
            )
            recent_rate = recent_count / 5.0  # per minute

            # Performance metrics
            avg_processing_time = (
                sum(self.processing_times) / len(self.processing_times)
                if self.processing_times
                else 0
            )
            avg_message_size = (
                sum(self.message_sizes) / len(self.message_sizes)
                if self.message_sizes
                else 0
            )

            return {
                "uptime_seconds": uptime_seconds,
                "total_messages": self.total_messages,
                "total_errors": self.total_errors,
                "error_rate": self.total_errors / max(self.total_messages, 1),
                "messages_per_second": messages_per_second,
                "messages_per_minute": messages_per_minute,
                "messages_per_hour": messages_per_hour,
                "recent_rate_per_minute": recent_rate,
                "avg_processing_time": avg_processing_time,
                "avg_message_size": avg_message_size,
                "messages_by_type": dict(self.messages_by_type),
                "messages_by_client": dict(self.messages_by_client),
                "errors_by_type": dict(self.errors_by_type),
            }

    def get_hourly_stats(self, hours: int = 24) -> Dict[str, int]:
        """Get hourly message statistics"""
        with self.lock:
            now = datetime.now()
            stats = {}

            for i in range(hours):
                hour = now - timedelta(hours=i)
                hour_key = hour.strftime("%Y-%m-%d %H:00")
                stats[hour_key] = self.messages_by_hour.get(hour_key, 0)

            return stats

    def get_daily_stats(self, days: int = 7) -> Dict[str, int]:
        """Get daily message statistics"""
        with self.lock:
            now = datetime.now()
            stats = {}

            for i in range(days):
                day = now - timedelta(days=i)
                day_key = day.strftime("%Y-%m-%d")
                stats[day_key] = self.messages_by_day.get(day_key, 0)

            return stats

    def get_top_clients(self, limit: int = 10) -> List[tuple]:
        """Get top clients by message count"""
        with self.lock:
            return sorted(
                self.messages_by_client.items(), key=lambda x: x[1], reverse=True
            )[:limit]

    def get_top_message_types(self, limit: int = 10) -> List[tuple]:
        """Get top message types by count"""
        with self.lock:
            return sorted(
                self.messages_by_type.items(), key=lambda x: x[1], reverse=True
            )[:limit]

    def reset(self):
        """Reset all statistics"""
        with self.lock:
            self.start_time = datetime.now()
            self.total_messages = 0
            self.total_errors = 0
            self.messages_by_type.clear()
            self.messages_by_client.clear()
            self.errors_by_type.clear()
            self.messages_by_hour.clear()
            self.messages_by_day.clear()
            self.recent_messages.clear()
            self.processing_times.clear()
            self.message_sizes.clear()

    def export_data(self) -> Dict[str, Any]:
        """Export all statistics data"""
        with self.lock:
            return {
                "start_time": self.start_time.isoformat(),
                "total_messages": self.total_messages,
                "total_errors": self.total_errors,
                "messages_by_type": dict(self.messages_by_type),
                "messages_by_client": dict(self.messages_by_client),
                "errors_by_type": dict(self.errors_by_type),
                "messages_by_hour": dict(self.messages_by_hour),
                "messages_by_day": dict(self.messages_by_day),
                "recent_messages": [
                    {
                        "timestamp": msg["timestamp"].isoformat(),
                        "type": msg["type"],
                        "client_id": msg["client_id"],
                        "valid": msg["valid"],
                    }
                    for msg in list(self.recent_messages)
                ],
                "processing_times": list(self.processing_times),
                "message_sizes": list(self.message_sizes),
            }


class PerformanceMonitor:
    """Performance monitoring utility"""

    def __init__(self):
        self.metrics = defaultdict(list)
        self.lock = threading.Lock()

    def record_metric(self, name: str, value: float, timestamp: datetime = None):
        """Record a performance metric"""
        if timestamp is None:
            timestamp = datetime.now()

        with self.lock:
            self.metrics[name].append({"value": value, "timestamp": timestamp})

            # Keep only recent metrics (last 1000 entries)
            if len(self.metrics[name]) > 1000:
                self.metrics[name] = self.metrics[name][-1000:]

    def get_metric_stats(self, name: str, minutes: int = 60) -> Dict[str, float]:
        """Get statistics for a metric over time period"""
        with self.lock:
            if name not in self.metrics:
                return {}

            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_values = [
                entry["value"]
                for entry in self.metrics[name]
                if entry["timestamp"] > cutoff_time
            ]

            if not recent_values:
                return {}

            return {
                "count": len(recent_values),
                "min": min(recent_values),
                "max": max(recent_values),
                "avg": sum(recent_values) / len(recent_values),
                "latest": recent_values[-1] if recent_values else 0,
            }

    def get_all_metrics(self) -> Dict[str, Dict[str, float]]:
        """Get statistics for all metrics"""
        with self.lock:
            return {name: self.get_metric_stats(name) for name in self.metrics.keys()}


class SystemStats:
    """System resource statistics"""

    @staticmethod
    def get_memory_usage() -> Dict[str, float]:
        """Get memory usage statistics"""
        try:
            import psutil

            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                "rss": memory_info.rss,  # Resident Set Size
                "vms": memory_info.vms,  # Virtual Memory Size
                "percent": process.memory_percent(),
                "available": psutil.virtual_memory().available,
            }
        except ImportError:
            return {}

    @staticmethod
    def get_cpu_usage() -> Dict[str, float]:
        """Get CPU usage statistics"""
        try:
            import psutil

            process = psutil.Process()

            return {
                "percent": process.cpu_percent(),
                "system_percent": psutil.cpu_percent(),
                "num_threads": process.num_threads(),
            }
        except ImportError:
            return {}

    @staticmethod
    def get_network_stats() -> Dict[str, int]:
        """Get network statistics"""
        try:
            import psutil

            net_io = psutil.net_io_counters()

            return {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv,
            }
        except ImportError:
            return {}


# Global statistics instances
_message_stats: Optional[MessageStats] = None
_performance_monitor: Optional[PerformanceMonitor] = None


def get_stats() -> MessageStats:
    """Get global message statistics instance"""
    global _message_stats
    if _message_stats is None:
        _message_stats = MessageStats()
    return _message_stats


def get_performance_monitor() -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def reset_stats():
    """Reset all global statistics"""
    global _message_stats, _performance_monitor
    if _message_stats:
        _message_stats.reset()
    if _performance_monitor:
        _performance_monitor.metrics.clear()
