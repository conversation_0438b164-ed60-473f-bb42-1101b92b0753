"""
SDK Exception Classes

Custom exceptions for the ChatGPT Controller SDK.
"""


class SDKError(Exception):
    """Base exception for all SDK errors"""
    pass


class ConnectionError(SDKError):
    """Raised when WebSocket connection fails or is lost"""
    pass


class TimeoutError(SDKError):
    """Raised when operations timeout"""
    pass


class CommandError(SDKError):
    """Raised when command execution fails"""
    pass


class ResponseError(SDKError):
    """Raised when response parsing or validation fails"""
    pass


class AuthenticationError(SDKError):
    """Raised when authentication fails"""
    pass


class ValidationError(SDKError):
    """Raised when input validation fails"""
    pass
