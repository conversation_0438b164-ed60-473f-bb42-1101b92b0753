"""
ChatGPT Controller SDK Client

Main SDK client class for connecting to and interacting with the ChatGPT Forward Plugin.
"""

import asyncio
import json
import logging
import uuid
import websockets
from typing import Dict, Any, Optional, Callable, List, Union
from datetime import datetime, timedelta
from contextlib import asynccontextmanager

from .exceptions import (
    SDKError,
    ConnectionError,
    TimeoutError,
    CommandError,
    ResponseError,
)
from .types import (
    ConnectionState,
    ElementSelector,
    ElementAction,
    BrowserControlCommand,
    ElementQueryCommand,
    CommandResponse,
    ElementInfo,
    PageInfo,
    SelectorType,
    ActionType,
    ConnectionInfo,
    SDKConfig,
    ElementQueryResult,
    BrowserControlResult,
    ScreenshotResult,
)
from .logging import get_sdk_logger, SDKLogger
from ..core.message_types import MessageType


class ChatGPTControllerSDK:
    """
    Python SDK for ChatGPT Forward Plugin Controller
    
    Provides a clean, well-documented API for programmatic interaction with the browser extension.
    Supports connection management, browser control, element queries, and response handling.
    
    Example:
        ```python
        async with ChatGPTControllerSDK("ws://localhost:8765") as sdk:
            # Open ChatGPT
            await sdk.open_chatgpt()
            
            # Find and click a button
            elements = await sdk.find_element_by_css("#submit-button")
            if elements:
                await sdk.click_element_by_id("submit-button")
            
            # Input text
            await sdk.input_text_by_id("message-input", "Hello, world!")
        ```
    """
    
    def __init__(
        self,
        url: str = "ws://localhost:8765",
        timeout: float = 30.0,
        auto_reconnect: bool = True,
        max_reconnect_attempts: int = 5,
        logger: Optional[SDKLogger] = None,
    ):
        """
        Initialize the SDK client

        Args:
            url: WebSocket server URL
            timeout: Default timeout for operations in seconds
            auto_reconnect: Whether to automatically reconnect on connection loss
            max_reconnect_attempts: Maximum number of reconnection attempts
            logger: Custom Rich logger instance
        """
        # Validate configuration using Pydantic
        config = SDKConfig(
            url=url,
            timeout=timeout,
            auto_reconnect=auto_reconnect,
            max_reconnect_attempts=max_reconnect_attempts
        )

        self.url = config.url
        self.timeout = config.timeout
        self.auto_reconnect = config.auto_reconnect
        self.max_reconnect_attempts = config.max_reconnect_attempts

        # Setup Rich logging
        self.logger = logger or get_sdk_logger("client")

        # Connection state
        self._websocket: Optional[websockets.WebSocketServerProtocol] = None
        self._connection_state = ConnectionState.DISCONNECTED
        self._reconnect_attempts = 0

        # Message handling
        self._pending_responses: Dict[str, asyncio.Future] = {}
        self._message_handlers: Dict[str, List[Callable]] = {}
        self._listen_task: Optional[asyncio.Task] = None

        # Callbacks
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        self.on_message: Optional[Callable] = None
    
    @property
    def is_connected(self) -> bool:
        """Check if the client is connected"""
        return (
            self._connection_state == ConnectionState.CONNECTED
            and self._websocket is not None
            and self._websocket.state.name == "OPEN"
        )
    
    @property
    def connection_state(self) -> ConnectionState:
        """Get current connection state"""
        return self._connection_state
    
    async def connect(self) -> None:
        """
        Connect to the WebSocket server
        
        Raises:
            ConnectionError: If connection fails
        """
        if self.is_connected:
            self.logger.warning("Already connected")
            return
        
        self._connection_state = ConnectionState.CONNECTING
        self.logger.connection_event(f"Connecting to [cyan]{self.url}[/cyan]")

        try:
            self._websocket = await asyncio.wait_for(
                websockets.connect(self.url),
                timeout=self.timeout
            )

            self._connection_state = ConnectionState.CONNECTED
            self._reconnect_attempts = 0

            # Start message listener
            self._listen_task = asyncio.create_task(self._listen_for_messages())

            # Send initialization message
            await self._send_init_message()

            self.logger.success(f"Connected to [cyan]{self.url}[/cyan]")

            if self.on_connected:
                try:
                    await self._call_callback(self.on_connected)
                except Exception as e:
                    self.logger.error(f"Error in connected callback: {e}")

        except asyncio.TimeoutError:
            self._connection_state = ConnectionState.ERROR
            raise ConnectionError(f"Connection timeout after {self.timeout}s")
        except Exception as e:
            self._connection_state = ConnectionState.ERROR
            raise ConnectionError(f"Failed to connect: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from the WebSocket server"""
        if not self.is_connected:
            return
        
        self.logger.info("Disconnecting")
        
        # Cancel message listener
        if self._listen_task:
            self._listen_task.cancel()
            try:
                await self._listen_task
            except asyncio.CancelledError:
                pass
        
        # Close WebSocket
        if self._websocket:
            await self._websocket.close()
        
        self._connection_state = ConnectionState.DISCONNECTED
        self._websocket = None
        
        # Cancel pending responses
        for future in self._pending_responses.values():
            if not future.done():
                future.cancel()
        self._pending_responses.clear()
        
        self.logger.info("Disconnected")
        
        if self.on_disconnected:
            try:
                await self._call_callback(self.on_disconnected)
            except Exception as e:
                self.logger.error(f"Error in disconnected callback: {e}")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.disconnect()
    
    def add_message_handler(self, message_type: str, handler: Callable) -> None:
        """
        Add a message handler for specific message types
        
        Args:
            message_type: Type of message to handle
            handler: Callback function to handle the message
        """
        if message_type not in self._message_handlers:
            self._message_handlers[message_type] = []
        self._message_handlers[message_type].append(handler)
    
    def remove_message_handler(self, message_type: str, handler: Callable) -> None:
        """Remove a message handler"""
        if message_type in self._message_handlers:
            try:
                self._message_handlers[message_type].remove(handler)
            except ValueError:
                pass

    async def send_command(
        self,
        command_type: str,
        data: Optional[Dict[str, Any]] = None,
        timeout: Optional[float] = None,
        wait_for_response: bool = True,
    ) -> Optional[CommandResponse]:
        """
        Send a command to the browser extension

        Args:
            command_type: Type of command to send
            data: Command data
            timeout: Timeout for response (uses default if None)
            wait_for_response: Whether to wait for a response

        Returns:
            Command response if wait_for_response is True, None otherwise

        Raises:
            ConnectionError: If not connected
            TimeoutError: If response timeout
            CommandError: If command execution fails
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to server")

        message_id = str(uuid.uuid4())
        message = {
            "type": command_type,
            "id": message_id,
            "timestamp": datetime.now().isoformat(),
            "data": data or {}
        }

        # Setup response future if waiting for response
        response_future = None
        if wait_for_response:
            response_future = asyncio.Future()
            self._pending_responses[message_id] = response_future

        try:
            # Send message
            await self._websocket.send(json.dumps(message))
            self.logger.debug(f"Sent command: {command_type} (ID: {message_id})")

            if not wait_for_response:
                return None

            # Wait for response
            timeout_value = timeout or self.timeout
            try:
                response_data = await asyncio.wait_for(response_future, timeout=timeout_value)
                return CommandResponse(**response_data)
            except asyncio.TimeoutError:
                raise TimeoutError(f"Command timeout after {timeout_value}s")

        except Exception as e:
            if wait_for_response and message_id in self._pending_responses:
                del self._pending_responses[message_id]
            raise CommandError(f"Failed to send command: {e}")

    async def _send_init_message(self) -> None:
        """Send initialization message to server"""
        init_data = {
            "clientType": "PYTHON_SDK",
            "version": "1.0.0",
            "capabilities": [
                "browser_control",
                "element_query",
                "page_info",
                "screenshots"
            ]
        }

        await self.send_command(
            MessageType.INIT.value,
            init_data,
            wait_for_response=False
        )

    async def _listen_for_messages(self) -> None:
        """Listen for incoming messages from the server"""
        try:
            async for message in self._websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(data)
                except json.JSONDecodeError as e:
                    self.logger.error(f"Failed to parse message: {e}")
                except Exception as e:
                    self.logger.error(f"Error handling message: {e}")
        except websockets.exceptions.ConnectionClosed:
            self.logger.info("WebSocket connection closed")
            self._connection_state = ConnectionState.DISCONNECTED

            if self.auto_reconnect and self._reconnect_attempts < self.max_reconnect_attempts:
                await self._attempt_reconnect()
        except Exception as e:
            self.logger.error(f"Error in message listener: {e}")
            self._connection_state = ConnectionState.ERROR

            if self.on_error:
                try:
                    await self._call_callback(self.on_error, e)
                except Exception as callback_error:
                    self.logger.error(f"Error in error callback: {callback_error}")

    async def _handle_message(self, data: Dict[str, Any]) -> None:
        """Handle incoming message"""
        message_type = data.get("type")
        message_id = data.get("id")

        # Handle response to pending command
        if message_id and message_id in self._pending_responses:
            future = self._pending_responses.pop(message_id)
            if not future.done():
                future.set_result(data)
            return

        # Call message handlers
        if message_type and message_type in self._message_handlers:
            for handler in self._message_handlers[message_type]:
                try:
                    await self._call_callback(handler, data)
                except Exception as e:
                    self.logger.error(f"Error in message handler: {e}")

        # Call global message callback
        if self.on_message:
            try:
                await self._call_callback(self.on_message, data)
            except Exception as e:
                self.logger.error(f"Error in message callback: {e}")

    async def _attempt_reconnect(self) -> None:
        """Attempt to reconnect to the server"""
        self._reconnect_attempts += 1
        self.logger.info(f"Attempting reconnection {self._reconnect_attempts}/{self.max_reconnect_attempts}")

        try:
            await asyncio.sleep(2 ** self._reconnect_attempts)  # Exponential backoff
            await self.connect()
        except Exception as e:
            self.logger.error(f"Reconnection attempt {self._reconnect_attempts} failed: {e}")

            if self._reconnect_attempts >= self.max_reconnect_attempts:
                self.logger.error("Max reconnection attempts reached")
                self._connection_state = ConnectionState.ERROR

    async def _call_callback(self, callback: Callable, *args) -> None:
        """Call a callback function, handling both sync and async"""
        if asyncio.iscoroutinefunction(callback):
            await callback(*args)
        else:
            callback(*args)

    # Browser Control Methods

    async def open_chatgpt(
        self,
        url: Optional[str] = None,
        new_tab: bool = True,
        focus: bool = True,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Open ChatGPT in the browser

        Args:
            url: ChatGPT URL (uses default if None)
            new_tab: Whether to open in a new tab
            focus: Whether to focus the tab
            timeout: Command timeout

        Returns:
            Command response with tab information

        Raises:
            CommandError: If command fails
        """
        data = {
            "url": url,
            "newTab": new_tab,
            "focus": focus,
        }

        response = await self.send_command(
            MessageType.OPEN_CHATGPT.value,
            data,
            timeout=timeout
        )

        if not response.success:
            raise CommandError(f"Failed to open ChatGPT: {response.error}")

        return response

    async def set_window_size(
        self,
        width: int,
        height: int,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Set browser window size

        Args:
            width: Window width in pixels
            height: Window height in pixels
            timeout: Command timeout

        Returns:
            Command response

        Raises:
            CommandError: If command fails
        """
        data = {
            "width": width,
            "height": height,
        }

        response = await self.send_command(
            MessageType.SET_WINDOW_SIZE.value,
            data,
            timeout=timeout
        )

        if not response.success:
            raise CommandError(f"Failed to set window size: {response.error}")

        return response

    async def take_screenshot(
        self,
        format: str = "png",
        quality: Optional[int] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Take a screenshot of the current page

        Args:
            format: Image format ("png" or "jpeg")
            quality: Image quality (1-100, for JPEG only)
            timeout: Command timeout

        Returns:
            Command response with screenshot data

        Raises:
            CommandError: If command fails
        """
        data = {
            "format": format,
        }

        if quality is not None:
            data["quality"] = quality

        response = await self.send_command(
            MessageType.TAKE_SCREENSHOT.value,
            data,
            timeout=timeout
        )

        if not response.success:
            raise CommandError(f"Failed to take screenshot: {response.error}")

        return response

    # Element Query Methods

    async def find_element_by_id(
        self,
        element_id: str,
        actions: Optional[List[Dict[str, Any]]] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Find element by ID

        Args:
            element_id: Element ID to find
            actions: Optional actions to perform on the element
            timeout: Command timeout

        Returns:
            Command response with element information

        Raises:
            CommandError: If command fails
        """
        selector = {
            "type": SelectorType.ID.value,
            "value": element_id,
        }

        return await self._send_element_query(selector, actions, timeout)

    async def find_element_by_css(
        self,
        css_selector: str,
        actions: Optional[List[Dict[str, Any]]] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Find element by CSS selector

        Args:
            css_selector: CSS selector string
            actions: Optional actions to perform on the element
            timeout: Command timeout

        Returns:
            Command response with element information

        Raises:
            CommandError: If command fails
        """
        selector = {
            "type": SelectorType.CSS.value,
            "value": css_selector,
        }

        return await self._send_element_query(selector, actions, timeout)

    async def find_element_by_xpath(
        self,
        xpath: str,
        actions: Optional[List[Dict[str, Any]]] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Find element by XPath

        Args:
            xpath: XPath expression
            actions: Optional actions to perform on the element
            timeout: Command timeout

        Returns:
            Command response with element information

        Raises:
            CommandError: If command fails
        """
        selector = {
            "type": SelectorType.XPATH.value,
            "value": xpath,
        }

        return await self._send_element_query(selector, actions, timeout)

    async def click_element_by_id(
        self,
        element_id: str,
        x: Optional[int] = None,
        y: Optional[int] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Click element by ID

        Args:
            element_id: Element ID to click
            x: Optional X coordinate offset
            y: Optional Y coordinate offset
            timeout: Command timeout

        Returns:
            Command response

        Raises:
            CommandError: If command fails
        """
        actions = [{
            "type": ActionType.CLICK.value,
            "params": {"x": x, "y": y} if x is not None and y is not None else {}
        }]

        return await self.find_element_by_id(element_id, actions, timeout)

    async def click_element_by_css(
        self,
        css_selector: str,
        x: Optional[int] = None,
        y: Optional[int] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Click element by CSS selector

        Args:
            css_selector: CSS selector string
            x: Optional X coordinate offset
            y: Optional Y coordinate offset
            timeout: Command timeout

        Returns:
            Command response

        Raises:
            CommandError: If command fails
        """
        actions = [{
            "type": ActionType.CLICK.value,
            "params": {"x": x, "y": y} if x is not None and y is not None else {}
        }]

        return await self.find_element_by_css(css_selector, actions, timeout)

    async def input_text_by_id(
        self,
        element_id: str,
        text: str,
        clear_first: bool = True,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Input text to element by ID

        Args:
            element_id: Element ID to input text to
            text: Text to input
            clear_first: Whether to clear existing text first
            timeout: Command timeout

        Returns:
            Command response

        Raises:
            CommandError: If command fails
        """
        actions = [{
            "type": ActionType.INPUT.value,
            "params": {"text": text, "clear": clear_first}
        }]

        return await self.find_element_by_id(element_id, actions, timeout)

    async def input_text_by_css(
        self,
        css_selector: str,
        text: str,
        clear_first: bool = True,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Input text to element by CSS selector

        Args:
            css_selector: CSS selector string
            text: Text to input
            clear_first: Whether to clear existing text first
            timeout: Command timeout

        Returns:
            Command response

        Raises:
            CommandError: If command fails
        """
        actions = [{
            "type": ActionType.INPUT.value,
            "params": {"text": text, "clear": clear_first}
        }]

        return await self.find_element_by_css(css_selector, actions, timeout)

    async def get_page_info(
        self,
        info_types: Optional[List[str]] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Get page information

        Args:
            info_types: Types of info to get (url, title, size, scroll, viewport, all)
            timeout: Command timeout

        Returns:
            Command response with page information

        Raises:
            CommandError: If command fails
        """
        data = {
            "info": info_types or ["all"]
        }

        response = await self.send_command(
            MessageType.PAGE_INFO.value,
            data,
            timeout=timeout
        )

        if not response.success:
            raise CommandError(f"Failed to get page info: {response.error}")

        return response

    async def _send_element_query(
        self,
        selector: Dict[str, Any],
        actions: Optional[List[Dict[str, Any]]] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Send element query command

        Args:
            selector: Element selector configuration
            actions: Optional actions to perform
            timeout: Command timeout

        Returns:
            Command response

        Raises:
            CommandError: If command fails
        """
        data = {
            "selector": selector,
        }

        if actions:
            data["actions"] = actions

        response = await self.send_command(
            MessageType.ELEMENT_QUERY.value,
            data,
            timeout=timeout
        )

        if not response.success:
            raise CommandError(f"Element query failed: {response.error}")

        return response

    # Response Handling and Utility Methods

    async def wait_for_message(
        self,
        message_type: str,
        timeout: Optional[float] = None,
        filter_func: Optional[Callable[[Dict[str, Any]], bool]] = None,
    ) -> Dict[str, Any]:
        """
        Wait for a specific message type

        Args:
            message_type: Type of message to wait for
            timeout: Timeout in seconds
            filter_func: Optional filter function to match specific messages

        Returns:
            The received message data

        Raises:
            TimeoutError: If timeout is reached
        """
        future = asyncio.Future()

        def handler(data: Dict[str, Any]) -> None:
            if not filter_func or filter_func(data):
                if not future.done():
                    future.set_result(data)

        # Add temporary handler
        self.add_message_handler(message_type, handler)

        try:
            timeout_value = timeout or self.timeout
            result = await asyncio.wait_for(future, timeout=timeout_value)
            return result
        except asyncio.TimeoutError:
            raise TimeoutError(f"Timeout waiting for message type: {message_type}")
        finally:
            # Remove temporary handler
            self.remove_message_handler(message_type, handler)

    def get_connection_info(self) -> ConnectionInfo:
        """
        Get connection information

        Returns:
            ConnectionInfo model with connection details
        """
        return ConnectionInfo(
            url=self.url,
            state=self.connection_state,
            connected=self.is_connected,
            reconnect_attempts=self._reconnect_attempts,
            max_reconnect_attempts=self.max_reconnect_attempts,
            auto_reconnect=self.auto_reconnect,
            timeout=self.timeout,
        )

    async def ping(self, timeout: Optional[float] = None) -> float:
        """
        Send a ping to test connection and measure latency

        Args:
            timeout: Ping timeout

        Returns:
            Round-trip time in seconds

        Raises:
            ConnectionError: If not connected
            TimeoutError: If ping timeout
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to server")

        start_time = datetime.now()

        response = await self.send_command(
            MessageType.PING.value,
            {"timestamp": start_time.isoformat()},
            timeout=timeout
        )

        end_time = datetime.now()
        latency = (end_time - start_time).total_seconds()

        return latency

    async def wait_for_element_query_result(
        self,
        query_id: str,
        timeout: Optional[float] = None,
    ) -> Dict[str, Any]:
        """
        Wait for element query result by query ID

        Args:
            query_id: Query ID to wait for
            timeout: Timeout in seconds

        Returns:
            Element query result data

        Raises:
            TimeoutError: If timeout is reached
        """
        def filter_func(data: Dict[str, Any]) -> bool:
            return data.get("id") == query_id

        return await self.wait_for_message(
            MessageType.ELEMENT_QUERY_RESPONSE.value,
            timeout=timeout,
            filter_func=filter_func
        )

    def set_callbacks(
        self,
        on_connected: Optional[Callable] = None,
        on_disconnected: Optional[Callable] = None,
        on_error: Optional[Callable] = None,
        on_message: Optional[Callable] = None,
    ) -> None:
        """
        Set callback functions for various events

        Args:
            on_connected: Called when connection is established
            on_disconnected: Called when connection is lost
            on_error: Called when an error occurs
            on_message: Called for every received message
        """
        if on_connected is not None:
            self.on_connected = on_connected
        if on_disconnected is not None:
            self.on_disconnected = on_disconnected
        if on_error is not None:
            self.on_error = on_error
        if on_message is not None:
            self.on_message = on_message
