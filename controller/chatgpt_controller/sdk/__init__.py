"""
ChatGPT Controller SDK

Python SDK for programmatic interaction with the ChatGPT Forward Plugin.
Provides a clean, well-documented API for connecting to and controlling the browser extension.
"""

from .client import ChatGPTControllerSDK
from .exceptions import (
    SDKError,
    ConnectionError,
    TimeoutError,
    CommandError,
    ResponseError,
)
from .types import (
    ConnectionState,
    ElementSelector,
    ElementAction,
    BrowserControlCommand,
    ElementQueryCommand,
    CommandResponse,
    ElementInfo,
    PageInfo,
    SelectorType,
    ActionType,
    ConnectionInfo,
    SDKConfig,
    ElementQueryResult,
    BrowserControlResult,
    ScreenshotResult,
    MessageFilter,
)
from .logging import (
    setup_sdk_logging,
    get_sdk_logger,
    SDKLogger,
    create_console,
    LoggingMixin,
    SDK_THEME,
)

__all__ = [
    # Main SDK class
    "ChatGPTControllerSDK",

    # Exceptions
    "SDKError",
    "ConnectionError",
    "TimeoutError",
    "CommandError",
    "ResponseError",

    # Types and Models
    "ConnectionState",
    "ElementSelector",
    "ElementAction",
    "BrowserControlCommand",
    "ElementQueryCommand",
    "CommandResponse",
    "ElementInfo",
    "PageInfo",
    "SelectorType",
    "ActionType",
    "ConnectionInfo",
    "SDKConfig",
    "ElementQueryResult",
    "BrowserControlResult",
    "ScreenshotResult",
    "MessageFilter",

    # Logging
    "setup_sdk_logging",
    "get_sdk_logger",
    "SDKLogger",
    "create_console",
    "LoggingMixin",
    "SDK_THEME",
]
