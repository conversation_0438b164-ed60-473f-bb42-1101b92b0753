"""
SDK Logging Configuration

Provides Rich-based logging utilities and configuration for the ChatGPT Controller SDK.
"""

import logging
from typing import Optional
from rich.console import Console
from rich.logging import <PERSON>Handler
from rich.traceback import install as install_rich_traceback
from rich.theme import Theme


# Install rich traceback handler for better error formatting
install_rich_traceback(show_locals=True)

# Custom theme for SDK logging
SDK_THEME = Theme({
    "sdk.timestamp": "dim cyan",
    "sdk.level.debug": "cyan",
    "sdk.level.info": "green",
    "sdk.level.warning": "yellow",
    "sdk.level.error": "red",
    "sdk.level.critical": "bold red",
    "sdk.context": "blue",
    "sdk.message": "white",
    "sdk.connection": "bright_green",
    "sdk.command": "bright_blue",
    "sdk.response": "bright_magenta",
    "sdk.error": "bright_red",
})


class SDKRichHandler(RichHandler):
    """Custom Rich handler for SDK logging"""

    def __init__(self, console: Optional[Console] = None, **kwargs):
        if console is None:
            console = Console(theme=SDK_THEME, stderr=True)

        super().__init__(
            console=console,
            show_time=True,
            show_level=True,
            show_path=False,
            markup=True,
            rich_tracebacks=True,
            tracebacks_show_locals=True,
            **kwargs
        )

        # Emoji mapping for log levels
        self.level_emojis = {
            'DEBUG': '🔍',
            'INFO': 'ℹ️',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'CRITICAL': '💥',
        }

    def emit(self, record: logging.LogRecord) -> None:
        """Emit a log record with Rich formatting"""
        try:
            # Add emoji to the message
            emoji = self.level_emojis.get(record.levelname, '')
            if emoji:
                record.msg = f"{emoji} {record.msg}"

            # Add context styling if present
            if hasattr(record, 'sdk_context'):
                context = record.sdk_context
                record.msg = f"[sdk.context]\\[{context}][/sdk.context] {record.msg}"

            super().emit(record)

        except Exception:
            self.handleError(record)


class SDKLogger:
    """Enhanced Rich-based logger for SDK operations"""

    def __init__(self, name: str, level: int = logging.INFO, console: Optional[Console] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        # Remove existing handlers to avoid duplicates
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # Create Rich handler
        if console is None:
            console = Console(theme=SDK_THEME, stderr=True)

        handler = SDKRichHandler(console=console, level=level)
        self.logger.addHandler(handler)

        # Prevent propagation to avoid duplicate logs
        self.logger.propagate = False
    
    def debug(self, message: str, context: Optional[str] = None, **kwargs):
        """Log debug message with Rich formatting"""
        self._log(logging.DEBUG, message, context, **kwargs)

    def info(self, message: str, context: Optional[str] = None, **kwargs):
        """Log info message with Rich formatting"""
        self._log(logging.INFO, message, context, **kwargs)

    def warning(self, message: str, context: Optional[str] = None, **kwargs):
        """Log warning message with Rich formatting"""
        self._log(logging.WARNING, message, context, **kwargs)

    def error(self, message: str, context: Optional[str] = None, exc_info: bool = False, **kwargs):
        """Log error message with Rich formatting"""
        self._log(logging.ERROR, message, context, exc_info=exc_info, **kwargs)

    def critical(self, message: str, context: Optional[str] = None, exc_info: bool = False, **kwargs):
        """Log critical message with Rich formatting"""
        self._log(logging.CRITICAL, message, context, exc_info=exc_info, **kwargs)

    def _log(self, level: int, message: str, context: Optional[str] = None, exc_info: bool = False, **kwargs):
        """Internal logging method with Rich support"""
        extra = kwargs.copy()
        if context:
            extra['sdk_context'] = context

        self.logger.log(level, message, extra=extra, exc_info=exc_info)

    def connection_event(self, message: str):
        """Log connection-related events with special styling"""
        self.info(f"[sdk.connection]{message}[/sdk.connection]", context="CONNECTION")

    def command_event(self, message: str):
        """Log command-related events with special styling"""
        self.info(f"[sdk.command]{message}[/sdk.command]", context="COMMAND")

    def response_event(self, message: str):
        """Log response-related events with special styling"""
        self.debug(f"[sdk.response]{message}[/sdk.response]", context="RESPONSE")

    def error_event(self, message: str, exc_info: bool = True):
        """Log error events with special styling"""
        self.error(f"[sdk.error]{message}[/sdk.error]", context="ERROR", exc_info=exc_info)

    def success(self, message: str, context: Optional[str] = None):
        """Log success message with green styling"""
        self.info(f"[green]✅ {message}[/green]", context=context)

    def failure(self, message: str, context: Optional[str] = None):
        """Log failure message with red styling"""
        self.error(f"[red]❌ {message}[/red]", context=context)


def setup_sdk_logging(
    level: int = logging.INFO,
    console: Optional[Console] = None,
    enable_rich_traceback: bool = True
) -> SDKLogger:
    """
    Setup SDK logging configuration with Rich formatting

    Args:
        level: Logging level
        console: Optional Rich console instance
        enable_rich_traceback: Whether to enable Rich traceback formatting

    Returns:
        Configured SDK logger with Rich formatting
    """
    if enable_rich_traceback:
        install_rich_traceback(show_locals=True)

    return SDKLogger("chatgpt_controller.sdk", level, console)


def get_sdk_logger(name: str, console: Optional[Console] = None) -> SDKLogger:
    """
    Get SDK logger for a specific component with Rich formatting

    Args:
        name: Logger name
        console: Optional Rich console instance

    Returns:
        SDK logger instance with Rich formatting
    """
    return SDKLogger(f"chatgpt_controller.sdk.{name}", console=console)


def create_console(
    theme: Optional[Theme] = None,
    width: Optional[int] = None,
    force_terminal: bool = False
) -> Console:
    """
    Create a Rich console with SDK theme

    Args:
        theme: Custom theme (uses SDK_THEME if None)
        width: Console width
        force_terminal: Force terminal mode

    Returns:
        Configured Rich console
    """
    return Console(
        theme=theme or SDK_THEME,
        width=width,
        force_terminal=force_terminal,
        stderr=True
    )


# Default logger for the SDK
default_logger = setup_sdk_logging()


class LoggingMixin:
    """Mixin class to add Rich logging capabilities to SDK classes"""

    def __init__(self, *args, console: Optional[Console] = None, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = get_sdk_logger(self.__class__.__name__.lower(), console)

    @property
    def logger(self) -> SDKLogger:
        """Get Rich logger instance"""
        return self._logger

    def log_connection(self, message: str):
        """Log connection event with Rich styling"""
        self.logger.connection_event(message)

    def log_command(self, message: str):
        """Log command event with Rich styling"""
        self.logger.command_event(message)

    def log_response(self, message: str):
        """Log response event with Rich styling"""
        self.logger.response_event(message)

    def log_error(self, message: str, exc_info: bool = True):
        """Log error event with Rich styling"""
        self.logger.error_event(message, exc_info=exc_info)

    def log_success(self, message: str):
        """Log success event with Rich styling"""
        self.logger.success(message)

    def log_failure(self, message: str):
        """Log failure event with Rich styling"""
        self.logger.failure(message)

    def log_progress(self, message: str, step: int, total: int):
        """Log progress with Rich styling"""
        percentage = (step / total) * 100 if total > 0 else 0
        self.logger.info(f"[cyan]📊 {message} ({step}/{total} - {percentage:.1f}%)[/cyan]")
