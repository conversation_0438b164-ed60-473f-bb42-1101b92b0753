"""
SDK Type Definitions

Type definitions and Pydantic models for the ChatGPT Controller SDK.
"""

from enum import Enum
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator, ConfigDict


class ConnectionState(Enum):
    """WebSocket connection states"""
    DISCONNECTED = "DISCONNECTED"
    CONNECTING = "CONNECTING"
    CONNECTED = "CONNECTED"
    ERROR = "ERROR"


class SelectorType(Enum):
    """Element selector types"""
    XPATH = "xpath"
    CSS = "css"
    ID = "id"
    CLASS = "class"
    TAG = "tag"
    TEXT = "text"
    ATTRIBUTE = "attribute"


class ActionType(Enum):
    """Element action types"""
    CLICK = "click"
    INPUT = "input"
    SCROLL = "scroll"
    HOVER = "hover"
    FOCUS = "focus"
    BLUR = "blur"
    GET_ATTRIBUTE = "getAttribute"
    SET_TEXT = "setText"
    SIMULATE_KEYBOARD = "simulateKeyboard"


class ElementSelector(BaseModel):
    """Element selector configuration"""
    model_config = ConfigDict(use_enum_values=True)

    type: SelectorType = Field(..., description="Type of selector")
    value: str = Field(..., min_length=1, description="Selector value")
    options: Optional[Dict[str, Any]] = Field(None, description="Additional selector options")

    @validator('value')
    def validate_value(cls, v):
        if not v or not v.strip():
            raise ValueError("Selector value cannot be empty")
        return v.strip()


class ElementAction(BaseModel):
    """Element action configuration"""
    model_config = ConfigDict(use_enum_values=True)

    type: ActionType = Field(..., description="Type of action to perform")
    params: Optional[Dict[str, Any]] = Field(None, description="Action parameters")


class BrowserControlCommand(BaseModel):
    """Browser control command"""
    type: str = Field(..., description="Command type")
    data: Dict[str, Any] = Field(default_factory=dict, description="Command data")


class ElementQueryCommand(BaseModel):
    """Element query command"""
    selector: ElementSelector = Field(..., description="Element selector")
    actions: Optional[List[ElementAction]] = Field(None, description="Actions to perform on element")


class CommandResponse(BaseModel):
    """Command response"""
    id: str = Field(..., description="Response ID")
    type: str = Field(..., description="Response type")
    success: bool = Field(..., description="Whether the command succeeded")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if failed")
    timestamp: Optional[datetime] = Field(None, description="Response timestamp")


class ElementInfo(BaseModel):
    """Element information"""
    tag: str = Field(..., description="HTML tag name")
    id: Optional[str] = Field(None, description="Element ID")
    classes: Optional[List[str]] = Field(None, description="CSS classes")
    text: Optional[str] = Field(None, description="Element text content")
    attributes: Optional[Dict[str, str]] = Field(None, description="Element attributes")
    position: Optional[Dict[str, int]] = Field(None, description="Element position")
    size: Optional[Dict[str, int]] = Field(None, description="Element size")
    visible: Optional[bool] = Field(None, description="Whether element is visible")


class PageInfo(BaseModel):
    """Page information"""
    url: str = Field(..., description="Page URL")
    title: str = Field(..., description="Page title")
    size: Dict[str, int] = Field(..., description="Page size")
    scroll: Dict[str, int] = Field(..., description="Scroll position")
    viewport: Dict[str, int] = Field(..., description="Viewport size")


class ConnectionInfo(BaseModel):
    """Connection information"""
    url: str = Field(..., description="WebSocket URL")
    state: ConnectionState = Field(..., description="Connection state")
    connected: bool = Field(..., description="Whether currently connected")
    reconnect_attempts: int = Field(0, ge=0, description="Number of reconnection attempts")
    max_reconnect_attempts: int = Field(5, ge=0, description="Maximum reconnection attempts")
    auto_reconnect: bool = Field(True, description="Whether auto-reconnect is enabled")
    timeout: float = Field(30.0, gt=0, description="Default timeout in seconds")


class SDKConfig(BaseModel):
    """SDK configuration"""
    url: str = Field("ws://localhost:8765", description="WebSocket server URL")
    timeout: float = Field(30.0, gt=0, description="Default timeout in seconds")
    auto_reconnect: bool = Field(True, description="Enable auto-reconnection")
    max_reconnect_attempts: int = Field(5, ge=0, description="Maximum reconnection attempts")
    log_level: str = Field("INFO", description="Logging level")

    @validator('url')
    def validate_url(cls, v):
        if not v.startswith(('ws://', 'wss://')):
            raise ValueError("URL must start with ws:// or wss://")
        return v

    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()


class ElementQueryResult(BaseModel):
    """Element query result"""
    success: bool = Field(..., description="Whether the query succeeded")
    elements: List[ElementInfo] = Field(default_factory=list, description="Found elements")
    count: int = Field(0, ge=0, description="Number of elements found")
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: int = Field(0, ge=0, description="Execution time in milliseconds")

    @validator('count')
    def validate_count(cls, v, values):
        elements = values.get('elements', [])
        if v != len(elements):
            return len(elements)
        return v


class BrowserControlResult(BaseModel):
    """Browser control operation result"""
    success: bool = Field(..., description="Whether the operation succeeded")
    operation: str = Field(..., description="Type of operation performed")
    data: Optional[Dict[str, Any]] = Field(None, description="Operation result data")
    error: Optional[str] = Field(None, description="Error message if failed")
    tab_id: Optional[int] = Field(None, description="Browser tab ID")


class ScreenshotResult(BaseModel):
    """Screenshot operation result"""
    success: bool = Field(..., description="Whether the screenshot succeeded")
    format: str = Field("png", description="Image format")
    data: Optional[str] = Field(None, description="Base64 encoded image data")
    size: Optional[Dict[str, int]] = Field(None, description="Image dimensions")
    error: Optional[str] = Field(None, description="Error message if failed")


class MessageFilter(BaseModel):
    """Message filter configuration"""
    message_type: str = Field(..., description="Type of message to filter")
    timeout: float = Field(30.0, gt=0, description="Timeout in seconds")
    max_messages: int = Field(1, ge=1, description="Maximum messages to collect")

    class Config:
        arbitrary_types_allowed = True
