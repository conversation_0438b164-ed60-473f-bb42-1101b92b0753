"""
Browser control interfaces for ChatGPT Controller

This module defines the interfaces for browser control operations that can be
implemented by different components (WebSocket server, GUI, SDK, etc.)
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from dataclasses import dataclass


@dataclass
class CommandResponse:
    """Response from a browser control command"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    message_id: Optional[str] = None


@dataclass
class ElementInfo:
    """Information about a DOM element"""
    tag_name: str
    id: Optional[str] = None
    class_name: Optional[str] = None
    text_content: Optional[str] = None
    attributes: Optional[Dict[str, str]] = None
    bounding_rect: Optional[Dict[str, float]] = None


class IBrowserController(ABC):
    """Interface for browser control operations"""

    @abstractmethod
    async def open_chatgpt(
        self,
        url: Optional[str] = None,
        new_tab: bool = True,
        focus: bool = True,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Open ChatGPT in the browser
        
        Args:
            url: ChatGPT URL (uses default if None)
            new_tab: Whether to open in a new tab
            focus: Whether to focus the tab
            timeout: Command timeout
            
        Returns:
            Command response with tab information
        """
        pass

    @abstractmethod
    async def set_window_size(
        self,
        width: int,
        height: int,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Set browser window size
        
        Args:
            width: Window width in pixels
            height: Window height in pixels
            timeout: Command timeout
            
        Returns:
            Command response
        """
        pass

    @abstractmethod
    async def take_screenshot(
        self,
        format: str = "png",
        quality: Optional[int] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Take a screenshot of the current page
        
        Args:
            format: Image format (png, jpeg)
            quality: Image quality (for jpeg)
            timeout: Command timeout
            
        Returns:
            Command response with screenshot data
        """
        pass

    @abstractmethod
    async def ping(
        self,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Ping the browser extension
        
        Args:
            timeout: Command timeout
            
        Returns:
            Command response
        """
        pass


class IConnectionManager(ABC):
    """Interface for connection management"""

    @abstractmethod
    def get_connected_clients_count(self) -> int:
        """Get the number of connected clients"""
        pass

    @abstractmethod
    def is_connected(self) -> bool:
        """Check if any clients are connected"""
        pass

    @abstractmethod
    async def send_message_to_all(self, message: Dict[str, Any]) -> bool:
        """Send a message to all connected clients"""
        pass


class IWebSocketServer(IBrowserController, IConnectionManager):
    """Combined interface for WebSocket server functionality"""

    @abstractmethod
    def is_running(self) -> bool:
        """Check if the server is running"""
        pass

    @abstractmethod
    async def start_server(self) -> None:
        """Start the WebSocket server"""
        pass

    @abstractmethod
    async def stop_server(self) -> None:
        """Stop the WebSocket server"""
        pass

    @abstractmethod
    def get_uptime(self) -> float:
        """Get server uptime in seconds"""
        pass
