"""
Element Query Handler for ChatGPT Controller

Handles element query operations and responses from the browser plugin.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..core.message_types import MessageType, BaseMessage
from ..core.events import EventType, emit_event


class ElementQueryHandler:
    """Handles element query operations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.pending_queries: Dict[str, Dict[str, Any]] = {}
        self.query_results: Dict[str, Dict[str, Any]] = {}

    def handle_element_query_response(
        self, msg: BaseMessage, context: Optional[Dict[str, Any]] = None
    ):
        """Handle element query response from plugin"""
        try:
            query_id = msg.id
            client_id = context.get("client_id", "unknown")

            if not query_id:
                self.logger.warning("Element query response missing ID")
                return

            # Get data from message
            # The parser now puts all extra fields into msg.data
            data = msg.data or {}

            # Also check the top-level success field if available
            if msg.success is not None:
                data['success'] = msg.success

            # Extract element query information
            success = data.get("success", False)
            elements = data.get("elements", [])
            count = data.get("count", 0)
            error = data.get("error")
            execution_time = data.get("executionTime", 0)

            # Store result
            self.query_results[query_id] = {
                "timestamp": datetime.now(),
                "success": success,
                "elements": elements,
                "count": count,
                "error": error,
                "execution_time": execution_time,
                **msg.to_dict()
            }
            # from rich import print
            # print(self.query_results)

            # Remove from pending
            if query_id in self.pending_queries:
                del self.pending_queries[query_id]

            # Log element query details (like monitored API)
            if success:
                self.logger.info(
                    f"Element Query Result from {client_id}: SUCCESS "
                    f"(count: {count}, execution_time: {execution_time}ms)"
                )

                # Log element details if present
                if elements:
                    for i, element in enumerate(elements[:3]):  # Log first 3 elements
                        selector = element.get("selector", "unknown")
                        tag_name = element.get("attributes", {}).get("tagName", "unknown")
                        self.logger.info(f"  Element {i+1}: {tag_name} - {selector}")

                    if len(elements) > 3:
                        self.logger.info(f"  ... and {len(elements) - 3} more elements")
            else:
                self.logger.warning(
                    f"Element Query Result from {client_id}: FAILED "
                    f"(error: {error or 'Unknown error'})"
                )

            # Emit element query event (like monitored API)
            emit_event(
                EventType.MESSAGE_RECEIVED,
                {
                    "client_id": client_id,
                    "message_type": "element_query_result",
                    "query_id": query_id,
                    "success": success,
                    "count": count,
                    "elements": elements,
                    "error": error,
                    "execution_time": execution_time,
                    "data": data,
                    "timestamp": self._get_timestamp_str(msg.timestamp),
                },
            )

            # Call custom handlers if any
            if context and "element_query_callback" in context:
                callback = context["element_query_callback"]
                callback(query_id, self.query_results[query_id])

        except Exception as e:
            self.logger.error(f"Error handling element query response: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def handle_element_event(
        self, parsed_message: BaseMessage, context: Optional[Dict[str, Any]] = None
    ):
        """Handle element event from plugin"""
        try:
            client_id = context.get("client_id", "unknown")
            data = parsed_message.data or {}

            # Handle both data structures
            if "data" in data:
                event_data = data.get("data", {})
            else:
                event_data = data

            event_type = event_data.get("eventType", "unknown")
            element_info = event_data.get("element", {})

            # Log element event details (like monitored API)
            self.logger.info(f"Element Event from {client_id}: {event_type}")

            # Log element details if present
            if element_info:
                selector = element_info.get("selector", "unknown")
                tag_name = element_info.get("attributes", {}).get("tagName", "unknown")
                self.logger.info(f"  Element: {tag_name} - {selector}")

            # Emit element event (like monitored API)
            emit_event(
                EventType.MESSAGE_RECEIVED,
                {
                    "client_id": client_id,
                    "message_type": "element_event",
                    "event_type": event_type,
                    "element_info": element_info,
                    "data": data,
                    "timestamp": self._get_timestamp_str(parsed_message.timestamp),
                },
            )

            # Call custom handlers if any
            if context and "element_event_callback" in context:
                callback = context["element_event_callback"]
                callback(event_type, element_info, event_data)

        except Exception as e:
            self.logger.error(f"Error handling element event: {e}")

    def _get_timestamp_str(self, timestamp) -> str:
        """Convert timestamp to string, handling both datetime objects and strings"""
        if isinstance(timestamp, str):
            return timestamp
        elif hasattr(timestamp, 'isoformat'):
            return timestamp.isoformat()
        else:
            return str(timestamp)

    def get_query_result(self, query_id: str) -> Optional[Dict[str, Any]]:
        """Get query result by ID"""
        return self.query_results.get(query_id)

    def is_query_pending(self, query_id: str) -> bool:
        """Check if query is still pending"""
        return query_id in self.pending_queries

    def add_pending_query(self, query_id: str, query_data: Dict[str, Any]):
        """Add query to pending list"""
        self.pending_queries[query_id] = {
            "timestamp": datetime.now(),
            "data": query_data
        }

    def get_pending_queries(self) -> Dict[str, Dict[str, Any]]:
        """Get all pending queries"""
        return self.pending_queries.copy()

    def clear_old_results(self, max_age_hours: int = 24):
        """Clear old query results"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)

        # Clear old results
        old_result_ids = []
        for query_id, result in self.query_results.items():
            if result["timestamp"].timestamp() < cutoff_time:
                old_result_ids.append(query_id)

        for query_id in old_result_ids:
            del self.query_results[query_id]

        # Clear old pending queries
        old_pending_ids = []
        for query_id, pending in self.pending_queries.items():
            if pending["timestamp"].timestamp() < cutoff_time:
                old_pending_ids.append(query_id)

        for query_id in old_pending_ids:
            del self.pending_queries[query_id]

        if old_result_ids or old_pending_ids:
            self.logger.info(
                f"Cleared {len(old_result_ids)} old results and "
                f"{len(old_pending_ids)} old pending queries"
            )

    def get_stats(self) -> Dict[str, Any]:
        """Get handler statistics"""
        return {
            "pending_queries": len(self.pending_queries),
            "cached_results": len(self.query_results)
        }


class ElementQueryBuilder:
    """Helper class to build element query commands"""

    @staticmethod
    def element_query(
        selector_type: str,
        selector_value: str,
        actions: Optional[List[Dict[str, Any]]] = None,
        options: Optional[Dict[str, Any]] = None
        
    ) -> Dict[str, Any]:
        """Build generic element query command"""
        from ..core.commands import ElementCommands
        return ElementCommands.element_query(
            selector_type=selector_type,
            selector_value=selector_value,
            actions=actions,
            options=options
        )

    @staticmethod
    def find_element_by_id(element_id: str, actions: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Build query to find element by ID"""
        from ..core.commands import ElementCommands
        return ElementCommands.element_query(
            selector_type="id",
            selector_value=element_id,
            actions=actions
        )

    @staticmethod
    def find_element_by_css(css_selector: str, actions: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Build query to find element by CSS selector"""
        from ..core.commands import ElementCommands
        return ElementCommands.element_query(
            selector_type="css",
            selector_value=css_selector,
            actions=actions
        )

    @staticmethod
    def find_element_by_xpath(xpath: str, actions: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Build query to find element by XPath"""
        from ..core.commands import ElementCommands
        return ElementCommands.element_query(
            selector_type="xpath",
            selector_value=xpath,
            actions=actions
        )

    @staticmethod
    def click_element_by_id(element_id: str, x: Optional[int] = None, y: Optional[int] = None, selector_type: str = "id") -> Dict[str, Any]:
        """Build command to click element by ID"""
        from ..core.commands import ElementCommands
        return ElementCommands.click_element(
            selector_type=selector_type,
            selector_value=element_id,
            x=x,
            y=y
        )

    @staticmethod
    def input_text_by_id(element_id: str, text: str, selector_type: str = "id") -> Dict[str, Any]:
        """Build command to input text by ID"""
        from ..core.commands import ElementCommands
        return ElementCommands.input_text(
            selector_type="id",
            selector_value=element_id,
            text=text
        )

    @staticmethod
    def get_page_info() -> Dict[str, Any]:
        """Build command to get page info"""
        from ..core.commands import ElementCommands
        return ElementCommands.page_info()

    @staticmethod
    def watch_element_events(
        selector_type: str,
        selector_value: str,
        events: List[str],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Build command to watch element events"""
        from ..core.commands import ElementCommands
        return ElementCommands.element_watch(
            selector_type=selector_type,
            selector_value=selector_value,
            events=events,
            options=options
        )

    @staticmethod
    def batch_query(queries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build batch query command"""
        from ..core.commands import ElementCommands
        return ElementCommands.batch_element_query(queries)


class ElementQueryManager:
    """High-level manager for element queries"""

    def __init__(self, websocket_server=None):
        self.handler = ElementQueryHandler()
        self.builder = ElementQueryBuilder()
        self.websocket_server = websocket_server
        self.logger = logging.getLogger(__name__)

    def send_query(self, command_data: Dict[str, Any]) -> str:
        """Send element query to plugin"""
        query_id = command_data.get("id")
        
        if not query_id:
            raise ValueError("Command data missing ID")

        # Add to pending queries
        self.handler.add_pending_query(query_id, command_data)

        # Send via WebSocket
        if self.websocket_server:
            message = json.dumps(command_data)
            self.websocket_server.broadcast_message(message)
            self.logger.info(f"Sent element query {query_id}")
        else:
            self.logger.warning("No WebSocket server available to send query")

        return query_id

    def wait_for_result(self, query_id: str, timeout_seconds: int = 30) -> Optional[Dict[str, Any]]:
        """Wait for query result (blocking)"""
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            result = self.handler.get_query_result(query_id)
            if result:
                return result
            time.sleep(0.1)
        
        return None

    def setup_handlers(self, message_handler):
        """Setup message handlers"""
        message_handler.add_handler(
            MessageType.ELEMENT_QUERY_RESPONSE,
            self.handler.handle_element_query_response
        )
        message_handler.add_handler(
            MessageType.ELEMENT_QUERY_RESULT,
            self.handler.handle_element_query_response
        )
        message_handler.add_handler(
            MessageType.ELEMENT_EVENT_MESSAGE,
            self.handler.handle_element_event
        )
