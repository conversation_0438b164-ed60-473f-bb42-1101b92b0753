"""
Operations Panel for ChatGPT Controller GUI

Provides controls for browser extension compatible operations and commands.
Aligned with browser extension's TypeScript interface definitions.
"""

from PyQt5.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGroupBox,
    QPushButton,
    QLineEdit,
    QLabel,
    QComboBox,
    QScrollArea,
    QCheckBox,
    QSpinBox,
    QSplitter,
)
from PyQt5.QtCore import Qt, pyqtSignal

from ..core.message_types import MessageType, create_base_message
from ..utils.logging import RichLogger
from .image_preview import ImagePreviewWidget
from .i18n import tr


class OperationsPanel(QWidget):
    """Browser extension compatible operations panel"""

    # Signals
    command_requested = pyqtSignal(str, dict)  # command_type, data

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = RichLogger(__name__)

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        # Main layout with splitter
        main_layout = QVBoxLayout(self)

        # Create horizontal splitter
        splitter = QSplitter(Qt.Horizontal)

        # Left side - Controls
        self.create_controls_panel(splitter)

        # Right side - Image preview
        self.create_image_preview_panel(splitter)

        # Set splitter proportions (70% controls, 30% preview)
        splitter.setSizes([700, 300])

        main_layout.addWidget(splitter)

    def create_controls_panel(self, parent):
        """Create the controls panel"""
        # Create scroll area for the content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create content widget
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)

        # Browser control operations group
        self.create_browser_control_group(layout)

        # Element query operations group
        self.create_element_query_group(layout)

        # Page information operations group
        self.create_page_info_group(layout)

        # Connection operations group
        self.create_connection_group(layout)

        # Add stretch to push everything to the top
        layout.addStretch()

        # Set content widget to scroll area
        scroll_area.setWidget(content_widget)

        parent.addWidget(scroll_area)

    def create_image_preview_panel(self, parent):
        """Create the image preview panel"""
        self.image_preview = ImagePreviewWidget()

        # Connect signals
        self.image_preview.error_occurred.connect(self.on_image_preview_error)

        parent.addWidget(self.image_preview)

    def create_browser_control_group(self, parent_layout):
        """Create browser control operations group - aligned with browser extension"""
        self.browser_control_group = QGroupBox(tr("browser_control_group"))
        layout = QVBoxLayout(self.browser_control_group)

        # Open ChatGPT section
        chatgpt_layout = QHBoxLayout()

        # URL input
        chatgpt_layout.addWidget(QLabel(tr("label_url")))
        self.chatgpt_url_input = QLineEdit()
        self.chatgpt_url_input.setPlaceholderText(tr("placeholder_chatgpt_url"))
        self.chatgpt_url_input.setStyleSheet(self.get_input_style())
        chatgpt_layout.addWidget(self.chatgpt_url_input)

        # Options
        self.new_tab_checkbox = QCheckBox(tr("checkbox_new_tab"))
        self.new_tab_checkbox.setChecked(True)
        chatgpt_layout.addWidget(self.new_tab_checkbox)

        self.focus_checkbox = QCheckBox(tr("checkbox_focus"))
        self.focus_checkbox.setChecked(True)
        chatgpt_layout.addWidget(self.focus_checkbox)

        # Open button
        self.open_chatgpt_btn = QPushButton(tr("button_open_chatgpt"))
        self.open_chatgpt_btn.clicked.connect(self.open_chatgpt)
        self.open_chatgpt_btn.setStyleSheet(self.get_primary_button_style())
        chatgpt_layout.addWidget(self.open_chatgpt_btn)

        layout.addLayout(chatgpt_layout)

        # Window size controls
        window_size_layout = QHBoxLayout()

        # Width input
        window_size_layout.addWidget(QLabel(tr("label_width")))
        self.window_width_input = QSpinBox()
        self.window_width_input.setRange(100, 4000)
        self.window_width_input.setValue(1920)
        self.window_width_input.setMaximumWidth(80)
        window_size_layout.addWidget(self.window_width_input)

        # Height input
        window_size_layout.addWidget(QLabel(tr("label_height")))
        self.window_height_input = QSpinBox()
        self.window_height_input.setRange(100, 4000)
        self.window_height_input.setValue(1080)
        self.window_height_input.setMaximumWidth(80)
        window_size_layout.addWidget(self.window_height_input)

        # Set window size button
        self.set_window_size_btn = QPushButton(tr("button_set_window_size"))
        self.set_window_size_btn.clicked.connect(self.set_window_size)
        self.set_window_size_btn.setStyleSheet(self.get_secondary_button_style())
        window_size_layout.addWidget(self.set_window_size_btn)

        window_size_layout.addStretch()
        layout.addLayout(window_size_layout)

        # Screenshot controls
        screenshot_layout = QHBoxLayout()

        # Format selection
        screenshot_layout.addWidget(QLabel(tr("label_format")))
        self.screenshot_format_combo = QComboBox()
        self.screenshot_format_combo.addItems(["png", "jpeg"])
        self.screenshot_format_combo.setMaximumWidth(80)
        screenshot_layout.addWidget(self.screenshot_format_combo)

        # Quality input (for JPEG)
        screenshot_layout.addWidget(QLabel(tr("label_quality")))
        self.screenshot_quality_input = QSpinBox()
        self.screenshot_quality_input.setRange(1, 100)
        self.screenshot_quality_input.setValue(90)
        self.screenshot_quality_input.setMaximumWidth(60)
        screenshot_layout.addWidget(self.screenshot_quality_input)

        # Full page checkbox
        self.full_page_checkbox = QCheckBox(tr("checkbox_full_page"))
        screenshot_layout.addWidget(self.full_page_checkbox)

        # Take screenshot button
        self.take_screenshot_btn = QPushButton(tr("button_take_screenshot"))
        self.take_screenshot_btn.clicked.connect(self.take_screenshot)
        self.take_screenshot_btn.setStyleSheet(self.get_secondary_button_style())
        screenshot_layout.addWidget(self.take_screenshot_btn)

        screenshot_layout.addStretch()
        layout.addLayout(screenshot_layout)

        parent_layout.addWidget(self.browser_control_group)

    def create_element_query_group(self, parent_layout):
        """Create element query operations group - aligned with browser extension"""
        self.element_query_group = QGroupBox(tr("element_query_group"))
        layout = QVBoxLayout(self.element_query_group)

        # Selector input
        selector_layout = QHBoxLayout()

        # Selector type
        selector_layout.addWidget(QLabel(tr("label_type")))
        self.selector_type_combo = QComboBox()
        self.selector_type_combo.addItems(["xpath", "css", "id", "class", "tag", "text", "attribute"])
        self.selector_type_combo.setMaximumWidth(100)
        selector_layout.addWidget(self.selector_type_combo)

        # Selector value
        selector_layout.addWidget(QLabel(tr("label_selector")))
        self.selector_value_input = QLineEdit()
        self.selector_value_input.setPlaceholderText(tr("placeholder_selector"))
        self.selector_value_input.setStyleSheet(self.get_input_style())
        selector_layout.addWidget(self.selector_value_input)

        layout.addLayout(selector_layout)

        # Options
        options_layout = QHBoxLayout()

        self.multiple_elements_checkbox = QCheckBox(tr("checkbox_multiple"))
        options_layout.addWidget(self.multiple_elements_checkbox)

        self.wait_visible_checkbox = QCheckBox(tr("checkbox_wait_visible"))
        options_layout.addWidget(self.wait_visible_checkbox)

        options_layout.addWidget(QLabel(tr("label_timeout")))
        self.timeout_input = QSpinBox()
        self.timeout_input.setRange(100, 30000)
        self.timeout_input.setValue(5000)
        self.timeout_input.setSuffix(tr("suffix_ms"))
        self.timeout_input.setMaximumWidth(100)
        options_layout.addWidget(self.timeout_input)

        options_layout.addStretch()
        layout.addLayout(options_layout)

        # Action selection section
        action_selection_layout = QHBoxLayout()

        action_selection_layout.addWidget(QLabel(tr("label_action")))
        self.action_type_combo = QComboBox()
        self.action_type_combo.addItems([
            "click", "input", "scroll", "hover", "focus", "blur", "getAttribute", "setText"
        ])
        self.action_type_combo.setMaximumWidth(120)
        self.action_type_combo.currentTextChanged.connect(self.on_action_type_changed)
        action_selection_layout.addWidget(self.action_type_combo)

        action_selection_layout.addStretch()
        layout.addLayout(action_selection_layout)

        # Action parameters section (dynamic based on selected action)
        self.action_params_layout = QVBoxLayout()
        self.create_action_parameters_ui()
        layout.addLayout(self.action_params_layout)

        # Action buttons
        action_layout = QHBoxLayout()

        self.query_element_btn = QPushButton(tr("button_query_element"))
        self.query_element_btn.clicked.connect(self.query_element)
        self.query_element_btn.setStyleSheet(self.get_secondary_button_style())
        action_layout.addWidget(self.query_element_btn)

        self.execute_action_btn = QPushButton(tr("button_execute_action"))
        self.execute_action_btn.clicked.connect(self.execute_element_action)
        self.execute_action_btn.setStyleSheet(self.get_primary_button_style())
        action_layout.addWidget(self.execute_action_btn)

        self.preview_action_btn = QPushButton(tr("button_preview_action"))
        self.preview_action_btn.clicked.connect(self.preview_element_action)
        self.preview_action_btn.setStyleSheet(self.get_secondary_button_style())
        action_layout.addWidget(self.preview_action_btn)

        self.screenshot_element_btn = QPushButton(tr("button_screenshot_element"))
        self.screenshot_element_btn.clicked.connect(self.screenshot_element)
        self.screenshot_element_btn.setStyleSheet(self.get_secondary_button_style())
        action_layout.addWidget(self.screenshot_element_btn)

        action_layout.addStretch()
        layout.addLayout(action_layout)

        parent_layout.addWidget(self.element_query_group)

    def create_action_parameters_ui(self):
        """Create dynamic UI for action parameters"""
        # Clear existing parameter widgets
        self.clear_action_parameters()

        # Initialize parameter input widgets (will be shown/hidden based on action type)
        self.action_param_widgets = {}

        # Text input for input, setText, getAttribute actions
        text_layout = QHBoxLayout()
        text_layout.addWidget(QLabel(tr("label_text")))
        self.text_input = QLineEdit()
        self.text_input.setPlaceholderText(tr("placeholder_text"))
        self.text_input.setStyleSheet(self.get_input_style())
        text_layout.addWidget(self.text_input)
        text_layout.addStretch()

        self.text_param_widget = QWidget()
        self.text_param_widget.setLayout(text_layout)
        self.action_param_widgets['text'] = self.text_param_widget
        self.action_params_layout.addWidget(self.text_param_widget)

        # Coordinate inputs for click action
        coord_layout = QHBoxLayout()
        coord_layout.addWidget(QLabel(tr("label_coordinates")))
        coord_layout.addWidget(QLabel("X:"))
        self.click_x_input = QSpinBox()
        self.click_x_input.setRange(-9999, 9999)
        self.click_x_input.setSpecialValueText(tr("auto"))
        self.click_x_input.setMaximumWidth(80)
        coord_layout.addWidget(self.click_x_input)

        coord_layout.addWidget(QLabel("Y:"))
        self.click_y_input = QSpinBox()
        self.click_y_input.setRange(-9999, 9999)
        self.click_y_input.setSpecialValueText(tr("auto"))
        self.click_y_input.setMaximumWidth(80)
        coord_layout.addWidget(self.click_y_input)
        coord_layout.addStretch()

        self.coord_param_widget = QWidget()
        self.coord_param_widget.setLayout(coord_layout)
        self.action_param_widgets['coordinates'] = self.coord_param_widget
        self.action_params_layout.addWidget(self.coord_param_widget)

        # Scroll inputs for scroll action
        scroll_layout = QHBoxLayout()
        scroll_layout.addWidget(QLabel(tr("label_scroll")))
        scroll_layout.addWidget(QLabel("X:"))
        self.scroll_x_input = QSpinBox()
        self.scroll_x_input.setRange(-9999, 9999)
        self.scroll_x_input.setSpecialValueText(tr("auto"))
        self.scroll_x_input.setMaximumWidth(80)
        scroll_layout.addWidget(self.scroll_x_input)

        scroll_layout.addWidget(QLabel("Y:"))
        self.scroll_y_input = QSpinBox()
        self.scroll_y_input.setRange(-9999, 9999)
        self.scroll_y_input.setSpecialValueText(tr("auto"))
        self.scroll_y_input.setMaximumWidth(80)
        scroll_layout.addWidget(self.scroll_y_input)
        scroll_layout.addStretch()

        self.scroll_param_widget = QWidget()
        self.scroll_param_widget.setLayout(scroll_layout)
        self.action_param_widgets['scroll'] = self.scroll_param_widget
        self.action_params_layout.addWidget(self.scroll_param_widget)

        # Attribute input for getAttribute action
        attr_layout = QHBoxLayout()
        attr_layout.addWidget(QLabel(tr("label_attribute")))
        self.attribute_input = QLineEdit()
        self.attribute_input.setPlaceholderText(tr("placeholder_attribute"))
        self.attribute_input.setStyleSheet(self.get_input_style())
        attr_layout.addWidget(self.attribute_input)
        attr_layout.addStretch()

        self.attr_param_widget = QWidget()
        self.attr_param_widget.setLayout(attr_layout)
        self.action_param_widgets['attribute'] = self.attr_param_widget
        self.action_params_layout.addWidget(self.attr_param_widget)

        # Update visibility based on current action type (default to 'click')
        current_action = self.action_type_combo.currentText() or 'click'
        self.on_action_type_changed(current_action)

    def clear_action_parameters(self):
        """Clear all action parameter widgets"""
        if hasattr(self, 'action_param_widgets'):
            for widget in self.action_param_widgets.values():
                widget.setParent(None)
            self.action_param_widgets.clear()

    def on_action_type_changed(self, action_type):
        """Handle action type selection change"""
        if not hasattr(self, 'action_param_widgets'):
            return

        # Hide all parameter widgets first
        for widget in self.action_param_widgets.values():
            widget.hide()

        # Show relevant parameter widgets based on action type
        if action_type in ['input', 'setText']:
            if 'text' in self.action_param_widgets:
                self.action_param_widgets['text'].show()
        elif action_type == 'click':
            if 'coordinates' in self.action_param_widgets:
                self.action_param_widgets['coordinates'].show()
        elif action_type == 'scroll':
            if 'scroll' in self.action_param_widgets:
                self.action_param_widgets['scroll'].show()
        elif action_type == 'getAttribute':
            if 'attribute' in self.action_param_widgets:
                self.action_param_widgets['attribute'].show()
        # hover, focus, blur don't need additional parameters

    def create_page_info_group(self, parent_layout):
        """Create page information operations group - aligned with browser extension"""
        self.page_info_group = QGroupBox(tr("page_info_group"))
        layout = QHBoxLayout(self.page_info_group)

        # Info type selection
        self.info_all_checkbox = QCheckBox(tr("checkbox_all"))
        self.info_all_checkbox.setChecked(True)
        layout.addWidget(self.info_all_checkbox)

        self.info_url_checkbox = QCheckBox(tr("checkbox_url"))
        layout.addWidget(self.info_url_checkbox)

        self.info_title_checkbox = QCheckBox(tr("checkbox_title"))
        layout.addWidget(self.info_title_checkbox)

        self.info_size_checkbox = QCheckBox(tr("checkbox_size"))
        layout.addWidget(self.info_size_checkbox)

        # Get page info button
        self.get_page_info_btn = QPushButton(tr("button_get_page_info"))
        self.get_page_info_btn.clicked.connect(self.get_page_info)
        self.get_page_info_btn.setStyleSheet(self.get_primary_button_style())
        layout.addWidget(self.get_page_info_btn)

        layout.addStretch()
        parent_layout.addWidget(self.page_info_group)

    def create_connection_group(self, parent_layout):
        """Create connection operations group - aligned with browser extension"""
        self.connection_group = QGroupBox(tr("connection_group"))
        layout = QHBoxLayout(self.connection_group)

        self.ping_btn = QPushButton(tr("button_ping"))
        self.ping_btn.clicked.connect(self.send_ping)
        self.ping_btn.setStyleSheet(self.get_secondary_button_style())
        layout.addWidget(self.ping_btn)

        layout.addStretch()
        parent_layout.addWidget(self.connection_group)

    def get_primary_button_style(self) -> str:
        """Get primary button stylesheet"""
        return """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """

    def get_secondary_button_style(self) -> str:
        """Get secondary button stylesheet"""
        return """
            QPushButton {
                background-color: #f0f0f0;
                color: #333;
                border: 2px solid #ddd;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #bbb;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """

    def get_input_style(self) -> str:
        """Get input field stylesheet"""
        return """
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """

    # Command methods - aligned with browser extension
    def open_chatgpt(self):
        """Open ChatGPT - aligned with browser extension"""
        url = self.chatgpt_url_input.text().strip() or None
        new_tab = self.new_tab_checkbox.isChecked()
        focus = self.focus_checkbox.isChecked()

        message = create_base_message(
            MessageType.OPEN_CHATGPT.value,
            {
                "url": url,
                "newTab": new_tab,
                "focus": focus
            }
        )

        self.command_requested.emit(message.type, message.data)
        self.logger.info(tr("log_open_chatgpt") + f": {url or 'default'}")

    def set_window_size(self):
        """Set browser window size - aligned with browser extension"""
        width = self.window_width_input.value()
        height = self.window_height_input.value()

        message = create_base_message(
            MessageType.SET_WINDOW_SIZE.value,
            {
                "width": width,
                "height": height
            }
        )

        self.command_requested.emit(message.type, message.data)
        self.logger.info(tr("log_set_window_size", width=width, height=height))

    def take_screenshot(self):
        """Take page screenshot - aligned with browser extension"""
        format_type = self.screenshot_format_combo.currentText()
        quality = self.screenshot_quality_input.value()
        full_page = self.full_page_checkbox.isChecked()

        message = create_base_message(
            MessageType.TAKE_SCREENSHOT.value,
            {
                "format": format_type,
                "quality": quality,
                "fullPage": full_page
            }
        )

        self.command_requested.emit(message.type, message.data)
        self.logger.info(tr("log_take_screenshot", format=format_type, quality=quality, full_page=full_page))

    def query_element(self):
        """Query element - aligned with browser extension"""
        selector_type = self.selector_type_combo.currentText()
        selector_value = self.selector_value_input.text().strip()

        if not selector_value:
            self.logger.warning(tr("log_no_selector_value"))
            return

        # Use new Pydantic model to create flat structure command
        from ..models.element_models import create_element_query_command

        command = create_element_query_command(
            selector_type=selector_type,
            selector_value=selector_value,
            options={
                "multiple": self.multiple_elements_checkbox.isChecked(),
                "timeout": self.timeout_input.value(),
                "waitVisible": self.wait_visible_checkbox.isChecked()
            }
        )

        command_dict = command.model_dump()
        self.command_requested.emit(command_dict["type"], command_dict)
        self.logger.info(tr("log_query_element", selector_type=selector_type, selector_value=selector_value))

    def click_element(self):
        """Click element - aligned with browser extension"""
        selector_type = self.selector_type_combo.currentText()
        selector_value = self.selector_value_input.text().strip()

        if not selector_value:
            self.logger.warning(tr("log_no_selector_value"))
            return

        message = create_base_message(
            MessageType.ELEMENT_QUERY.value,
            {
                "selector": {
                    "type": selector_type,
                    "value": selector_value,
                    "options": {
                        "timeout": self.timeout_input.value(),
                        "waitVisible": True
                    }
                },
                "actions": [{
                    "type": "click",
                    "params": {}
                }]
            }
        )

        self.command_requested.emit(message.type, message.data)
        self.logger.info(tr("log_click_element", selector_type=selector_type, selector_value=selector_value))

    def execute_element_action(self):
        """Execute the selected element action with enhanced validation and feedback"""
        selector_type = self.selector_type_combo.currentText()
        selector_value = self.selector_value_input.text().strip()
        action_type = self.action_type_combo.currentText()

        # Validate selector
        if not selector_value:
            self.logger.warning(tr("log_no_selector_value"))
            return

        # Validate action type
        if not action_type:
            self.logger.warning(tr("log_no_action_type"))
            return

        # Collect and validate action parameters based on action type
        action_params = {}
        validation_errors = []

        try:
            if action_type in ['input', 'setText']:
                text = self.text_input.text().strip()
                if not text:
                    validation_errors.append(tr("validation_empty_text", action_type=action_type))
                else:
                    action_params['text'] = text

            elif action_type == 'click':
                # Use coordinates if specified, otherwise use element center
                if self.click_x_input.value() != self.click_x_input.minimum():
                    action_params['x'] = self.click_x_input.value()
                if self.click_y_input.value() != self.click_y_input.minimum():
                    action_params['y'] = self.click_y_input.value()
                # Click action doesn't require parameters, coordinates are optional

            elif action_type == 'scroll':
                # Use scroll values if specified
                if self.scroll_x_input.value() != self.scroll_x_input.minimum():
                    action_params['scrollX'] = self.scroll_x_input.value()
                if self.scroll_y_input.value() != self.scroll_y_input.minimum():
                    action_params['scrollY'] = self.scroll_y_input.value()
                # Scroll action doesn't require parameters, will scroll into view if none specified

            elif action_type == 'getAttribute':
                attribute = self.attribute_input.text().strip()
                if not attribute:
                    validation_errors.append(tr("validation_empty_attribute"))
                elif not self._is_valid_attribute_name(attribute):
                    validation_errors.append(tr("validation_invalid_attribute", attribute=attribute))
                else:
                    action_params['attribute'] = attribute

            elif action_type in ['hover', 'focus', 'blur']:
                # These actions don't require additional parameters
                pass
            else:
                validation_errors.append(tr("validation_unsupported_action", action_type=action_type))

            # Check for validation errors
            if validation_errors:
                for error in validation_errors:
                    self.logger.warning(error)
                return

        except Exception as e:
            self.logger.error(tr("log_parameter_validation_error", error=str(e)))
            return

        # Create element query command with action
        try:
            message = create_base_message(
                MessageType.ELEMENT_QUERY.value,
                {
                    "selector": {
                        "type": selector_type,
                        "value": selector_value,
                        "options": {
                            "timeout": self.timeout_input.value(),
                            "waitVisible": self.wait_visible_checkbox.isChecked()
                        }
                    },
                    "actions": [{
                        "type": action_type,
                        "params": action_params
                    }]
                }
            )

            self.command_requested.emit(message.type, message.data)

            # Enhanced logging with parameter details
            param_summary = self._get_action_param_summary(action_type, action_params)
            self.logger.info(tr("log_execute_action_detailed",
                               action_type=action_type,
                               selector_type=selector_type,
                               selector_value=selector_value,
                               params=param_summary))

        except Exception as e:
            self.logger.error(tr("log_command_creation_error", error=str(e)))

    def _is_valid_attribute_name(self, attribute: str) -> bool:
        """Validate HTML attribute name"""
        import re
        # HTML attribute names should start with a letter and contain only letters, digits, hyphens, periods, and underscores
        return bool(re.match(r'^[a-zA-Z][a-zA-Z0-9\-\._]*$', attribute))

    def _get_action_param_summary(self, action_type: str, params: dict) -> str:
        """Get a human-readable summary of action parameters"""
        if not params:
            return tr("param_summary_none")

        if action_type in ['input', 'setText']:
            text = params.get('text', '')
            return tr("param_summary_text", text=text[:50] + ('...' if len(text) > 50 else ''))
        elif action_type == 'click':
            if 'x' in params or 'y' in params:
                x = params.get('x', 'auto')
                y = params.get('y', 'auto')
                return tr("param_summary_coordinates", x=x, y=y)
            else:
                return tr("param_summary_click_center")
        elif action_type == 'scroll':
            if 'scrollX' in params or 'scrollY' in params:
                x = params.get('scrollX', 0)
                y = params.get('scrollY', 0)
                return tr("param_summary_scroll", x=x, y=y)
            else:
                return tr("param_summary_scroll_into_view")
        elif action_type == 'getAttribute':
            return tr("param_summary_attribute", attribute=params.get('attribute', ''))
        else:
            return tr("param_summary_none")

    def preview_element_action(self):
        """Preview the selected element action without executing it"""
        try:
            selector_type = self.selector_type_combo.currentText()
            selector_value = self.selector_value_input.text().strip()
            action_type = self.action_type_combo.currentText()

            if not selector_value:
                self.logger.warning(tr("log_no_selector_value"))
                return

            if not action_type:
                self.logger.warning(tr("log_no_action_type"))
                return

            # Build enhanced preview message
            preview_parts = [tr("preview_action_label", action_type=action_type)]
            preview_parts.append(tr("preview_selector_label", selector_type=selector_type, selector_value=selector_value))

            # Add parameter details with validation warnings
            validation_warnings = []

            if action_type in ['input', 'setText']:
                text = self.text_input.text().strip()
                if text:
                    preview_parts.append(tr("preview_text_label", text=text[:50] + ('...' if len(text) > 50 else '')))
                else:
                    validation_warnings.append(tr("preview_warning_empty_text"))

            elif action_type == 'click':
                if (self.click_x_input.value() != self.click_x_input.minimum() or
                    self.click_y_input.value() != self.click_y_input.minimum()):
                    x = self.click_x_input.value() if self.click_x_input.value() != self.click_x_input.minimum() else "auto"
                    y = self.click_y_input.value() if self.click_y_input.value() != self.click_y_input.minimum() else "auto"
                    preview_parts.append(tr("preview_coordinates_label", x=x, y=y))
                else:
                    preview_parts.append(tr("preview_click_center_label"))

            elif action_type == 'scroll':
                if (self.scroll_x_input.value() != self.scroll_x_input.minimum() or
                    self.scroll_y_input.value() != self.scroll_y_input.minimum()):
                    x = self.scroll_x_input.value() if self.scroll_x_input.value() != self.scroll_x_input.minimum() else 0
                    y = self.scroll_y_input.value() if self.scroll_y_input.value() != self.scroll_y_input.minimum() else 0
                    preview_parts.append(tr("preview_scroll_label", x=x, y=y))
                else:
                    preview_parts.append(tr("preview_scroll_into_view_label"))

            elif action_type == 'getAttribute':
                attribute = self.attribute_input.text().strip()
                if attribute:
                    if self._is_valid_attribute_name(attribute):
                        preview_parts.append(tr("preview_attribute_label", attribute=attribute))
                    else:
                        validation_warnings.append(tr("preview_warning_invalid_attribute", attribute=attribute))
                else:
                    validation_warnings.append(tr("preview_warning_empty_attribute"))

            elif action_type in ['hover', 'focus', 'blur']:
                preview_parts.append(tr("preview_no_params_label"))

            # Build final preview message
            preview_message = " | ".join(preview_parts)

            if validation_warnings:
                preview_message += " | " + tr("preview_warnings_label") + ": " + "; ".join(validation_warnings)

            self.logger.info(tr("log_preview_action", preview=preview_message))

        except Exception as e:
            self.logger.error(tr("log_preview_error", error=str(e)))

    def screenshot_element(self):
        """Screenshot element - aligned with browser extension"""
        selector_type = self.selector_type_combo.currentText()
        selector_value = self.selector_value_input.text().strip()

        if not selector_value:
            self.logger.warning(tr("log_no_selector_value"))
            return

        # Use new Pydantic model to create flat structure command
        from ..models.element_models import create_element_screenshot_command

        command = create_element_screenshot_command(
            selector_type=selector_type,
            selector_value=selector_value,
            options={
                "format": "png",
                "quality": 1.0
            }
        )

        command_dict = command.model_dump()
        self.command_requested.emit(command_dict["type"], command_dict)
        self.logger.info(tr("log_screenshot_element", selector_type=selector_type, selector_value=selector_value))

    def get_page_info(self):
        """Get page information - aligned with browser extension"""
        info_types = []

        if self.info_all_checkbox.isChecked():
            info_types = ["all"]
        else:
            if self.info_url_checkbox.isChecked():
                info_types.append("url")
            if self.info_title_checkbox.isChecked():
                info_types.append("title")
            if self.info_size_checkbox.isChecked():
                info_types.append("size")

        if not info_types:
            info_types = ["all"]

        # Use new Pydantic model to create flat structure command
        from ..models.element_models import create_page_info_command

        command = create_page_info_command(info_types=info_types)
        command_dict = command.model_dump()

        self.command_requested.emit(command_dict["type"], command_dict)
        self.logger.info(tr("log_get_page_info", info_types=', '.join(info_types)))

    def send_ping(self):
        """Send ping - aligned with browser extension"""
        message = create_base_message(MessageType.PING.value, {})
        self.command_requested.emit(message.type, message.data)
        self.logger.info(tr("log_ping_sent"))

    def on_image_preview_error(self, error_message: str):
        """Handle image preview errors"""
        self.logger.error(tr("log_image_preview_error", error=error_message))

    def handle_image_response(self, message_data: dict):
        """Handle image response from WebSocket messages"""
        try:
            # Check if message contains image data
            data = message_data.get("data", {})

            # Handle screenshot responses
            if "dataUrl" in data:
                image_data = data["dataUrl"]
                image_format = "png"  # Default format

                # Try to detect format from data URL
                if image_data.startswith("data:image/"):
                    format_part = image_data.split(";")[0].split("/")[1]
                    if format_part in ["png", "jpeg", "jpg", "gif", "webp"]:
                        image_format = format_part

                self.image_preview.set_image_from_base64(image_data, image_format)
                self.logger.info(tr("log_image_loaded", format=image_format))

            # Handle base64 image data directly
            elif "image" in data or "screenshot" in data:
                image_data = data.get("image") or data.get("screenshot")
                if image_data:
                    image_format = data.get("format", "png")
                    self.image_preview.set_image_from_base64(image_data, image_format)
                    self.logger.info(tr("log_image_loaded", format=image_format))

            # Handle element screenshots in element query results
            elif "elements" in data:
                # Check if any elements contain screenshot data
                elements = data.get("elements", [])
                for element in elements:
                    if isinstance(element, dict):
                        # Look for screenshot data in element
                        for key in ["dataUrl", "screenshot", "image"]:
                            if key in element:
                                image_data = element[key]
                                if isinstance(image_data, str) and len(image_data) > 100:
                                    image_format = "png"
                                    if image_data.startswith("data:image/"):
                                        format_part = image_data.split(";")[0].split("/")[1]
                                        if format_part in ["png", "jpeg", "jpg", "gif", "webp"]:
                                            image_format = format_part

                                    self.image_preview.set_image_from_base64(image_data, image_format)
                                    self.logger.info(tr("log_image_loaded", format=image_format))
                                    return  # Only load the first image found

        except Exception as e:
            self.logger.error(tr("log_image_response_error", error=str(e)))

    def handle_websocket_message(self, message_type: str, message_data: dict):
        """Handle incoming WebSocket messages that might contain images"""
        # Check for image-containing message types
        image_message_types = [
            "TAKE_SCREENSHOT",
            "ELEMENT_SCREENSHOT",
            "BROWSER_CONTROL_RESPONSE",
            "ELEMENT_SCREENSHOT_RESULT",
            "ELEMENT_QUERY_RESPONSE",
            "ELEMENT_QUERY_RESULT",
            "element_query_result",  # Server handler message type
            "browser_control_response"  # Server handler message type
        ]

        self.logger.debug(f"Received message type: {message_type}")
        if message_type.upper() in [t.upper() for t in image_message_types] or message_type in image_message_types:
            self.logger.debug(f"Handling image response for message type: {message_type}")
            self.handle_image_response(message_data)

    def retranslate_ui(self):
        """Retranslate UI elements - updated for browser extension compatibility"""
        # Update group titles
        self.browser_control_group.setTitle(tr("browser_control_group"))
        self.element_query_group.setTitle(tr("element_query_group"))
        self.page_info_group.setTitle(tr("page_info_group"))
        self.connection_group.setTitle(tr("connection_group"))

        # Update placeholders
        self.chatgpt_url_input.setPlaceholderText(tr("placeholder_chatgpt_url"))
        self.selector_value_input.setPlaceholderText(tr("placeholder_selector"))
        if hasattr(self, 'text_input'):
            self.text_input.setPlaceholderText(tr("placeholder_text"))
        if hasattr(self, 'attribute_input'):
            self.attribute_input.setPlaceholderText(tr("placeholder_attribute"))

        # Update buttons
        self.open_chatgpt_btn.setText(tr("button_open_chatgpt"))
        self.set_window_size_btn.setText(tr("button_set_window_size"))
        self.take_screenshot_btn.setText(tr("button_take_screenshot"))
        self.query_element_btn.setText(tr("button_query_element"))
        self.execute_action_btn.setText(tr("button_execute_action"))
        self.preview_action_btn.setText(tr("button_preview_action"))
        self.screenshot_element_btn.setText(tr("button_screenshot_element"))
        self.get_page_info_btn.setText(tr("button_get_page_info"))
        self.ping_btn.setText(tr("button_ping"))

        # Update checkboxes
        self.new_tab_checkbox.setText(tr("checkbox_new_tab"))
        self.focus_checkbox.setText(tr("checkbox_focus"))
        self.full_page_checkbox.setText(tr("checkbox_full_page"))
        self.multiple_elements_checkbox.setText(tr("checkbox_multiple"))
        self.wait_visible_checkbox.setText(tr("checkbox_wait_visible"))
        self.info_all_checkbox.setText(tr("checkbox_all"))
        self.info_url_checkbox.setText(tr("checkbox_url"))
        self.info_title_checkbox.setText(tr("checkbox_title"))
        self.info_size_checkbox.setText(tr("checkbox_size"))

        # Update timeout suffix
        self.timeout_input.setSuffix(tr("suffix_ms"))

        # Update image preview
        if hasattr(self, 'image_preview'):
            self.image_preview.retranslate_ui()
