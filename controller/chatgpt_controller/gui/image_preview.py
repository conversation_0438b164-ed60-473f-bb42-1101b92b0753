"""
Image Preview Widget for ChatGPT Controller GUI

Provides image preview functionality for screenshots and other image responses.
"""

import base64
import os
from io import BytesIO
from typing import Optional, Dict, Any
from PyQt5.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
    QGroupBox,
    QFileDialog,
    QMessageBox,
    QSizePolicy,
    QFrame,
    QButtonGroup,
    QToolButton,
    QSpacerItem,
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QTimer
from PyQt5.QtGui import QPixmap, QIcon, QClipboard, QPainter, QFont, QResizeEvent

from ..utils.logging import RichLogger
from .i18n import tr


class ImagePreviewWidget(QWidget):
    """Image preview widget with zoom and save functionality"""

    # Signals
    image_saved = pyqtSignal(str)  # path
    image_copied = pyqtSignal()
    error_occurred = pyqtSignal(str)  # error message

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = RichLogger(__name__)
        
        # Image data
        self.current_pixmap: Optional[QPixmap] = None
        self.original_pixmap: Optional[QPixmap] = None
        self.current_scale = 1.0
        self.image_format = "png"
        self.auto_fit_enabled = True  # Track if auto-fit is enabled
        
        # UI components
        self.image_label: Optional[QLabel] = None
        self.scroll_area: Optional[QScrollArea] = None
        self.info_label: Optional[QLabel] = None

        # Timer for delayed resize handling
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self._handle_delayed_resize)

        self.setup_ui()

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Create group box
        self.group_box = QGroupBox(tr("image_preview_group"))
        group_layout = QVBoxLayout(self.group_box)
        
        # Toolbar
        self.create_toolbar(group_layout)
        
        # Image display area
        self.create_image_area(group_layout)
        
        # Info area
        self.create_info_area(group_layout)
        
        layout.addWidget(self.group_box)

    def create_toolbar(self, parent_layout):
        """Create toolbar with image controls"""
        toolbar_layout = QHBoxLayout()
        
        # Save button
        self.save_btn = QPushButton(tr("save_image"))
        self.save_btn.clicked.connect(self.save_image)
        self.save_btn.setEnabled(False)
        toolbar_layout.addWidget(self.save_btn)
        
        # Copy button
        self.copy_btn = QPushButton(tr("copy_image"))
        self.copy_btn.clicked.connect(self.copy_image)
        self.copy_btn.setEnabled(False)
        toolbar_layout.addWidget(self.copy_btn)
        
        # Clear button
        self.clear_btn = QPushButton(tr("clear_image"))
        self.clear_btn.clicked.connect(self.clear_image)
        self.clear_btn.setEnabled(False)
        toolbar_layout.addWidget(self.clear_btn)
        
        # Separator
        toolbar_layout.addItem(QSpacerItem(20, 0, QSizePolicy.Fixed, QSizePolicy.Minimum))
        
        # Zoom controls
        self.zoom_in_btn = QToolButton()
        self.zoom_in_btn.setText("+")
        self.zoom_in_btn.setToolTip(tr("zoom_in"))
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        self.zoom_in_btn.setEnabled(False)
        toolbar_layout.addWidget(self.zoom_in_btn)
        
        self.zoom_out_btn = QToolButton()
        self.zoom_out_btn.setText("-")
        self.zoom_out_btn.setToolTip(tr("zoom_out"))
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        self.zoom_out_btn.setEnabled(False)
        toolbar_layout.addWidget(self.zoom_out_btn)
        
        self.zoom_reset_btn = QToolButton()
        self.zoom_reset_btn.setText("1:1")
        self.zoom_reset_btn.setToolTip(tr("actual_size"))
        self.zoom_reset_btn.clicked.connect(self.zoom_reset)
        self.zoom_reset_btn.setEnabled(False)
        toolbar_layout.addWidget(self.zoom_reset_btn)
        
        self.fit_window_btn = QToolButton()
        self.fit_window_btn.setText("Fit")
        self.fit_window_btn.setToolTip(tr("fit_to_window"))
        self.fit_window_btn.clicked.connect(self.fit_to_window)
        self.fit_window_btn.setEnabled(False)
        toolbar_layout.addWidget(self.fit_window_btn)
        
        # Stretch to push buttons to the left
        toolbar_layout.addStretch()
        
        parent_layout.addLayout(toolbar_layout)

    def create_image_area(self, parent_layout):
        """Create scrollable image display area"""
        # Create scroll area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignCenter)
        self.scroll_area.setMinimumHeight(300)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: 2px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
            }
        """)
        
        # Create image label
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(200, 200)
        self.image_label.setText(tr("no_image_available"))
        self.image_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 14px;
                background-color: white;
                border: 1px dashed #ccc;
            }
        """)
        
        self.scroll_area.setWidget(self.image_label)
        parent_layout.addWidget(self.scroll_area)

    def create_info_area(self, parent_layout):
        """Create image information area"""
        self.info_label = QLabel()
        self.info_label.setAlignment(Qt.AlignLeft)
        self.info_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 11px;
                padding: 4px;
                background-color: #f0f0f0;
                border-radius: 2px;
            }
        """)
        self.update_info_label()
        parent_layout.addWidget(self.info_label)

    def set_image_from_base64(self, base64_data: str, image_format: str = "png"):
        """Set image from base64 data"""
        try:
            # Remove data URL prefix if present
            if base64_data.startswith('data:image/'):
                base64_data = base64_data.split(',', 1)[1]
            
            # Decode base64 data
            image_data = base64.b64decode(base64_data)
            
            # Create pixmap from data
            pixmap = QPixmap()
            if pixmap.loadFromData(image_data):
                self.set_image(pixmap, image_format)
                self.logger.info(f"Image loaded from base64 data, format: {image_format}")
            else:
                self.error_occurred.emit(tr("invalid_image_data"))
                self.logger.error("Failed to load image from base64 data")
                
        except Exception as e:
            error_msg = tr("image_load_error", error=str(e))
            self.error_occurred.emit(error_msg)
            self.logger.error(f"Error loading image from base64: {e}")

    def set_image(self, pixmap: QPixmap, image_format: str = "png"):
        """Set image from QPixmap"""
        self.original_pixmap = pixmap.copy()
        self.current_pixmap = pixmap.copy()
        self.image_format = image_format
        self.current_scale = 1.0

        # Enable controls first so fit_to_window can work
        self.enable_controls(True)

        # Fit to window by default
        self.fit_to_window()

        # Update info label after fitting
        self.update_info_label()

    def update_image_display(self):
        """Update the image display"""
        if self.current_pixmap and self.image_label:
            self.image_label.setPixmap(self.current_pixmap)
            self.image_label.resize(self.current_pixmap.size())
            self.image_label.setStyleSheet("QLabel { border: none; background-color: white; }")

    def update_info_label(self):
        """Update image information label"""
        if self.original_pixmap:
            width = self.original_pixmap.width()
            height = self.original_pixmap.height()
            size_info = tr("image_size_info", width=width, height=height)
            format_info = tr("image_format_info", format=self.image_format.upper())
            zoom_info = f"Zoom: {self.current_scale:.0%}"
            self.info_label.setText(f"{size_info} | {format_info} | {zoom_info}")
        else:
            self.info_label.setText(tr("no_image_available"))

    def enable_controls(self, enabled: bool):
        """Enable or disable image controls"""
        self.save_btn.setEnabled(enabled)
        self.copy_btn.setEnabled(enabled)
        self.clear_btn.setEnabled(enabled)
        self.zoom_in_btn.setEnabled(enabled)
        self.zoom_out_btn.setEnabled(enabled)
        self.zoom_reset_btn.setEnabled(enabled)
        self.fit_window_btn.setEnabled(enabled)

    def zoom_in(self):
        """Zoom in the image"""
        if self.original_pixmap:
            self.auto_fit_enabled = False  # Disable auto-fit when manually zooming
            self.current_scale = min(self.current_scale * 1.25, 5.0)
            self.apply_zoom()

    def zoom_out(self):
        """Zoom out the image"""
        if self.original_pixmap:
            self.auto_fit_enabled = False  # Disable auto-fit when manually zooming
            self.current_scale = max(self.current_scale / 1.25, 0.1)
            self.apply_zoom()

    def zoom_reset(self):
        """Reset zoom to actual size"""
        if self.original_pixmap:
            self.auto_fit_enabled = False  # Disable auto-fit when manually zooming
            self.current_scale = 1.0
            self.apply_zoom()

    def fit_to_window(self):
        """Fit image to window size"""
        if self.original_pixmap and self.scroll_area:
            self.auto_fit_enabled = True  # Re-enable auto-fit when explicitly called
            available_size = self.scroll_area.size()
            image_size = self.original_pixmap.size()

            # If scroll area hasn't been sized yet, use minimum size as fallback
            if available_size.width() <= 0 or available_size.height() <= 0:
                available_size = QSize(self.scroll_area.minimumWidth() or 400,
                                     self.scroll_area.minimumHeight() or 300)

            # Calculate scale to fit
            scale_x = available_size.width() / image_size.width()
            scale_y = available_size.height() / image_size.height()
            self.current_scale = min(scale_x, scale_y, 1.0)  # Don't scale up

            self.apply_zoom()

    def apply_zoom(self):
        """Apply current zoom scale to image"""
        if self.original_pixmap:
            new_size = self.original_pixmap.size() * self.current_scale
            self.current_pixmap = self.original_pixmap.scaled(
                new_size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )
            self.update_image_display()
            self.update_info_label()

    def save_image(self):
        """Save current image to file"""
        if not self.original_pixmap:
            return
            
        try:
            # Get save path from user
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                tr("save_image"),
                f"screenshot.{self.image_format}",
                f"Image Files (*.{self.image_format} *.png *.jpg *.jpeg);;All Files (*)"
            )
            
            if file_path:
                # Save the original pixmap
                if self.original_pixmap.save(file_path):
                    self.image_saved.emit(file_path)
                    self.logger.success(f"Image saved to: {file_path}")
                    QMessageBox.information(
                        self, 
                        tr("save_image"), 
                        tr("image_saved", path=file_path)
                    )
                else:
                    error_msg = "Failed to save image file"
                    self.error_occurred.emit(tr("save_image_error", error=error_msg))
                    
        except Exception as e:
            error_msg = tr("save_image_error", error=str(e))
            self.error_occurred.emit(error_msg)
            self.logger.error(f"Error saving image: {e}")

    def copy_image(self):
        """Copy image to clipboard"""
        if not self.original_pixmap:
            return
            
        try:
            clipboard = QClipboard()
            clipboard.setPixmap(self.original_pixmap)
            self.image_copied.emit()
            self.logger.info("Image copied to clipboard")
            
        except Exception as e:
            error_msg = tr("copy_image_error", error=str(e))
            self.error_occurred.emit(error_msg)
            self.logger.error(f"Error copying image: {e}")

    def clear_image(self):
        """Clear current image"""
        self.original_pixmap = None
        self.current_pixmap = None
        self.current_scale = 1.0
        self.auto_fit_enabled = True  # Reset auto-fit for next image
        
        # Reset display
        self.image_label.clear()
        self.image_label.setText(tr("no_image_available"))
        self.image_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 14px;
                background-color: white;
                border: 1px dashed #ccc;
            }
        """)
        
        # Update info and disable controls
        self.update_info_label()
        self.enable_controls(False)
        
        self.logger.info("Image preview cleared")

    def resizeEvent(self, event: QResizeEvent):
        """Handle widget resize events"""
        super().resizeEvent(event)

        # If auto-fit is enabled and we have an image, schedule a delayed re-fit
        if self.auto_fit_enabled and self.original_pixmap:
            self.resize_timer.start(100)  # 100ms delay to avoid excessive updates

    def _handle_delayed_resize(self):
        """Handle delayed resize to re-fit image if auto-fit is enabled"""
        if self.auto_fit_enabled and self.original_pixmap:
            self.fit_to_window()

    def retranslate_ui(self):
        """Retranslate UI elements"""
        self.group_box.setTitle(tr("image_preview_group"))
        self.save_btn.setText(tr("save_image"))
        self.copy_btn.setText(tr("copy_image"))
        self.clear_btn.setText(tr("clear_image"))
        self.zoom_in_btn.setToolTip(tr("zoom_in"))
        self.zoom_out_btn.setToolTip(tr("zoom_out"))
        self.zoom_reset_btn.setToolTip(tr("actual_size"))
        self.fit_window_btn.setToolTip(tr("fit_to_window"))
        
        if not self.original_pixmap:
            self.image_label.setText(tr("no_image_available"))
        
        self.update_info_label()
