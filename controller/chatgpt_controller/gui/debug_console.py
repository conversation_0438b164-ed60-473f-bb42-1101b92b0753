"""
Debug Console for ChatGPT Controller GUI

Rich-formatted debug console for monitoring WebSocket activities and events.
Aligned with Chrome plugin WebSocket message types.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, Set

from PyQt5.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTextEdit,
    QPushButton,
    QComboBox,
    QLabel,
    QCheckBox,
    QSplitter,
    QGroupBox,
    QTreeWidget,
    QTreeWidgetItem,
    QTabWidget,
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QTextCharFormat, QColor, QTextCursor

from ..utils.logging import RichLogger
from ..utils.helpers import format_timestamp, truncate_string


class DebugConsole(QWidget):
    """Rich debug console widget - aligned with Chrome plugin WebSocket types"""

    # Signals
    clear_requested = pyqtSignal()
    export_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = RichLogger(__name__)
        self.max_messages = 1000
        self.message_count = 0

        # Message type mappings from Chrome plugin
        self.setup_message_type_mappings()

        self.setup_ui()
        self.setup_styles()

    def setup_message_type_mappings(self):
        """Setup message type mappings aligned with Chrome plugin WebSocket types"""
        # Connection management types
        self.connection_types = {
            "CONNECT", "DISCONNECT", "PING", "PONG", "ERROR"
        }

        # Browser control command types
        self.browser_control_types = {
            "OPEN_CHATGPT", "SET_WINDOW_SIZE", "TAKE_SCREENSHOT"
        }

        # Element query command types
        self.element_query_types = {
            "ELEMENT_QUERY", "PAGE_INFO", "ELEMENT_WATCH",
            "BATCH_ELEMENT_QUERY", "ELEMENT_SCREENSHOT"
        }

        # Response message types
        self.response_types = {
            "ELEMENT_QUERY_RESPONSE", "ELEMENT_QUERY_RESULT",
            "ELEMENT_ACTION_RESULT", "PAGE_INFO_RESULT",
            "ELEMENT_WATCH_RESULT", "BATCH_ELEMENT_QUERY_RESULT",
            "ELEMENT_SCREENSHOT_RESULT", "ELEMENT_EVENT_MESSAGE",
            "BROWSER_CONTROL_RESPONSE"
        }

        # Data forwarding types (from server handlers)
        self.data_forward_types = {
            "chatgpt_message", "monitored_api_request", "monitored_api_headers",
            "monitored_api_response_headers", "monitored_api_completed",
            "monitored_api_response_data", "user_info", "chat_requirements",
            "browser_control_response"
        }

        # All known types
        self.all_known_types = (
            self.connection_types | self.browser_control_types |
            self.element_query_types | self.response_types |
            self.data_forward_types
        )

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)

        # Create tab widget for different views
        self.tab_widget = QTabWidget()

        # Console tab
        self.create_console_tab()

        # Messages tab
        self.create_messages_tab()

        # Events tab
        self.create_events_tab()

        layout.addWidget(self.tab_widget)

        # Control panel
        self.create_control_panel(layout)

    def create_console_tab(self):
        """Create the main console tab"""
        console_widget = QWidget()
        layout = QVBoxLayout(console_widget)

        # Filter controls
        filter_layout = QHBoxLayout()

        filter_layout.addWidget(QLabel("Filter:"))

        self.filter_combo = QComboBox()
        self.filter_combo.addItems(
            [
                "All",
                "Connection",
                "Browser Control",
                "Element Query",
                "API Monitoring",
                "ChatGPT Messages",
                "User Info",
                "Responses",
                "Errors",
                "Unknown",
            ]
        )
        self.filter_combo.currentTextChanged.connect(self.apply_filter)
        filter_layout.addWidget(self.filter_combo)

        self.auto_scroll_cb = QCheckBox("Auto-scroll")
        self.auto_scroll_cb.setChecked(True)
        filter_layout.addWidget(self.auto_scroll_cb)

        filter_layout.addStretch()
        layout.addLayout(filter_layout)

        # Main console display
        self.console_display = QTextEdit()
        self.console_display.setReadOnly(True)
        self.console_display.setFont(QFont("Consolas", 9))
        layout.addWidget(self.console_display)

        self.tab_widget.addTab(console_widget, "Console")

    def create_messages_tab(self):
        """Create the messages tab"""
        messages_widget = QWidget()
        layout = QVBoxLayout(messages_widget)

        # Splitter for message list and details
        splitter = QSplitter(Qt.Horizontal)

        # Message list
        self.message_tree = QTreeWidget()
        self.message_tree.setHeaderLabels(["Time", "Type", "Direction", "Client"])
        self.message_tree.itemClicked.connect(self.show_message_details)
        splitter.addWidget(self.message_tree)

        # Message details
        self.message_details = QTextEdit()
        self.message_details.setReadOnly(True)
        self.message_details.setFont(QFont("Consolas", 9))
        splitter.addWidget(self.message_details)

        splitter.setSizes([400, 600])
        layout.addWidget(splitter)

        self.tab_widget.addTab(messages_widget, "Messages")

    def create_events_tab(self):
        """Create the events tab"""
        events_widget = QWidget()
        layout = QVBoxLayout(events_widget)

        # Events list
        self.events_tree = QTreeWidget()
        self.events_tree.setHeaderLabels(["Time", "Event Type", "Source", "Details"])
        self.events_tree.itemClicked.connect(self.show_event_details)
        layout.addWidget(self.events_tree)

        # Event details
        self.event_details = QTextEdit()
        self.event_details.setReadOnly(True)
        self.event_details.setFont(QFont("Consolas", 9))
        layout.addWidget(self.event_details)

        self.tab_widget.addTab(events_widget, "Events")

    def create_control_panel(self, parent_layout):
        """Create the control panel"""
        control_layout = QHBoxLayout()

        # Clear button
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self.clear)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        control_layout.addWidget(self.clear_btn)

        # Export button
        self.export_btn = QPushButton("Export")
        self.export_btn.clicked.connect(self.export_requested.emit)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        control_layout.addWidget(self.export_btn)

        # Message count label
        self.message_count_label = QLabel("Messages: 0")
        self.message_count_label.setStyleSheet("font-weight: bold; color: #666;")
        control_layout.addWidget(self.message_count_label)

        control_layout.addStretch()
        parent_layout.addLayout(control_layout)

    def setup_styles(self):
        """Setup text formatting styles - aligned with message categories"""
        self.styles = {
            "timestamp": QTextCharFormat(),
            "connection": QTextCharFormat(),
            "browser_control": QTextCharFormat(),
            "element_query": QTextCharFormat(),
            "api_monitoring": QTextCharFormat(),
            "chatgpt": QTextCharFormat(),
            "user_info": QTextCharFormat(),
            "response": QTextCharFormat(),
            "error": QTextCharFormat(),
            "unknown": QTextCharFormat(),
            "event": QTextCharFormat(),
            "info": QTextCharFormat(),
        }

        # Timestamp style
        self.styles["timestamp"].setForeground(QColor("#666666"))

        # Connection management style (blue)
        self.styles["connection"].setForeground(QColor("#2196F3"))
        self.styles["connection"].setFontWeight(QFont.Bold)

        # Browser control style (purple)
        self.styles["browser_control"].setForeground(QColor("#9C27B0"))
        self.styles["browser_control"].setFontWeight(QFont.Bold)

        # Element query style (teal)
        self.styles["element_query"].setForeground(QColor("#009688"))
        self.styles["element_query"].setFontWeight(QFont.Bold)

        # API monitoring style (orange)
        self.styles["api_monitoring"].setForeground(QColor("#FF9800"))

        # ChatGPT messages style (green)
        self.styles["chatgpt"].setForeground(QColor("#4CAF50"))
        self.styles["chatgpt"].setFontWeight(QFont.Bold)

        # User info style (indigo)
        self.styles["user_info"].setForeground(QColor("#3F51B5"))

        # Response style (cyan)
        self.styles["response"].setForeground(QColor("#00BCD4"))

        # Error style (red)
        self.styles["error"].setForeground(QColor("#F44336"))
        self.styles["error"].setFontWeight(QFont.Bold)

        # Unknown style (gray)
        self.styles["unknown"].setForeground(QColor("#9E9E9E"))

        # Event style (blue)
        self.styles["event"].setForeground(QColor("#2196F3"))

        # Info style (light blue)
        self.styles["info"].setForeground(QColor("#2196F3"))

    def log_event(self, event_type: str, data: Dict[str, Any]):
        """Log an event"""
        timestamp = datetime.now()

        # Add to console
        self.append_to_console(f"🔔 {event_type}", data, "event")

        # Add to events tree
        item = QTreeWidgetItem(
            [
                format_timestamp(timestamp),
                event_type,
                data.get("source", "System"),
                truncate_string(str(data), 50),
            ]
        )
        item.setData(0, Qt.UserRole, data)
        self.events_tree.addTopLevelItem(item)

        # Limit items
        if self.events_tree.topLevelItemCount() > self.max_messages:
            self.events_tree.takeTopLevelItem(0)

    def log_message(self, direction: str, data: Dict[str, Any]):
        """Log a message - aligned with Chrome plugin WebSocket types"""
        timestamp = datetime.now()

        # Extract message type from the correct field
        # Server handlers use 'message_type', WebSocket messages use 'type'
        message_type = data.get("message_type") or data.get("type", "UNKNOWN")
        client_id = data.get("client_id", "unknown")

        # Categorize message type
        category = self.categorize_message_type(message_type)
        display_name = self.get_message_type_display_name(message_type)

        # Add to console
        icon = "📤" if direction == "OUTGOING" else "📥"
        self.append_to_console(f"{icon} {direction} {display_name}", data, category)

        # Add to messages tree
        item = QTreeWidgetItem(
            [format_timestamp(timestamp), display_name, direction, client_id]
        )
        item.setData(0, Qt.UserRole, data)
        item.setData(1, Qt.UserRole, category)  # Store category for filtering
        self.message_tree.addTopLevelItem(item)

        # Limit items
        if self.message_tree.topLevelItemCount() > self.max_messages:
            self.message_tree.takeTopLevelItem(0)

        self.message_count += 1
        self.message_count_label.setText(f"Messages: {self.message_count}")

    def categorize_message_type(self, message_type: str) -> str:
        """Categorize message type for filtering and styling"""
        if message_type in self.connection_types:
            return "connection"
        elif message_type in self.browser_control_types:
            return "browser_control"
        elif message_type in self.element_query_types:
            return "element_query"
        elif message_type in self.response_types:
            return "response"
        elif message_type == "chatgpt_message":
            return "chatgpt"
        elif message_type in ["monitored_api_request", "monitored_api_headers",
                             "monitored_api_response_headers", "monitored_api_completed",
                             "monitored_api_response_data"]:
            return "api_monitoring"
        elif message_type in ["element_query_result", "element_event"]:
            return "element_query"
        elif message_type in ["user_info", "chat_requirements"]:
            return "user_info"
        elif message_type == "ERROR" or "error" in message_type.lower():
            return "error"
        else:
            return "unknown"

    def get_message_type_display_name(self, message_type: str) -> str:
        """Get user-friendly display name for message type"""
        display_names = {
            # Connection types
            "CONNECT": "Connect",
            "DISCONNECT": "Disconnect",
            "PING": "Ping",
            "PONG": "Pong",
            "ERROR": "Error",

            # Browser control types
            "OPEN_CHATGPT": "Open ChatGPT",
            "SET_WINDOW_SIZE": "Set Window Size",
            "TAKE_SCREENSHOT": "Take Screenshot",

            # Element query types
            "ELEMENT_QUERY": "Element Query",
            "PAGE_INFO": "Page Info",
            "ELEMENT_WATCH": "Element Watch",
            "BATCH_ELEMENT_QUERY": "Batch Element Query",
            "ELEMENT_SCREENSHOT": "Element Screenshot",

            # Response types
            "ELEMENT_QUERY_RESPONSE": "Element Query Response",
            "ELEMENT_QUERY_RESULT": "Element Query Result",
            "ELEMENT_ACTION_RESULT": "Element Action Result",
            "PAGE_INFO_RESULT": "Page Info Result",
            "ELEMENT_WATCH_RESULT": "Element Watch Result",
            "BATCH_ELEMENT_QUERY_RESULT": "Batch Element Query Result",
            "ELEMENT_SCREENSHOT_RESULT": "Element Screenshot Result",
            "ELEMENT_EVENT_MESSAGE": "Element Event Message",
            "BROWSER_CONTROL_RESPONSE": "Browser Control Response",

            # Data forwarding types
            "chatgpt_message": "ChatGPT Message",
            "monitored_api_request": "API Request",
            "monitored_api_headers": "API Headers",
            "monitored_api_response_headers": "API Response Headers",
            "monitored_api_completed": "API Completed",
            "monitored_api_response_data": "API Response Data",
            "user_info": "User Info",
            "chat_requirements": "Chat Requirements",
            "browser_control_response": "Browser Control Response",
            "element_query_result": "Element Query Result",
            "element_event": "Element Event",
        }

        return display_names.get(message_type, message_type)

    def append_to_console(self, title: str, data: Dict[str, Any], category: str):
        """Append formatted text to console"""
        cursor = self.console_display.textCursor()
        cursor.movePosition(QTextCursor.End)

        # Timestamp
        cursor.insertText(f"[{format_timestamp()}] ", self.styles["timestamp"])

        # Title with appropriate style
        style = self.styles.get(category, self.styles["info"])
        cursor.insertText(f"{title}\n", style)

        # Handle image data specially
        display_data = self.prepare_display_data(data)

        # Determine if we should show full data or truncate
        should_show_full = self._should_show_full_data(data, category)

        # Data display logic
        if display_data and should_show_full:
            cursor.insertText(
                f"  {json.dumps(display_data, indent=2)}\n\n", self.styles["info"]
            )
        elif display_data and len(str(display_data)) < 500:
            cursor.insertText(
                f"  {json.dumps(display_data, indent=2)}\n\n", self.styles["info"]
            )
        else:
            cursor.insertText(
                f"  {truncate_string(str(display_data), 100)}\n\n", self.styles["info"]
            )

        # Auto-scroll if enabled
        if self.auto_scroll_cb.isChecked():
            self.console_display.ensureCursorVisible()

        # Limit console content
        if self.console_display.document().blockCount() > self.max_messages * 3:
            cursor.movePosition(QTextCursor.Start)
            cursor.movePosition(QTextCursor.Down, QTextCursor.KeepAnchor, 100)
            cursor.removeSelectedText()

    def _should_show_full_data(self, data: Dict[str, Any], category: str) -> bool:
        """Determine if we should show full data regardless of size"""
        # Always show full data for important response types
        important_categories = {"response", "element_query", "api_monitoring"}
        if category in important_categories:
            return True

        # Check message type for element query results
        message_type = data.get("message_type", "")
        element_result_types = {
            "element_query_result", "element_event", "browser_control_response",
            "monitored_api_completed", "monitored_api_response_data"
        }
        if message_type in element_result_types:
            return True

        # Check WebSocket message type
        ws_message_type = data.get("type", "")
        important_ws_types = {
            "ELEMENT_QUERY_RESULT", "ELEMENT_QUERY_RESPONSE", "ELEMENT_EVENT_MESSAGE",
            "BROWSER_CONTROL_RESPONSE", "ELEMENT_SCREENSHOT_RESULT"
        }
        if ws_message_type in important_ws_types:
            return True

        return False

    def prepare_display_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for display, handling large image data"""
        if not isinstance(data, dict):
            return data

        display_data = data.copy()

        # Handle base64 image data
        for key in ["dataUrl", "image", "screenshot"]:
            if key in display_data:
                image_data = display_data[key]
                if isinstance(image_data, str) and len(image_data) > 100:
                    # Check if it's base64 image data
                    if image_data.startswith('data:image/') or len(image_data) > 1000:
                        # Replace with summary
                        format_info = "unknown"
                        if image_data.startswith('data:image/'):
                            format_info = image_data.split(';')[0].split('/')[1]

                        display_data[key] = f"[IMAGE DATA: {format_info.upper()}, {len(image_data)} chars]"

        # Recursively handle nested dictionaries
        for key, value in display_data.items():
            if isinstance(value, dict):
                display_data[key] = self.prepare_display_data(value)

        return display_data

    def show_message_details(self, item: QTreeWidgetItem):
        """Show detailed message information"""
        data = item.data(0, Qt.UserRole)
        if data:
            formatted_data = json.dumps(data, indent=2, ensure_ascii=False)
            self.message_details.setPlainText(formatted_data)

    def show_event_details(self, item: QTreeWidgetItem):
        """Show detailed event information"""
        data = item.data(0, Qt.UserRole)
        if data:
            formatted_data = json.dumps(data, indent=2, ensure_ascii=False)
            self.event_details.setPlainText(formatted_data)

    def apply_filter(self, filter_text: str):
        """Apply filter to message tree based on message categories"""
        if filter_text == "All":
            # Show all items
            for i in range(self.message_tree.topLevelItemCount()):
                self.message_tree.topLevelItem(i).setHidden(False)
        else:
            # Map filter text to categories
            filter_mapping = {
                "Connection": "connection",
                "Browser Control": "browser_control",
                "Element Query": "element_query",
                "API Monitoring": "api_monitoring",
                "ChatGPT Messages": "chatgpt",
                "User Info": "user_info",
                "Responses": "response",
                "Errors": "error",
                "Unknown": "unknown"
            }

            target_category = filter_mapping.get(filter_text)
            if target_category:
                for i in range(self.message_tree.topLevelItemCount()):
                    item = self.message_tree.topLevelItem(i)
                    item_category = item.data(1, Qt.UserRole)
                    item.setHidden(item_category != target_category)

        self.logger.info(f"Filter applied: {filter_text}")

    def clear(self):
        """Clear all console content"""
        self.console_display.clear()
        self.message_tree.clear()
        self.events_tree.clear()
        self.message_details.clear()
        self.event_details.clear()

        self.message_count = 0
        self.message_count_label.setText("Messages: 0")

        self.logger.info("Debug console cleared")

    def get_console_content(self) -> str:
        """Get all console content as text"""
        return self.console_display.toPlainText()

    def set_max_messages(self, max_messages: int):
        """Set maximum number of messages to keep"""
        self.max_messages = max_messages

    def retranslate_ui(self):
        """Retranslate UI elements"""
        try:
            # Update tab titles
            self.tab_widget.setTabText(0, "Console")
            self.tab_widget.setTabText(1, "Messages")
            self.tab_widget.setTabText(2, "Events")

            # Update filter combo box items
            filter_items = [
                "All", "Connection", "Browser Control", "Element Query",
                "API Monitoring", "ChatGPT Messages", "User Info",
                "Responses", "Errors", "Unknown"
            ]
            for i, item in enumerate(filter_items):
                if i < self.filter_combo.count():
                    self.filter_combo.setItemText(i, item)

            # Update checkbox
            self.auto_scroll_cb.setText("Auto-scroll")

            # Update buttons
            self.clear_btn.setText("Clear")
            self.export_btn.setText("Export")

            # Update message count label
            self.message_count_label.setText(f"Messages: {self.message_count}")
        except Exception as e:
            self.logger.warning(f"Error in retranslate_ui: {e}")
