"""
Internationalization (i18n) support for ChatGPT Controller GUI

Provides automatic language detection and translation management.
"""

import os
import locale
import json
from pathlib import Path
from typing import Dict, Optional, Any
from PyQt5.QtCore import QObject, pyqtSignal

from ..utils.logging import RichLogger


class TranslationManager(QObject):
    """Translation manager for GUI internationalization"""

    # Signal emitted when language changes
    language_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.logger = RichLogger(__name__)
        self.current_language = "en"
        self.translations: Dict[str, Dict[str, str]] = {}
        self.fallback_language = "en"

        # Get translations directory
        self.translations_dir = Path(__file__).parent / "translations"
        self.translations_dir.mkdir(exist_ok=True)

        # Initialize translations
        self.load_translations()
        self.detect_system_language()

    def detect_system_language(self) -> str:
        """Detect system language and set as current language"""
        try:
            # Get system locale
            system_locale = locale.getdefaultlocale()[0]
            if system_locale:
                # Extract language code (e.g., 'zh_CN' -> 'zh')
                lang_code = system_locale.split("_")[0].lower()

                # Check if we have translations for this language
                if lang_code in self.translations:
                    self.set_language(lang_code)
                    self.logger.info(f"Detected system language: {lang_code}")
                    return lang_code
                else:
                    self.logger.info(
                        f"System language {lang_code} not supported, using English"
                    )

        except Exception as e:
            self.logger.warning(f"Failed to detect system language: {e}")

        # Default to English
        self.set_language("en")
        return "en"

    def load_translations(self):
        """Load all available translations"""
        try:
            # Load language files
            for lang_file in self.translations_dir.glob("*.json"):
                lang_code = lang_file.stem
                try:
                    with open(lang_file, "r", encoding="utf-8") as f:
                        self.translations[lang_code] = json.load(f)
                    self.logger.info(f"Loaded translations for: {lang_code}")
                except Exception as e:
                    self.logger.error(
                        f"Failed to load translations for {lang_code}: {e}"
                    )

            # If no English file found, use built-in fallback
            if "en" not in self.translations:
                self.translations["en"] = self._load_english_translations()
                self.logger.info("Using built-in English translations as fallback")

            self.logger.success(
                f"Loaded translations for languages: {list(self.translations.keys())}"
            )

        except Exception as e:
            self.logger.error(f"Failed to load translations: {e}")
            # Ensure we have at least English
            if "en" not in self.translations:
                self.translations["en"] = self._load_english_translations()

    def _load_english_translations(self) -> Dict[str, str]:
        """Load English translations (built-in fallback)"""
        return {
            # Basic fallback translations
            "main_window_title": "ChatGPT Forward Plugin Controller",
            "operations_panel_title": "Operations",
            "browser_control_group": "Browser Control",
            "element_query_group": "Element Query",
            "page_info_group": "Page Information",
            "connection_group": "Connection",
            "image_preview_group": "Image Preview",
            "button_open_chatgpt": "Open ChatGPT",
            "button_ping": "Ping",
            "no_image_available": "No image available",
            "language_english": "English",
            "language_chinese": "中文",
            "language_auto": "Auto (System)",
        }

    def set_language(self, language_code: str):
        """Set current language"""
        if language_code in self.translations:
            old_language = self.current_language
            self.current_language = language_code
            if old_language != language_code:
                self.language_changed.emit(language_code)
                self.logger.info(f"Language changed to: {language_code}")
        else:
            self.logger.warning(f"Language {language_code} not available")

    def get_available_languages(self) -> Dict[str, str]:
        """Get available languages with their display names"""
        return {
            "en": self.tr("language_english"),
            "zh": self.tr("language_chinese"),
        }

    def tr(self, key: str, **kwargs) -> str:
        """Translate a key to current language"""
        # Try current language first
        if self.current_language in self.translations:
            translation = self.translations[self.current_language].get(key)
            if translation:
                try:
                    return translation.format(**kwargs) if kwargs else translation
                except KeyError as e:
                    self.logger.warning(
                        f"Translation formatting error for key '{key}': {e}"
                    )
                    return translation

        # Fall back to English
        if self.fallback_language in self.translations:
            translation = self.translations[self.fallback_language].get(key)
            if translation:
                try:
                    return translation.format(**kwargs) if kwargs else translation
                except KeyError as e:
                    self.logger.warning(
                        f"Fallback translation formatting error for key '{key}': {e}"
                    )
                    return translation

        # Return key if no translation found
        self.logger.warning(f"No translation found for key: {key}")
        return key

    def get_current_language(self) -> str:
        """Get current language code"""
        return self.current_language

    def save_language_preference(self, language_code: str):
        """Save language preference to config"""
        try:
            from ..core.config import get_config

            config = get_config()
            if hasattr(config, "gui"):
                config.gui.language = language_code
                config.save_to_file()
                self.logger.success(f"Language preference saved: {language_code}")
        except Exception as e:
            self.logger.error(f"Failed to save language preference: {e}")

    def load_language_preference(self) -> Optional[str]:
        """Load language preference from config"""
        try:
            from ..core.config import get_config

            config = get_config()
            if hasattr(config, "gui") and hasattr(config.gui, "language"):
                return config.gui.language
        except Exception as e:
            self.logger.warning(f"Failed to load language preference: {e}")
        return None


# Global translation manager instance
_translation_manager = None


def get_translation_manager() -> TranslationManager:
    """Get global translation manager instance"""
    global _translation_manager
    if _translation_manager is None:
        _translation_manager = TranslationManager()
    return _translation_manager


def tr(key: str, **kwargs) -> str:
    """Global translation function"""
    return get_translation_manager().tr(key, **kwargs)
