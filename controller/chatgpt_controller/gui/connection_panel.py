"""
Connection Panel for ChatGPT Controller GUI

Provides controls for WebSocket server connection management.
"""

from PyQt5.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGroupBox,
    QFormLayout,
    QPushButton,
    QLineEdit,
    QSpinBox,
    QLabel,
    QProgressBar,
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

from ..core.config import get_config
from ..utils.logging import RichLogger
from .i18n import tr


class ConnectionPanel(QWidget):
    """Connection management panel"""

    # Signals
    start_server_requested = pyqtSignal()
    stop_server_requested = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = get_config()
        self.logger = RichLogger(__name__)

        self.setup_ui()
        self.load_configuration()

    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)

        # Server configuration group
        self.create_server_config_group(layout)

        # Server control group
        self.create_server_control_group(layout)

        # Connection status group
        self.create_connection_status_group(layout)

        # Add stretch to push everything to the top
        layout.addStretch()

    def create_server_config_group(self, parent_layout):
        """Create server configuration group"""
        self.server_config_group = QGroupBox(tr("server_config_group"))
        layout = QFormLayout(self.server_config_group)

        # Host input
        self.host_input = QLineEdit()
        self.host_input.setPlaceholderText(tr("placeholder_host"))
        self.host_label = QLabel(tr("label_host"))
        layout.addRow(self.host_label, self.host_input)

        # Port input
        self.port_input = QSpinBox()
        self.port_input.setRange(1, 65535)
        self.port_input.setValue(8765)
        self.port_label = QLabel(tr("label_port"))
        layout.addRow(self.port_label, self.port_input)

        # Max clients input
        self.max_clients_input = QSpinBox()
        self.max_clients_input.setRange(1, 100)
        self.max_clients_input.setValue(10)
        self.max_clients_label = QLabel(tr("label_max_clients"))
        layout.addRow(self.max_clients_label, self.max_clients_input)

        # Ping interval input
        self.ping_interval_input = QSpinBox()
        self.ping_interval_input.setRange(5, 300)
        self.ping_interval_input.setValue(20)
        self.ping_interval_input.setSuffix(tr("suffix_seconds"))
        self.ping_interval_label = QLabel(tr("label_ping_interval"))
        layout.addRow(self.ping_interval_label, self.ping_interval_input)

        parent_layout.addWidget(self.server_config_group)

    def create_server_control_group(self, parent_layout):
        """Create server control group"""
        self.server_control_group = QGroupBox(tr("server_control_group"))
        layout = QVBoxLayout(self.server_control_group)

        # Button layout
        button_layout = QHBoxLayout()

        # Start server button
        self.start_btn = QPushButton(tr("button_start_server"))
        self.start_btn.clicked.connect(self.start_server_requested.emit)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.start_btn)

        # Stop server button
        self.stop_btn = QPushButton(tr("button_stop_server"))
        self.stop_btn.clicked.connect(self.stop_server_requested.emit)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.stop_btn)

        layout.addLayout(button_layout)

        # Progress bar for server status
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        parent_layout.addWidget(self.server_control_group)

    def create_connection_status_group(self, parent_layout):
        """Create connection status group"""
        self.connection_status_group = QGroupBox(tr("connection_status_group"))
        layout = QFormLayout(self.connection_status_group)

        # Server status
        self.server_status_label = QLabel(tr("status_stopped"))
        self.server_status_label.setStyleSheet("color: #f44336; font-weight: bold;")
        self.server_status_label_text = QLabel(tr("label_server_status"))
        layout.addRow(self.server_status_label_text, self.server_status_label)

        # Connected clients count
        self.clients_count_label = QLabel("0")
        self.clients_count_label.setStyleSheet("font-weight: bold;")
        self.clients_count_label_text = QLabel(tr("label_connected_clients"))
        layout.addRow(self.clients_count_label_text, self.clients_count_label)

        # Server address
        self.server_address_label = QLabel(tr("address_not_running"))
        self.server_address_label.setStyleSheet("font-family: monospace;")
        self.server_address_label_text = QLabel(tr("label_server_address"))
        layout.addRow(self.server_address_label_text, self.server_address_label)

        # Uptime
        self.uptime_label = QLabel(tr("uptime_format"))
        self.uptime_label.setStyleSheet("font-family: monospace;")
        self.uptime_label_text = QLabel(tr("label_uptime"))
        layout.addRow(self.uptime_label_text, self.uptime_label)

        parent_layout.addWidget(self.connection_status_group)

    def load_configuration(self):
        """Load configuration values"""
        self.host_input.setText(self.config.server.host)
        self.port_input.setValue(self.config.server.port)
        self.max_clients_input.setValue(self.config.server.max_clients)
        self.ping_interval_input.setValue(self.config.server.ping_interval)

    def get_host(self) -> str:
        """Get the configured host"""
        return self.host_input.text().strip() or "localhost"

    def get_port(self) -> int:
        """Get the configured port"""
        return self.port_input.value()

    def get_max_clients(self) -> int:
        """Get the configured max clients"""
        return self.max_clients_input.value()

    def get_ping_interval(self) -> int:
        """Get the configured ping interval"""
        return self.ping_interval_input.value()

    def set_server_starting(self):
        """Set UI state to server starting"""
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        self.server_status_label.setText(tr("status_starting"))
        self.server_status_label.setStyleSheet("color: #ff9800; font-weight: bold;")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress

        # Disable configuration inputs
        self.host_input.setEnabled(False)
        self.port_input.setEnabled(False)
        self.max_clients_input.setEnabled(False)
        self.ping_interval_input.setEnabled(False)

    def set_server_running(self):
        """Set UI state to server running"""
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.server_status_label.setText(tr("status_running"))
        self.server_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        self.progress_bar.setVisible(False)

        # Update server address
        host = self.get_host()
        port = self.get_port()
        self.server_address_label.setText(f"ws://{host}:{port}")

        self.logger.success(f"Server running on {host}:{port}")

    def set_server_stopped(self):
        """Set UI state to server stopped"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.server_status_label.setText(tr("status_stopped"))
        self.server_status_label.setStyleSheet("color: #f44336; font-weight: bold;")
        self.progress_bar.setVisible(False)

        # Reset status displays
        self.clients_count_label.setText("0")
        self.server_address_label.setText(tr("address_not_running"))
        self.uptime_label.setText(tr("uptime_format"))

        # Re-enable configuration inputs
        self.host_input.setEnabled(True)
        self.port_input.setEnabled(True)
        self.max_clients_input.setEnabled(True)
        self.ping_interval_input.setEnabled(True)

        self.logger.info("Server stopped")

    def update_client_count(self, count: int):
        """Update the connected clients count"""
        self.clients_count_label.setText(str(count))

        # Update styling based on client count
        if count > 0:
            self.clients_count_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        else:
            self.clients_count_label.setStyleSheet("color: #666666; font-weight: bold;")

    def update_uptime(self, uptime_str: str):
        """Update the server uptime display"""
        self.uptime_label.setText(uptime_str)

    def save_configuration(self):
        """Save current configuration"""
        self.config.server.host = self.get_host()
        self.config.server.port = self.get_port()
        self.config.server.max_clients = self.get_max_clients()
        self.config.server.ping_interval = self.get_ping_interval()

        try:
            self.config.save_to_file()
            self.logger.success("Configuration saved")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")

    def reset_configuration(self):
        """Reset configuration to defaults"""
        self.host_input.setText("localhost")
        self.port_input.setValue(8765)
        self.max_clients_input.setValue(10)
        self.ping_interval_input.setValue(20)

        self.logger.info("Configuration reset to defaults")

    def retranslate_ui(self):
        """Retranslate UI elements"""
        # Update group titles
        self.server_config_group.setTitle(tr("server_config_group"))
        self.server_control_group.setTitle(tr("server_control_group"))
        self.connection_status_group.setTitle(tr("connection_status_group"))

        # Update labels
        self.host_label.setText(tr("label_host"))
        self.port_label.setText(tr("label_port"))
        self.max_clients_label.setText(tr("label_max_clients"))
        self.ping_interval_label.setText(tr("label_ping_interval"))

        # Update placeholders and suffixes
        self.host_input.setPlaceholderText(tr("placeholder_host"))
        self.ping_interval_input.setSuffix(tr("suffix_seconds"))

        # Update buttons
        self.start_btn.setText(tr("button_start_server"))
        self.stop_btn.setText(tr("button_stop_server"))

        # Update status labels
        self.server_status_label_text.setText(tr("label_server_status"))
        self.clients_count_label_text.setText(tr("label_connected_clients"))
        self.server_address_label_text.setText(tr("label_server_address"))
        self.uptime_label_text.setText(tr("label_uptime"))
