"""
Main GUI Window for ChatGPT Controller

PyQt5-based main application window with tabs for connection, operations, and debugging.
"""

import sys
import asyncio
from datetime import datetime
from typing import Optional
from PyQt5.QtWidgets import (
    Q<PERSON>ainWindow,
    QWidget,
    Q<PERSON>oxLayout,
    QHB<PERSON>Layout,
    QTabWidget,
    QStatusBar,
    QMenuBar,
    QAction,
    QMessageBox,
    QApplication,
    QSplitter,
    QTextEdit,
    QPushButton,
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont

from ..server.websocket_server import WebSocketServer
from ..core.config import get_config
from ..core.events import EventType, subscribe_to_event
from ..utils.logging import RichLogger
from .connection_panel import ConnectionPanel
from .operations_panel import OperationsPanel
from .debug_console import Debug<PERSON>onsole
from .i18n import get_translation_manager, tr


class ServerThread(QThread):
    """Thread for running the WebSocket server"""

    server_started = pyqtSignal()
    server_stopped = pyqtSignal()
    error_occurred = pyqtSignal(str)

    def __init__(self, server: WebSocketServer):
        super().__init__()
        self.server = server
        self.loop = None
        self.logger = RichLogger(__name__)

    def run(self):
        """Run the WebSocket server in a separate thread"""
        try:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

            self.loop.run_until_complete(self.server.start_server())
            self.server_started.emit()

            # Keep the server running
            self.loop.run_forever()

        except Exception as e:
            import traceback
            self.logger.error(f"Server thread error: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            self.error_occurred.emit(str(e))
        finally:
            if self.loop:
                self.loop.close()
            self.server_stopped.emit()

    def stop_server(self):
        """Stop the WebSocket server"""
        if self.loop and self.server.is_running():
            # Submit the stop_server coroutine and wait for it to complete
            future = asyncio.run_coroutine_threadsafe(self.server.stop_server(), self.loop)
            try:
                # Wait for the server to stop with a timeout
                future.result(timeout=5.0)
            except Exception as e:
                self.logger.error(f"Error stopping server: {e}")
            finally:
                # Stop the event loop
                self.loop.call_soon_threadsafe(self.loop.stop)


class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.config = get_config()
        self.logger = RichLogger(__name__)

        # Translation manager
        self.translation_manager = get_translation_manager()
        self.translation_manager.language_changed.connect(self.on_language_changed)

        # Initialize language from config
        saved_language = self.translation_manager.load_language_preference()
        if saved_language and saved_language != "auto":
            self.translation_manager.set_language(saved_language)

        # Server components
        self.server: Optional[WebSocketServer] = None
        self.server_thread: Optional[ServerThread] = None
        self.server_start_time: Optional[datetime] = None

        # GUI components
        self.connection_panel: Optional[ConnectionPanel] = None
        self.operations_panel: Optional[OperationsPanel] = None
        self.debug_console: Optional[DebugConsole] = None

        self.setup_ui()
        self.setup_events()
        self.setup_timers()

        self.logger.success("Main window initialized")

    def setup_ui(self):
        """Setup the user interface"""
        # Window properties
        self.setWindowTitle(tr("main_window_title"))
        self.setGeometry(
            100, 100, self.config.gui.window_width, self.config.gui.window_height
        )

        # Set application icon (if available)
        try:
            self.setWindowIcon(QIcon("icon.png"))
        except:
            pass

        # Create menu bar
        self.create_menu_bar()

        # Create central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QHBoxLayout(central_widget)

        # Create main splitter
        splitter = QSplitter(Qt.Horizontal)

        # Left panel - Control tabs
        self.create_control_panel(splitter)

        # Right panel - Debug console
        self.create_debug_panel(splitter)

        # Set splitter proportions (60% control, 40% debug)
        splitter.setSizes([600, 400])
        main_layout.addWidget(splitter)

        # Create status bar
        self.create_status_bar()

        self.logger.info("UI setup completed")

    def create_menu_bar(self):
        """Create the menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu(tr("menu_file"))

        # Configuration action
        config_action = QAction(tr("action_configuration"), self)
        config_action.setShortcut("Ctrl+,")
        config_action.triggered.connect(self.show_configuration)
        file_menu.addAction(config_action)

        file_menu.addSeparator()

        # Language submenu
        language_menu = file_menu.addMenu(tr("language_auto"))
        self.create_language_menu(language_menu)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction(tr("action_exit"), self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Server menu
        server_menu = menubar.addMenu(tr("menu_server"))

        # Start server action
        self.start_server_action = QAction(tr("action_start_server"), self)
        self.start_server_action.setShortcut("Ctrl+S")
        self.start_server_action.triggered.connect(self.start_server)
        server_menu.addAction(self.start_server_action)

        # Stop server action
        self.stop_server_action = QAction(tr("action_stop_server"), self)
        self.stop_server_action.setShortcut("Ctrl+T")
        self.stop_server_action.triggered.connect(self.stop_server)
        self.stop_server_action.setEnabled(False)
        server_menu.addAction(self.stop_server_action)

        # View menu
        view_menu = menubar.addMenu(tr("menu_view"))

        # Clear debug console action
        clear_action = QAction(tr("action_clear_console"), self)
        clear_action.setShortcut("Ctrl+L")
        clear_action.triggered.connect(self.clear_debug_console)
        view_menu.addAction(clear_action)

        # Help menu
        help_menu = menubar.addMenu(tr("menu_help"))

        # About action
        about_action = QAction(tr("action_about"), self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_language_menu(self, language_menu):
        """Create language selection submenu"""
        from PyQt5.QtWidgets import QActionGroup

        # Create action group for language selection
        language_group = QActionGroup(self)
        language_group.setExclusive(True)

        # Auto language action
        auto_action = QAction(tr("language_auto"), self)
        auto_action.setCheckable(True)
        auto_action.setData("auto")
        auto_action.triggered.connect(lambda: self.change_language("auto"))
        language_group.addAction(auto_action)
        language_menu.addAction(auto_action)

        language_menu.addSeparator()

        # Available languages
        languages = self.translation_manager.get_available_languages()
        for lang_code, lang_name in languages.items():
            action = QAction(lang_name, self)
            action.setCheckable(True)
            action.setData(lang_code)
            action.triggered.connect(
                lambda checked=False, code=lang_code: self.change_language(code)
            )
            language_group.addAction(action)
            language_menu.addAction(action)

        # Set current language as checked
        current_lang = self.translation_manager.get_current_language()
        saved_pref = self.translation_manager.load_language_preference()

        if saved_pref == "auto" or not saved_pref:
            auto_action.setChecked(True)
        else:
            for action in language_group.actions():
                if action.data() == current_lang:
                    action.setChecked(True)
                    break

    def change_language(self, language_code: str):
        """Change application language"""
        if language_code == "auto":
            # Detect system language
            detected = self.translation_manager.detect_system_language()
            self.translation_manager.save_language_preference("auto")
            self.logger.info(f"Language set to auto (detected: {detected})")
        else:
            self.translation_manager.set_language(language_code)
            self.translation_manager.save_language_preference(language_code)
            self.logger.info(f"Language changed to: {language_code}")

    def on_language_changed(self, language_code: str):
        """Handle language change event"""
        self.retranslate_ui()
        self.logger.success(f"UI language updated to: {language_code}")

    def retranslate_ui(self):
        """Retranslate all UI elements"""
        # Update window title
        self.setWindowTitle(tr("main_window_title"))

        # Update status bar
        if hasattr(self, "status_bar"):
            if self.server and self.server.is_running():
                self.status_bar.showMessage(
                    tr(
                        "status_server_running",
                        host=self.server.host,
                        port=self.server.port,
                    )
                )
            else:
                self.status_bar.showMessage(tr("status_ready"))

        # Only retranslate if UI has been set up
        if hasattr(self, 'menuBar') and self.menuBar():
            # Update menu bar - retranslate existing menus instead of recreating
            self.retranslate_menu_bar()

        # Update tab titles
        if hasattr(self, 'tab_widget'):
            self.retranslate_tabs()

        # Update panels - only if they have been created
        if hasattr(self, 'connection_panel') and self.connection_panel:
            self.connection_panel.retranslate_ui()
        if hasattr(self, 'operations_panel') and self.operations_panel:
            self.operations_panel.retranslate_ui()
        if hasattr(self, 'debug_console') and self.debug_console:
            self.debug_console.retranslate_ui()

    def retranslate_menu_bar(self):
        """Retranslate menu bar without recreating it"""
        menubar = self.menuBar()
        actions = menubar.actions()

        if len(actions) >= 4:  # File, Server, View, Help
            # Update menu titles
            actions[0].setText(tr("menu_file"))
            actions[1].setText(tr("menu_server"))
            actions[2].setText(tr("menu_view"))
            actions[3].setText(tr("menu_help"))

            # Update File menu actions
            file_menu = actions[0].menu()
            if file_menu:
                file_actions = file_menu.actions()
                if len(file_actions) >= 4:
                    file_actions[0].setText(tr("action_configuration"))
                    # Skip separator at index 1
                    if len(file_actions) > 2 and file_actions[2].menu():
                        file_actions[2].setText(tr("language_auto"))
                    # Skip separator at index 3
                    if len(file_actions) > 4:
                        file_actions[4].setText(tr("action_exit"))

            # Update Server menu actions
            server_menu = actions[1].menu()
            if server_menu:
                server_actions = server_menu.actions()
                if len(server_actions) >= 2:
                    server_actions[0].setText(tr("action_start_server"))
                    server_actions[1].setText(tr("action_stop_server"))

            # Update View menu actions
            view_menu = actions[2].menu()
            if view_menu:
                view_actions = view_menu.actions()
                if len(view_actions) >= 1:
                    view_actions[0].setText(tr("action_clear_console"))

            # Update Help menu actions
            help_menu = actions[3].menu()
            if help_menu:
                help_actions = help_menu.actions()
                if len(help_actions) >= 1:
                    help_actions[0].setText(tr("action_about"))

    def retranslate_tabs(self):
        """Retranslate tab titles"""
        if hasattr(self, 'tab_widget'):
            self.tab_widget.setTabText(0, tr("connection_panel_title"))
            self.tab_widget.setTabText(1, tr("operations_panel_title"))

    def create_control_panel(self, parent):
        """Create the control panel with tabs"""
        # Create tab widget
        self.tab_widget = QTabWidget()

        # Connection tab
        self.connection_panel = ConnectionPanel()
        self.connection_panel.start_server_requested.connect(self.start_server)
        self.connection_panel.stop_server_requested.connect(self.stop_server)
        self.tab_widget.addTab(self.connection_panel, tr("connection_panel_title"))

        # Operations tab
        self.operations_panel = OperationsPanel()
        self.operations_panel.command_requested.connect(self.send_command)
        self.tab_widget.addTab(self.operations_panel, tr("operations_panel_title"))

        parent.addWidget(self.tab_widget)

    def create_debug_panel(self, parent):
        """Create the debug console panel"""
        self.debug_console = DebugConsole()
        parent.addWidget(self.debug_console)

    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage(tr("status_ready"))

    def setup_events(self):
        """Setup event handlers"""
        # Subscribe to global events
        subscribe_to_event(EventType.SERVER_STARTED, self.on_server_started_event)
        subscribe_to_event(EventType.SERVER_STOPPED, self.on_server_stopped_event)
        subscribe_to_event(EventType.CLIENT_CONNECTED, self.on_client_connected_event)
        subscribe_to_event(
            EventType.CLIENT_DISCONNECTED, self.on_client_disconnected_event
        )
        subscribe_to_event(EventType.MESSAGE_RECEIVED, self.on_message_received_event)

    def setup_timers(self):
        """Setup periodic timers"""
        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second

    def start_server(self):
        """Start the WebSocket server"""
        if self.server_thread and self.server_thread.isRunning():
            self.logger.warning("Server is already running")
            return

        try:
            # Get configuration from connection panel
            host = self.connection_panel.get_host()
            port = self.connection_panel.get_port()

            # Create server
            self.server = WebSocketServer(host, port)

            # Create and start server thread
            self.server_thread = ServerThread(self.server)
            self.server_thread.server_started.connect(self.on_server_started)
            self.server_thread.server_stopped.connect(self.on_server_stopped)
            self.server_thread.error_occurred.connect(self.on_server_error)

            self.server_thread.start()

            # Update UI
            self.start_server_action.setEnabled(False)
            self.stop_server_action.setEnabled(True)
            self.connection_panel.set_server_starting()

            self.logger.server_event(f"Starting server on {host}:{port}")

        except Exception as e:
            import traceback
            self.logger.error(f"Failed to start server: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            QMessageBox.critical(self, "Server Error", f"Failed to start server: {e}")

    def stop_server(self):
        """Stop the WebSocket server"""
        if not self.server_thread or not self.server_thread.isRunning():
            self.logger.warning("Server is not running")
            return

        try:
            self.server_thread.stop_server()
            self.server_thread.wait(5000)  # Wait up to 5 seconds

            # Update UI
            self.start_server_action.setEnabled(True)
            self.stop_server_action.setEnabled(False)
            self.connection_panel.set_server_stopped()

            self.logger.server_event("Server stopped")

        except Exception as e:
            self.logger.error(f"Error stopping server: {e}")

    def send_command(self, command_type: str, data: dict):
        """Send command to the plugin"""
        if not self.server or not self.server.is_running():
            QMessageBox.warning(self, "Warning", "Server is not running")
            return

        if self.server.get_connected_clients_count() == 0:
            QMessageBox.warning(self, "Warning", "No clients connected")
            return

        try:
            # Send command asynchronously
            from ..core.message_types import MessageType

            # Convert string to MessageType enum
            msg_type = MessageType(command_type)

            # Use asyncio to send command
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self.server.send_command(msg_type, data))
                self.logger.command_event(f"Sent command: {command_type}")
            finally:
                loop.close()

        except Exception as e:
            self.logger.error(f"Failed to send command: {e}")
            QMessageBox.critical(self, "Command Error", f"Failed to send command: {e}")

    def on_server_started(self):
        """Handle server started signal"""
        self.server_start_time = datetime.now()
        self.connection_panel.set_server_running()
        self.status_bar.showMessage(
            tr("status_server_running", host=self.server.host, port=self.server.port)
        )
        self.logger.success(tr("log_server_started"))

    def on_server_stopped(self):
        """Handle server stopped signal"""
        self.server_start_time = None
        self.connection_panel.set_server_stopped()
        self.status_bar.showMessage(tr("status_server_stopped"))
        self.start_server_action.setEnabled(True)
        self.stop_server_action.setEnabled(False)

    def on_server_error(self, error: str):
        """Handle server error signal"""
        self.logger.error(f"Server error: {error}")
        QMessageBox.critical(self, "Server Error", f"Server error: {error}")
        self.start_server_action.setEnabled(True)
        self.stop_server_action.setEnabled(False)
        self.connection_panel.set_server_stopped()

    def on_server_started_event(self, event):
        """Handle server started event"""
        if self.debug_console:
            self.debug_console.log_event("SERVER_STARTED", event.data)

    def on_server_stopped_event(self, event):
        """Handle server stopped event"""
        if self.debug_console:
            self.debug_console.log_event("SERVER_STOPPED", event.data)

    def on_client_connected_event(self, event):
        """Handle client connected event"""
        if self.debug_console:
            self.debug_console.log_event("CLIENT_CONNECTED", event.data)
        if self.connection_panel:
            self.connection_panel.update_client_count(
                self.server.get_connected_clients_count() if self.server else 0
            )

    def on_client_disconnected_event(self, event):
        """Handle client disconnected event"""
        if self.debug_console:
            self.debug_console.log_event("CLIENT_DISCONNECTED", event.data)
        if self.connection_panel:
            self.connection_panel.update_client_count(
                self.server.get_connected_clients_count() if self.server else 0
            )

    def on_message_received_event(self, event):
        """Handle message received event"""
        if self.debug_console:
            self.debug_console.log_message("INCOMING", event.data)

        # Forward message to operations panel for image handling
        if self.operations_panel and hasattr(event, 'data'):
            try:
                message_data = event.data
                if isinstance(message_data, dict):
                    message_type = message_data.get('message_type', '')
                    self.operations_panel.handle_websocket_message(message_type, message_data)
                else:
                    self.logger.warning("Invalid message data format")
            except Exception as e:
                self.logger.error(f"Error forwarding message to operations panel: {e}")

    def update_status(self):
        """Update status information periodically"""
        if self.server and self.server.is_running():
            client_count = self.server.get_connected_clients_count()
            if self.connection_panel:
                self.connection_panel.update_client_count(client_count)

                # Update uptime
                if self.server_start_time:
                    uptime = datetime.now() - self.server_start_time
                    uptime_str = self.format_uptime(uptime)
                    self.connection_panel.update_uptime(uptime_str)

    def format_uptime(self, uptime):
        """Format uptime as HH:MM:SS"""
        total_seconds = int(uptime.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def show_configuration(self):
        """Show configuration dialog"""
        QMessageBox.information(
            self, "Configuration", "Configuration dialog not yet implemented"
        )

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About",
            "ChatGPT Forward Plugin Controller\n\n"
            "A remote controller for the ChatGPT Forward Plugin\n"
        )

    def clear_debug_console(self):
        """Clear the debug console"""
        if self.debug_console:
            self.debug_console.clear()

    def closeEvent(self, event):
        """Handle application close event"""
        if self.server_thread and self.server_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "Confirm Exit",
                "Server is running. Stop server and exit?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No,
            )

            if reply == QMessageBox.Yes:
                self.stop_server()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """Main entry point for the GUI application"""
    app = QApplication(sys.argv)
    app.setApplicationName("ChatGPT Forward Plugin Controller")
    app.setApplicationVersion("1.0.0")

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run the application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
