{"main_window_title": "ChatGPT Forward Plugin Controller", "menu_file": "&File", "menu_server": "&Server", "menu_view": "&View", "menu_help": "&Help", "action_configuration": "&Configuration...", "action_exit": "E&xit", "action_start_server": "&Start Server", "action_stop_server": "S&top Server", "action_clear_console": "&Clear Debug Console", "action_about": "&About", "status_ready": "Ready - Server not started", "status_server_running": "Server running on {host}:{port}", "status_server_stopped": "Server stopped", "connection_panel_title": "Connection", "server_config_group": "Server Configuration", "server_control_group": "Server Control", "connection_status_group": "Connection Status", "label_host": "Host:", "label_port": "Port:", "label_max_clients": "Max Clients:", "label_ping_interval": "Ping Interval:", "placeholder_host": "localhost", "suffix_seconds": " seconds", "button_start_server": "Start Server", "button_stop_server": "Stop Server", "label_server_status": "Server Status:", "label_connected_clients": "Connected Clients:", "label_server_address": "Server Address:", "label_uptime": "Uptime:", "status_stopped": "Stopped", "status_starting": "Starting...", "status_running": "Running", "address_not_running": "Not running", "uptime_format": "00:00:00", "operations_panel_title": "Operations", "send_message_group": "Send Message", "conversation_ops_group": "Conversation Operations", "response_ops_group": "Response Operations", "info_control_group": "Information & Control", "browser_control_group": "Browser Control", "element_query_group": "Element Query", "page_info_group": "Page Information", "connection_group": "Connection", "placeholder_message": "Enter message to send to ChatGPT...", "button_send_message": "Send Message", "button_clear": "Clear", "placeholder_conv_id": "Conversation ID (optional)", "button_select": "Select", "button_new_chat": "New Chat", "button_regenerate": "Regenerate Response", "button_stop_generation": "Stop Generation", "button_get_user_info": "Get User Info", "button_get_chat_req": "Get Chat Requirements", "button_open_chatgpt": "Open ChatGPT", "button_set_window_size": "Set Window Size", "button_take_screenshot": "Take Screenshot", "button_query_element": "Query Element", "button_execute_action": "Execute Action", "button_preview_action": "Preview Action", "button_screenshot_element": "Screenshot Element", "button_get_page_info": "Get Page Info", "button_ping": "<PERSON>", "label_url": "URL:", "label_width": "Width:", "label_height": "Height:", "label_format": "Format:", "label_quality": "Quality:", "label_type": "Type:", "label_selector": "Selector:", "label_action": "Action:", "label_text": "Text:", "label_coordinates": "Coordinates:", "label_scroll": "Scroll:", "label_attribute": "Attribute:", "label_timeout": "Timeout:", "checkbox_new_tab": "New Tab", "checkbox_focus": "Focus", "checkbox_full_page": "Full Page", "checkbox_multiple": "Multiple", "checkbox_wait_visible": "Wait Visible", "checkbox_all": "All", "checkbox_url": "URL", "checkbox_title": "Title", "checkbox_size": "Size", "placeholder_chatgpt_url": "https://chatgpt.com", "placeholder_selector": "Enter selector...", "placeholder_text": "Enter text...", "placeholder_attribute": "Enter attribute name...", "auto": "Auto", "suffix_ms": " ms", "debug_console_title": "Debug Console", "tab_console": "<PERSON><PERSON><PERSON>", "tab_messages": "Messages", "tab_events": "Events", "label_filter": "Filter:", "filter_all": "All", "filter_server_events": "Server Events", "filter_client_events": "Client Events", "filter_messages": "Messages", "filter_commands": "Commands", "filter_errors": "Errors", "filter_incoming": "Incoming", "filter_outgoing": "Outgoing", "checkbox_auto_scroll": "Auto-scroll", "button_clear_console": "Clear", "button_export": "Export", "label_messages_count": "Messages: {count}", "header_time": "Time", "header_type": "Type", "header_direction": "Direction", "header_client": "Client", "header_event_type": "Event Type", "header_source": "Source", "header_details": "Details", "warning_title": "Warning", "error_title": "Error", "info_title": "Information", "confirm_exit_title": "Confirm Exit", "server_already_running": "Server is already running", "server_not_running": "Server is not running", "no_clients_connected": "No clients connected", "confirm_exit_message": "Server is running. Stop server and exit?", "server_error_message": "Server error: {error}", "failed_start_server": "Failed to start server: {error}", "failed_send_command": "Failed to send command: {error}", "config_not_implemented": "Configuration dialog not yet implemented", "about_message": "ChatGPT Forward Plugin Controller\\n\\nA professional remote controller for the ChatGPT Forward Plugin\\nBuilt with PyQt5 and Rich logging", "log_server_started": "Server started successfully", "log_server_stopped": "Server stopped", "log_server_starting": "Starting server on {host}:{port}", "log_config_saved": "Configuration saved", "log_config_reset": "Configuration reset to defaults", "log_debug_cleared": "Debug console cleared", "log_no_message_text": "No message text entered", "log_no_conv_id": "No conversation ID entered", "log_send_message": "Send message: {text}...", "log_select_conversation": "Select conversation: {conv_id}", "log_create_new_chat": "Create new chat", "log_clear_conversation": "Clear conversation", "log_regenerate_response": "Regenerate response", "log_stop_generating": "Stop generating", "log_get_user_info": "Get user info", "log_get_chat_requirements": "Get chat requirements", "log_open_chatgpt": "Open ChatGPT", "log_set_window_size": "Set window size: {width}x{height}", "log_take_screenshot": "Take screenshot: {format}, quality={quality}, full_page={full_page}", "log_query_element": "Query element: {selector_type}={selector_value}", "log_click_element": "Click element: {selector_type}={selector_value}", "log_execute_action": "Execute action '{action_type}' on element: {selector_type}={selector_value}", "log_preview_action": "Action preview: {preview}", "log_screenshot_element": "Screenshot element: {selector_type}={selector_value}", "log_get_page_info": "Get page info: {info_types}", "log_ping_sent": "<PERSON> sent", "log_filter_applied": "Filter applied: {filter}", "log_no_selector_value": "No selector value entered", "log_no_text_value": "No text value entered", "log_no_attribute_value": "No attribute name entered", "log_no_action_type": "No action type selected", "log_parameter_validation_error": "Parameter validation error: {error}", "log_command_creation_error": "Failed to create command: {error}", "log_execute_action_detailed": "Execute action '{action_type}' on element: {selector_type}={selector_value} with {params}", "validation_empty_text": "Text is required for '{action_type}' action", "validation_empty_attribute": "Attribute name is required for 'getAttribute' action", "validation_invalid_attribute": "Invalid attribute name: '{attribute}'", "validation_unsupported_action": "Unsupported action type: '{action_type}'", "param_summary_none": "no parameters", "param_summary_text": "text: '{text}'", "param_summary_coordinates": "coordinates: ({x}, {y})", "param_summary_click_center": "click at center", "param_summary_scroll": "scroll: ({x}, {y})", "param_summary_scroll_into_view": "scroll into view", "param_summary_attribute": "attribute: '{attribute}'", "preview_action_label": "Action: {action_type}", "preview_selector_label": "Selector: {selector_type}={selector_value}", "preview_text_label": "Text: '{text}'", "preview_coordinates_label": "Coordinates: ({x}, {y})", "preview_click_center_label": "Click at center", "preview_scroll_label": "Scroll: ({x}, {y})", "preview_scroll_into_view_label": "Scroll into view", "preview_attribute_label": "Attribute: '{attribute}'", "preview_no_params_label": "No additional parameters", "preview_warnings_label": "Warnings", "preview_warning_empty_text": "Text is required", "preview_warning_empty_attribute": "Attribute name is required", "preview_warning_invalid_attribute": "Invalid attribute name '{attribute}'", "log_preview_error": "Preview error: {error}", "log_image_loaded": "Image loaded in preview: {format}", "log_image_preview_error": "Image preview error: {error}", "log_image_response_error": "Error handling image response: {error}", "language_english": "English", "language_chinese": "中文", "language_auto": "Auto (System)", "image_preview_group": "Image Preview", "image_preview_title": "Image Preview Window", "no_image_available": "No image available", "image_size_info": "Size: {width} x {height}", "image_format_info": "Format: {format}", "save_image": "Save Image", "clear_image": "Clear Image", "copy_image": "Copy Image", "zoom_in": "Zoom In", "zoom_out": "Zoom Out", "zoom_reset": "Reset Zoom", "fit_to_window": "Fit to Window", "actual_size": "Actual Size", "image_saved": "Image saved to: {path}", "image_copied": "Image copied to clipboard", "save_image_error": "Failed to save image: {error}", "copy_image_error": "Failed to copy image: {error}", "invalid_image_data": "Invalid image data", "image_load_error": "Failed to load image: {error}"}