import asyncio
import traceback
from datetime import datetime
from urllib.parse import quote

from fastapi import APIRouter, Depends, Request, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from ..database import get_db
from ..models import Conversation, Message
from ..schemas import (
    ConversationStartRequest,
    ConversationStartResponse,
    MessageRequest,
    MessageResponse,
    ConversationResponse,
    ConversationListResponse,
    ConversationData,
    MessageData,
    ConversationQueryParams,
)
from ..exceptions import (
    NotFoundError,
    BrowserControlError,
)
from ..utils.browser_utils import (
    extract_conversation_id_from_url,
    simulate_keyboard_by_xpath,
    wait_url_match_regex,
    wait_for_page_load,
    get_current_url,
    click_element_by_xpath,
    input_text_by_xpath,
)
from ...utils.logging import RichLogger
from ...server.websocket_server import WebSocketServer
from ...core.message_types import MessageType
from ..utils.timestamp_resolver import timestamp_resolver
from ..utils.pending_conversation_manager import pending_manager
from ..models import ConversationStatus

# Event handlers are initialized in the FastAPI app startup

# Create router
router = APIRouter(tags=['后台更新管理'])
logger = RichLogger(__name__)

@router.get(
    "/pending-status",
    response_model=dict,
    summary="获取后台更新池状态",
    description="查看当前等待更新的对话状态和管理器运行情况",
)
async def get_pending_status():
    """
    获取后台更新池状态

    Returns:
        dict: 包含管理器状态、等待对话列表等信息
    """
    try:
        status = pending_manager.get_status()
        return {
            "success": True,
            "data": status,
        }
    except Exception as e:
        logger.error(f"❌ Error getting pending status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/{conversation_id}/add-to-pending",
    response_model=dict,
    summary="手动添加对话到更新池",
    description="手动将指定对话添加到后台更新池",
)
async def add_conversation_to_pending(
    conversation_id: str,
    db: AsyncSession = Depends(get_db),
    auto_config: bool = Query(True, description="是否自动选择最优配置"),
):
    """
    手动添加对话到更新池

    Args:
        conversation_id: 对话ID
        db: 数据库会话
        auto_config: 是否自动选择配置

    Returns:
        dict: 操作结果
    """
    try:
        # 检查对话是否存在
        query = select(Conversation).where(Conversation.id == conversation_id)
        result = await db.execute(query)
        conversation = result.scalar_one_or_none()

        if not conversation:
            raise NotFoundError(f"Conversation {conversation_id} not found")

        # 设置为pending状态
        conversation.status = ConversationStatus.PENDING
        await db.commit()

        # 添加到更新池
        await pending_manager.add_pending_conversation(conversation_id, auto_config=auto_config)

        logger.info(f"➕ Manually added conversation {conversation_id} to pending pool")

        return {
            "success": True,
            "message": f"Conversation {conversation_id} added to pending pool",
        }

    except Exception as e:
        logger.error(f"❌ Error adding conversation to pending: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.delete(
    "/{conversation_id}/remove-from-pending",
    response_model=dict,
    summary="从更新池移除对话",
    description="从后台更新池中移除指定对话",
)
async def remove_conversation_from_pending(
    conversation_id: str,
    db: AsyncSession = Depends(get_db),
    set_completed: bool = Query(True, description="是否将对话状态设置为已完成"),
):
    """
    从更新池移除对话

    Args:
        conversation_id: 对话ID
        db: 数据库会话
        set_completed: 是否设置为已完成状态

    Returns:
        dict: 操作结果
    """
    try:
        # 从更新池移除
        await pending_manager.remove_pending_conversation(conversation_id)

        if set_completed:
            # 更新数据库状态
            query = select(Conversation).where(Conversation.id == conversation_id)
            result = await db.execute(query)
            conversation = result.scalar_one_or_none()

            if conversation:
                conversation.status = ConversationStatus.COMPLETED
                await db.commit()

        logger.info(f"➖ Removed conversation {conversation_id} from pending pool")

        return {
            "success": True,
            "message": f"Conversation {conversation_id} removed from pending pool",
        }

    except Exception as e:
        logger.error(f"❌ Error removing conversation from pending: {e}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/pending-manager/start",
    response_model=dict,
    summary="启动后台更新管理器",
    description="启动后台对话更新管理器",
)
async def start_pending_manager():
    """启动后台更新管理器"""
    try:
        await pending_manager.start()
        return {
            "success": True,
            "message": "Pending conversation manager started",
        }
    except Exception as e:
        logger.error(f"❌ Error starting pending manager: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/pending-manager/stop",
    response_model=dict,
    summary="停止后台更新管理器",
    description="停止后台对话更新管理器",
)
async def stop_pending_manager():
    """停止后台更新管理器"""
    try:
        await pending_manager.stop()
        return {
            "success": True,
            "message": "Pending conversation manager stopped",
        }
    except Exception as e:
        logger.error(f"❌ Error stopping pending manager: {e}")
        raise HTTPException(status_code=500, detail=str(e))