"""
System Status API Router

Provides endpoints for checking system health, WebSocket connection status,
and overall service availability.
"""

import time
import psutil
import websockets
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from ..database import get_db, check_db_health
from ..schemas import StatusResponse, ConnectionStatus, SystemHealth, HealthCheckResponse
from ...utils.logging import RichLogger

# Create router
router = APIRouter()
logger = RichLogger(__name__)

# Track service start time
SERVICE_START_TIME = time.time()


def get_websocket_server(request: Request):
    """Get WebSocket server from app state"""
    return getattr(request.app.state, 'websocket_server', None)


def get_monitoring_service(request: Request):
    """Get monitoring service from app state"""
    return getattr(request.app.state, 'monitoring_service', None)


async def get_connection_status(websocket_server) -> ConnectionStatus:
    """Get WebSocket connection status"""
    if not websocket_server:
        return ConnectionStatus(
            connected=False,
            client_count=0,
            last_ping=None,
            uptime_seconds=0.0
        )

    try:
        # Get connection info from WebSocket server
        is_running = False
        client_count = 0

        # Check if server is running
        if hasattr(websocket_server, 'running'):
            is_running = websocket_server.running
        elif hasattr(websocket_server, 'is_running'):
            is_running = websocket_server.is_running()

        # Get client count from connection manager
        if hasattr(websocket_server, 'connection_manager') and websocket_server.connection_manager:
            connection_manager = websocket_server.connection_manager

            if hasattr(connection_manager, 'connections'):
                connections = connection_manager.connections
                if isinstance(connections, dict):
                    # Count active connections by checking WebSocket state
                    active_count = 0
                    for client_id, websocket in connections.items():
                        try:
                            # Check if WebSocket is still open
                            if hasattr(websocket, 'state'):
                                if websocket.state == websockets.protocol.State.OPEN:
                                    active_count += 1
                            elif hasattr(websocket, 'closed'):
                                if not websocket.closed:
                                    active_count += 1
                            else:
                                # Fallback: assume active if no state info
                                active_count += 1
                        except Exception:
                            # Skip connections that can't be checked
                            continue

                    client_count = active_count
                else:
                    client_count = len(connections)

            # Also check if connection manager has a method to get active count
            elif hasattr(connection_manager, 'get_active_connection_count'):
                client_count = connection_manager.get_active_connection_count()
            elif hasattr(connection_manager, 'active_connections'):
                client_count = len(connection_manager.active_connections)

        # Calculate uptime
        uptime = time.time() - SERVICE_START_TIME

        logger.debug(f"WebSocket status: running={is_running}, clients={client_count}")

        return ConnectionStatus(
            connected=is_running,
            client_count=client_count,
            last_ping=datetime.now() if is_running and client_count > 0 else None,
            uptime_seconds=uptime
        )
    except Exception as e:
        logger.error(f"Error getting connection status: {e}")
        return ConnectionStatus(
            connected=False,
            client_count=0,
            last_ping=None,
            uptime_seconds=0.0
        )


async def get_system_health(db: AsyncSession) -> SystemHealth:
    """Get system health metrics"""
    try:
        # Check database health
        db_healthy = await check_db_health()
        
        # Get system metrics
        memory_info = psutil.virtual_memory()
        memory_usage_mb = memory_info.used / (1024 * 1024)
        cpu_usage = psutil.cpu_percent(interval=0.1)
        
        return SystemHealth(
            database_healthy=db_healthy,
            websocket_healthy=True,  # Will be updated based on WebSocket status
            memory_usage_mb=memory_usage_mb,
            cpu_usage_percent=cpu_usage
        )
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        return SystemHealth(
            database_healthy=False,
            websocket_healthy=False,
            memory_usage_mb=0.0,
            cpu_usage_percent=0.0
        )


@router.get("/status", response_model=StatusResponse)
async def get_status(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Get system status including WebSocket connection and health metrics
    
    Returns:
        StatusResponse: System status information
    """
    try:
        # Get WebSocket server
        websocket_server = get_websocket_server(request)
        
        # Get connection status
        connection_status = await get_connection_status(websocket_server)
        
        # Get system health
        system_health = await get_system_health(db)
        
        # Update WebSocket health based on connection status
        system_health.websocket_healthy = connection_status.connected
        
        logger.info(f"Status check: WS={connection_status.connected}, DB={system_health.database_healthy}")
        
        return StatusResponse(
            success=True,
            message="System status retrieved successfully",
            connection_status=connection_status,
            system_health=system_health
        )
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return StatusResponse(
            success=False,
            message=f"Error retrieving system status: {str(e)}",
            connection_status=ConnectionStatus(
                connected=False,
                client_count=0,
                last_ping=None,
                uptime_seconds=0.0
            ),
            system_health=SystemHealth(
                database_healthy=False,
                websocket_healthy=False,
                memory_usage_mb=0.0,
                cpu_usage_percent=0.0
            )
        )


@router.get("/health", response_model=HealthCheckResponse)
async def health_check(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Simple health check endpoint for load balancers and monitoring
    
    Returns:
        HealthCheckResponse: Basic health status
    """
    try:
        # Check database
        db_healthy = await check_db_health()
        
        # Check WebSocket server
        websocket_server = get_websocket_server(request)
        ws_healthy = False

        if websocket_server:
            if hasattr(websocket_server, 'running'):
                ws_healthy = websocket_server.running
            elif hasattr(websocket_server, 'is_running'):
                ws_healthy = websocket_server.is_running()
            else:
                # Fallback: check if server object exists
                ws_healthy = websocket_server is not None
        
        # Calculate uptime
        uptime = time.time() - SERVICE_START_TIME
        
        # Determine overall status
        overall_healthy = db_healthy and ws_healthy
        status = "healthy" if overall_healthy else "unhealthy"
        
        return HealthCheckResponse(
            status=status,
            uptime_seconds=uptime,
            checks={
                "database": db_healthy,
                "websocket": ws_healthy,
                "api": True  # If we're responding, API is healthy
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            uptime_seconds=time.time() - SERVICE_START_TIME,
            checks={
                "database": False,
                "websocket": False,
                "api": False
            }
        )


@router.get("/metrics")
async def get_metrics(request: Request):
    """
    Get detailed system metrics
    
    Returns:
        Dict: Detailed system metrics
    """
    try:
        # Get WebSocket server metrics
        websocket_server = get_websocket_server(request)
        ws_metrics = {}
        
        if websocket_server and hasattr(websocket_server, 'statistics_handler'):
            stats_handler = websocket_server.statistics_handler
            ws_metrics = {
                "messages_received": getattr(stats_handler, 'messages_received', 0),
                "messages_sent": getattr(stats_handler, 'messages_sent', 0),
                "connections_total": getattr(stats_handler, 'connections_total', 0),
                "active_connections": len(websocket_server.connection_manager.connections) if hasattr(websocket_server, 'connection_manager') else 0
            }
        
        # Get system metrics
        memory_info = psutil.virtual_memory()
        disk_info = psutil.disk_usage('/')
        
        system_metrics = {
            "memory": {
                "total_mb": memory_info.total / (1024 * 1024),
                "used_mb": memory_info.used / (1024 * 1024),
                "available_mb": memory_info.available / (1024 * 1024),
                "percent": memory_info.percent
            },
            "disk": {
                "total_gb": disk_info.total / (1024 * 1024 * 1024),
                "used_gb": disk_info.used / (1024 * 1024 * 1024),
                "free_gb": disk_info.free / (1024 * 1024 * 1024),
                "percent": (disk_info.used / disk_info.total) * 100
            },
            "cpu": {
                "percent": psutil.cpu_percent(interval=0.1),
                "count": psutil.cpu_count()
            }
        }
        
        # Get middleware metrics if available
        middleware_metrics = {}
        if hasattr(request.app, 'state') and hasattr(request.app.state, 'metrics_middleware'):
            middleware_metrics = request.app.state.metrics_middleware.get_metrics()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": time.time() - SERVICE_START_TIME,
            "websocket": ws_metrics,
            "system": system_metrics,
            "api": middleware_metrics
        }
        
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
