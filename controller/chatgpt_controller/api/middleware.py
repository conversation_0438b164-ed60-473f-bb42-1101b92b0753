"""
Custom Middleware for FastAPI Application

Implements logging, rate limiting, and other middleware functionality.
"""

import time
import asyncio
from typing import Dict, Any
from collections import defaultdict, deque
from datetime import datetime, timedelta

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import <PERSON><PERSON><PERSON><PERSON>ponse

from ..utils.logging import <PERSON><PERSON><PERSON><PERSON>
from .exceptions import RateLimitError

# Create logger
logger = RichLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging"""
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Log request
        logger.info(f"📥 {request.method} {request.url.path}")
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        status_emoji = "✅" if response.status_code < 400 else "❌"
        logger.info(
            f"{status_emoji} {request.method} {request.url.path} "
            f"- {response.status_code} - {process_time:.3f}s"
        )
        
        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting"""
    
    def __init__(self, app, requests_per_minute: int = 100):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests: Dict[str, deque] = defaultdict(deque)
        self.cleanup_interval = 60  # seconds
        self.last_cleanup = time.time()
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to client host
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    def _cleanup_old_requests(self):
        """Remove old request records"""
        current_time = time.time()
        cutoff_time = current_time - 60  # 1 minute ago
        
        for client_ip in list(self.requests.keys()):
            client_requests = self.requests[client_ip]
            
            # Remove old requests
            while client_requests and client_requests[0] < cutoff_time:
                client_requests.popleft()
            
            # Remove empty deques
            if not client_requests:
                del self.requests[client_ip]
        
        self.last_cleanup = current_time
    
    def _is_rate_limited(self, client_ip: str) -> bool:
        """Check if client is rate limited"""
        current_time = time.time()
        
        # Periodic cleanup
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_requests()
        
        client_requests = self.requests[client_ip]
        
        # Remove requests older than 1 minute
        cutoff_time = current_time - 60
        while client_requests and client_requests[0] < cutoff_time:
            client_requests.popleft()
        
        # Check rate limit
        if len(client_requests) >= self.requests_per_minute:
            return True
        
        # Add current request
        client_requests.append(current_time)
        return False
    
    async def dispatch(self, request: Request, call_next):
        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/api/v1/status"]:
            return await call_next(request)
        
        client_ip = self._get_client_ip(request)
        
        if self._is_rate_limited(client_ip):
            logger.warning(f"🚫 Rate limit exceeded for {client_ip}")
            return JSONResponse(
                status_code=429,
                content={
                    "error": {
                        "type": "RateLimitError",
                        "message": "Rate limit exceeded",
                        "details": {
                            "limit": self.requests_per_minute,
                            "window": "1 minute",
                            "retry_after": 60
                        }
                    }
                },
                headers={"Retry-After": "60"}
            )
        
        return await call_next(request)


class CORSMiddleware(BaseHTTPMiddleware):
    """Custom CORS middleware with enhanced logging"""
    
    def __init__(self, app, allow_origins=None, allow_methods=None, allow_headers=None):
        super().__init__(app)
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["*"]
        self.allow_headers = allow_headers or ["*"]
    
    async def dispatch(self, request: Request, call_next):
        # Handle preflight requests
        if request.method == "OPTIONS":
            response = Response()
            response.headers["Access-Control-Allow-Origin"] = "*"
            response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
            response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
            response.headers["Access-Control-Max-Age"] = "86400"
            return response
        
        # Process request
        response = await call_next(request)
        
        # Add CORS headers
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Credentials"] = "true"
        
        return response


class SecurityMiddleware(BaseHTTPMiddleware):
    """Security headers middleware"""
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add API version header
        response.headers["X-API-Version"] = "1.0.0"
        
        return response


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware for collecting metrics"""
    
    def __init__(self, app):
        super().__init__(app)
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
        self.start_time = time.time()
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        # Increment request counter
        self.request_count += 1
        
        # Process request
        response = await call_next(request)
        
        # Calculate metrics
        response_time = time.time() - start_time
        self.total_response_time += response_time
        
        if response.status_code >= 400:
            self.error_count += 1
        
        # Add metrics headers
        response.headers["X-Request-Count"] = str(self.request_count)
        response.headers["X-Error-Rate"] = f"{(self.error_count / self.request_count * 100):.2f}%"
        response.headers["X-Avg-Response-Time"] = f"{(self.total_response_time / self.request_count):.3f}s"
        response.headers["X-Uptime"] = f"{(time.time() - self.start_time):.0f}s"
        
        return response
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics"""
        uptime = time.time() - self.start_time
        return {
            "request_count": self.request_count,
            "error_count": self.error_count,
            "error_rate": (self.error_count / max(self.request_count, 1)) * 100,
            "avg_response_time": self.total_response_time / max(self.request_count, 1),
            "uptime_seconds": uptime,
        }
