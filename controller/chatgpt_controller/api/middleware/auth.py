"""
Authentication middleware for FastAPI

Provides API key validation for all HTTP endpoints with configurable
authentication requirements and clear error responses.
"""

import logging
from typing import Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from ...core.config import get_config
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """
    Middleware to validate API keys for HTTP requests
    
    Checks for valid API keys in the Authorization header and returns
    401 Unauthorized for invalid or missing keys when authentication is required.
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.config = get_config()

    async def dispatch(self, request: Request, call_next):
        """Process request and validate authentication"""
        
        # Skip authentication for certain paths
        if self._should_skip_auth(request.url.path):
            return await call_next(request)

        # Skip if authentication is disabled
        if not self.config.auth.enabled or not self.config.auth.require_auth_for_api:
            return await call_next(request)

        # Extract and validate API key
        api_key = self._extract_api_key(request)
        
        if not api_key:
            logger.warning(f"Missing API key for request to {request.url.path}")
            return self._create_auth_error_response("Missing API key")

        if not self.config.auth.is_valid_api_key(api_key):
            logger.warning(f"Invalid API key for request to {request.url.path}")
            return self._create_auth_error_response("Invalid API key")

        # Authentication successful, proceed with request
        logger.debug(f"Authentication successful for request to {request.url.path}")
        return await call_next(request)

    def _should_skip_auth(self, path: str) -> bool:
        """Check if authentication should be skipped for this path"""
        # Skip authentication for documentation and health check endpoints
        skip_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/api/v1/health",  # Health check endpoint
            "/health",
            "/ping"
        ]

        return any(path.startswith(skip_path) for skip_path in skip_paths)

    def _extract_api_key(self, request: Request) -> Optional[str]:
        """Extract API key from request headers"""
        auth_header = request.headers.get(self.config.auth.auth_header_name)
        
        if not auth_header:
            return None

        # Handle Bearer token format
        if self.config.auth.auth_scheme.lower() == "bearer":
            if auth_header.startswith("Bearer "):
                return auth_header[7:]  # Remove "Bearer " prefix
            else:
                return None
        
        # Handle direct API key in header
        return auth_header

    def _create_auth_error_response(self, message: str) -> JSONResponse:
        """Create standardized authentication error response"""
        return JSONResponse(
            status_code=401,
            content={
                "error": "Authentication failed",
                "message": message,
                "type": "authentication_error"
            },
            headers={
                "WWW-Authenticate": f"{self.config.auth.auth_scheme} realm=\"API\""
            }
        )


def extract_api_key_from_request(request: Request) -> Optional[str]:
    """
    Utility function to extract API key from request
    
    Can be used in route handlers for additional validation if needed.
    """
    config = get_config()
    auth_header = request.headers.get(config.auth.auth_header_name)
    
    if not auth_header:
        return None

    if config.auth.auth_scheme.lower() == "bearer":
        if auth_header.startswith("Bearer "):
            return auth_header[7:]
        else:
            return None
    
    return auth_header


def validate_api_key(api_key: str) -> bool:
    """
    Utility function to validate an API key
    
    Args:
        api_key: The API key to validate
        
    Returns:
        True if the API key is valid, False otherwise
    """
    config = get_config()
    return config.auth.is_valid_api_key(api_key)


def require_auth(request: Request) -> str:
    """
    Dependency function to require authentication in route handlers
    
    Args:
        request: FastAPI request object
        
    Returns:
        The validated API key
        
    Raises:
        HTTPException: If authentication fails
    """
    config = get_config()
    
    if not config.auth.enabled or not config.auth.require_auth_for_api:
        return "auth_disabled"
    
    api_key = extract_api_key_from_request(request)
    
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail="Missing API key",
            headers={"WWW-Authenticate": f"{config.auth.auth_scheme} realm=\"API\""}
        )
    
    if not validate_api_key(api_key):
        raise HTTPException(
            status_code=401,
            detail="Invalid API key",
            headers={"WWW-Authenticate": f"{config.auth.auth_scheme} realm=\"API\""}
        )
    
    return api_key
