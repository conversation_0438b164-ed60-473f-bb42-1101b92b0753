"""
Startup Checks for Application Initialization

Functions to perform database checks and cleanup operations when the application starts.

Key functionality:
- check_pending_conversations(): Updates conversations from 'pending' to 'completed'
  if their last message role is not 'user'. This handles cases where conversations
  were left in pending state due to unexpected shutdowns.
- check_conversation_integrity(): Performs additional integrity checks on conversation data.
"""

from typing import List
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from ..database import get_db_session
from ..models import Conversation, Message, ConversationStatus
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


async def check_pending_conversations():
    """
    Check all conversations with 'pending' status and update to 'completed' 
    if the last message role is not 'user'.
    
    This function is called during app startup to clean up conversations
    that may have been left in pending state due to unexpected shutdowns
    or other issues.
    """
    logger.info("🔍 Checking pending conversations on startup...")
    
    db = None
    try:
        db = await get_db_session()
        
        # Get all conversations with pending status
        pending_query = select(Conversation).where(
            Conversation.status == ConversationStatus.PENDING
        ).options(selectinload(Conversation.messages))
        
        result = await db.execute(pending_query)
        pending_conversations = result.scalars().all()
        
        if not pending_conversations:
            logger.info("📋 No pending conversations found")
            return
        
        logger.info(f"📋 Found {len(pending_conversations)} pending conversations")
        
        conversations_to_complete = []
        
        for conversation in pending_conversations:
            # Get the last message for this conversation
            last_message_query = select(Message).where(
                Message.conversation_id == conversation.id
            ).order_by(Message.created_at.desc()).limit(1)
            
            last_message_result = await db.execute(last_message_query)
            last_message = last_message_result.scalar_one_or_none()
            
            if last_message is None:
                # No messages in conversation, keep as pending
                logger.debug(f"📝 Conversation {conversation.id} has no messages, keeping as pending")
                continue
            
            if last_message.role != 'user':
                # Last message is not from user, mark for completion
                conversations_to_complete.append(conversation.id)
                logger.debug(f"📝 Conversation {conversation.id} last message role: '{last_message.role}' (not 'user'), marking for completion")
            else:
                # Last message is from user, keep as pending
                logger.debug(f"📝 Conversation {conversation.id} last message role: '{last_message.role}' (is 'user'), keeping as pending")
        
        # Update conversations to completed status
        if conversations_to_complete:
            update_query = update(Conversation).where(
                Conversation.id.in_(conversations_to_complete)
            ).values(status=ConversationStatus.COMPLETED)
            
            await db.execute(update_query)
            await db.commit()
            
            logger.success(f"✅ Updated {len(conversations_to_complete)} conversations from pending to completed")
            for conv_id in conversations_to_complete:
                logger.debug(f"   - {conv_id}")
        else:
            logger.info("📋 No pending conversations need to be updated to completed")
            
    except Exception as e:
        logger.error(f"❌ Error checking pending conversations: {e}")
        if db:
            await db.rollback()
        raise
    finally:
        if db:
            await db.close()


async def check_conversation_integrity():
    """
    Additional integrity checks for conversations.
    
    This function can be extended to perform other startup checks
    such as validating conversation data consistency, cleaning up
    orphaned records, etc.
    """
    logger.info("🔍 Performing conversation integrity checks...")
    
    db = None
    try:
        db = await get_db_session()
        
        # Check for conversations without any messages
        conversations_query = select(Conversation).options(selectinload(Conversation.messages))
        result = await db.execute(conversations_query)
        conversations = result.scalars().all()
        
        empty_conversations = []
        for conversation in conversations:
            if not conversation.messages:
                empty_conversations.append(conversation.id)
        
        if empty_conversations:
            logger.warning(f"⚠️ Found {len(empty_conversations)} conversations without messages:")
            for conv_id in empty_conversations:
                logger.warning(f"   - {conv_id}")
        else:
            logger.info("✅ All conversations have messages")
            
    except Exception as e:
        logger.error(f"❌ Error during integrity checks: {e}")
        raise
    finally:
        if db:
            await db.close()
