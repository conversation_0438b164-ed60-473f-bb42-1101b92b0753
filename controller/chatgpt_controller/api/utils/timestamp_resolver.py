"""
Timestamp Consistency and Conflict Resolution

Utilities for handling timestamp inconsistencies between ChatGPT API responses
and local database data, ensuring ChatGPT API data is treated as source of truth.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..models import Message, Conversation
from ..schemas import ChatGPTConversationData, ChatGPTMessage, MessageData
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


class TimestampResolver:
    """Handles timestamp consistency and conflict resolution"""
    
    def __init__(self):
        self.tolerance_seconds = 5  # Allow 5 seconds tolerance for timestamp differences
    
    def compare_timestamps(self, chatgpt_time: float, db_time: datetime) -> bool:
        """
        Compare ChatGPT timestamp with database timestamp
        
        Args:
            chatgpt_time: ChatGPT timestamp (Unix timestamp)
            db_time: Database timestamp (datetime object)
            
        Returns:
            True if timestamps are consistent (within tolerance), False otherwise
        """
        if not chatgpt_time or not db_time:
            return False
            
        chatgpt_datetime = datetime.fromtimestamp(chatgpt_time)
        time_diff = abs((chatgpt_datetime - db_time).total_seconds())
        
        return time_diff <= self.tolerance_seconds
    
    def should_update_from_chatgpt(
        self, 
        chatgpt_message: ChatGPTMessage, 
        db_message: Optional[Message]
    ) -> bool:
        """
        Determine if database message should be updated from ChatGPT data
        
        Args:
            chatgpt_message: Message from ChatGPT API
            db_message: Existing message in database (None if not exists)
            
        Returns:
            True if database should be updated, False otherwise
        """
        if not db_message:
            # New message, always create from ChatGPT data
            return True
            
        # If message source is already from ChatGPT API, check timestamps
        if db_message.source == "chatgpt_api":
            # Check if ChatGPT timestamps are newer or different
            if chatgpt_message.create_time and db_message.created_at:
                if not self.compare_timestamps(chatgpt_message.create_time, db_message.created_at):
                    logger.info(f"📅 Timestamp mismatch for message {chatgpt_message.id}, updating from ChatGPT")
                    return True
                    
            if chatgpt_message.update_time and db_message.sent_at:
                if not self.compare_timestamps(chatgpt_message.update_time, db_message.sent_at):
                    logger.info(f"📅 Update timestamp mismatch for message {chatgpt_message.id}, updating from ChatGPT")
                    return True
                    
            # Check content differences
            chatgpt_content = "\n".join(chatgpt_message.content.parts)
            if chatgpt_content != db_message.content:
                logger.info(f"📝 Content mismatch for message {chatgpt_message.id}, updating from ChatGPT")
                return True
                
            return False
        else:
            # Message was created locally, update with ChatGPT data if available
            logger.info(f"🔄 Converting local message {db_message.id} to ChatGPT source")
            return True
    
    async def resolve_conversation_conflicts(
        self,
        conversation_id: str,
        chatgpt_data: ChatGPTConversationData,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """
        Resolve conflicts between ChatGPT conversation data and database
        
        Args:
            conversation_id: Conversation ID
            chatgpt_data: ChatGPT conversation data
            db: Database session
            
        Returns:
            Dictionary with resolution results
        """
        results = {
            "conversation_updated": False,
            "messages_created": 0,
            "messages_updated": 0,
            "messages_deleted": 0,
            "conflicts_resolved": 0
        }
        
        # Get existing conversation
        conv_query = select(Conversation).where(Conversation.id == conversation_id)
        conv_result = await db.execute(conv_query)
        conversation = conv_result.scalar_one_or_none()
        
        # Update conversation timestamps if needed
        if conversation:
            chatgpt_create_time = datetime.fromtimestamp(chatgpt_data.create_time)
            chatgpt_update_time = datetime.fromtimestamp(chatgpt_data.update_time)
            
            # Check if conversation timestamps need updating
            if (not self.compare_timestamps(chatgpt_data.create_time, conversation.created_at) or
                not self.compare_timestamps(chatgpt_data.update_time, conversation.updated_at)):
                
                logger.info(f"🔄 Updating conversation {conversation_id} timestamps from ChatGPT")
                conversation.created_at = chatgpt_create_time
                conversation.updated_at = chatgpt_update_time
                conversation.title = chatgpt_data.title
                results["conversation_updated"] = True
                results["conflicts_resolved"] += 1
        
        # Get existing messages
        msg_query = select(Message).where(Message.conversation_id == conversation_id)
        msg_result = await db.execute(msg_query)
        existing_messages = {msg.message_id: msg for msg in msg_result.scalars().all() if msg.message_id}
        
        # Process ChatGPT messages
        chatgpt_message_ids = set()
        
        for node_id, node in chatgpt_data.mapping.items():
            if not node.message or not node.message.content.parts:
                continue
                
            chatgpt_message_ids.add(node.message.id)
            existing_msg = existing_messages.get(node.message.id)
            
            if self.should_update_from_chatgpt(node.message, existing_msg):
                if existing_msg:
                    # Update existing message
                    await self._update_message_from_chatgpt(existing_msg, node.message)
                    results["messages_updated"] += 1
                    logger.info(f"🔄 Updated message {node.message.id} from ChatGPT")
                else:
                    # Create new message
                    new_message = await self._create_message_from_chatgpt(
                        conversation_id, node.message, node.parent, db
                    )
                    results["messages_created"] += 1
                    logger.info(f"✨ Created new message {node.message.id} from ChatGPT")
                
                results["conflicts_resolved"] += 1
        
        # Handle orphaned messages (exist in DB but not in ChatGPT response)
        orphaned_messages = [
            msg for msg_id, msg in existing_messages.items() 
            if msg_id not in chatgpt_message_ids and msg.source == "chatgpt_api"
        ]
        
        if conversation:
            conversation.is_cached = True
            conversation.updated_at = datetime.now()
        
        for orphaned_msg in orphaned_messages:
            logger.warning(f"🗑️ Removing orphaned message {orphaned_msg.message_id}")
            await db.delete(orphaned_msg)
            results["messages_deleted"] += 1
            results["conflicts_resolved"] += 1
        
        return results
    
    async def _update_message_from_chatgpt(
        self, 
        db_message: Message, 
        chatgpt_message: ChatGPTMessage
    ) -> None:
        """Update database message with ChatGPT data"""
        db_message.content = "\n".join(chatgpt_message.content.parts)
        db_message.content_type = chatgpt_message.content.content_type
        db_message.role = chatgpt_message.author.role
        db_message.model = chatgpt_message.metadata.model_slug
        db_message.source = "chatgpt_api"
        
        if chatgpt_message.create_time:
            db_message.created_at = datetime.fromtimestamp(chatgpt_message.create_time)
        if chatgpt_message.update_time:
            db_message.sent_at = datetime.fromtimestamp(chatgpt_message.update_time)
            
        # Update metadata
        db_message.message_metadata = {
            "weight": chatgpt_message.weight,
            "status": chatgpt_message.status,
            "end_turn": chatgpt_message.end_turn,
            "recipient": chatgpt_message.recipient,
            "channel": chatgpt_message.channel,
            **chatgpt_message.metadata.model_dump()
        }
    
    async def _create_message_from_chatgpt(
        self,
        conversation_id: str,
        chatgpt_message: ChatGPTMessage,
        parent_id: Optional[str],
        db: AsyncSession
    ) -> Message:
        """Create new message from ChatGPT data"""
        message = Message(
            conversation_id=conversation_id,
            role=chatgpt_message.author.role,
            content="\n".join(chatgpt_message.content.parts),
            content_type=chatgpt_message.content.content_type,
            message_id=chatgpt_message.id,
            parent_id=parent_id,
            model=chatgpt_message.metadata.model_slug,
            source="chatgpt_api",
            created_at=datetime.fromtimestamp(chatgpt_message.create_time) if chatgpt_message.create_time else datetime.now(),
            sent_at=datetime.fromtimestamp(chatgpt_message.update_time) if chatgpt_message.update_time else None,
            message_metadata={
                "weight": chatgpt_message.weight,
                "status": chatgpt_message.status,
                "end_turn": chatgpt_message.end_turn,
                "recipient": chatgpt_message.recipient,
                "channel": chatgpt_message.channel,
                **chatgpt_message.metadata.model_dump()
            }
        )
        
        db.add(message)
        return message


# Global instance
timestamp_resolver = TimestampResolver()
