"""
API Utilities Package

Utility modules for the ChatGPT Controller API including browser control,
conversation parsing, and event handling.
"""

from .browser_utils import (
    extract_conversation_id_from_url,
    wait_for_element_query_result_async,
    wait_for_page_load,
    get_current_url,
    click_element_by_xpath,
    input_text_by_xpath,
)

from .conversation_parser import (
    parse_chatgpt_conversation_data,
    extract_messages_from_conversation,
    update_conversation_from_api_data,
)

from .event_handlers import (
    handle_conversation_api_response,
    setup_conversation_event_listeners,
)

__all__ = [
    # Browser utilities
    "extract_conversation_id_from_url",
    "wait_for_element_query_result_async", 
    "wait_for_page_load",
    "get_current_url",
    "click_element_by_xpath",
    "input_text_by_xpath",
    
    # Conversation parser
    "parse_chatgpt_conversation_data",
    "extract_messages_from_conversation", 
    "update_conversation_from_api_data",
    
    # Event handlers
    "handle_conversation_api_response",
    "setup_conversation_event_listeners",
]
