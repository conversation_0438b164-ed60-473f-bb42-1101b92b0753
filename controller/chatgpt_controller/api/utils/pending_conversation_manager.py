"""
Pending Conversation Manager

管理等待ChatGPT响应的对话，实现智能后台更新机制
适配Deep Research对话的长响应时间特点
"""

import asyncio
import random
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Set
from dataclasses import dataclass
from enum import Enum

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from ..models import Conversation, Message, ConversationStatus
from ..database import get_db
from ..config.pending_config import PendingConversationConfig, get_config_by_conversation_type, DEFAULT_CONFIG
from ...utils.logging import RichLogger
from ...core.message_types import MessageType

logger = RichLogger(__name__)


class CircuitBreakerState(str, Enum):
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断开启
    HALF_OPEN = "half_open"  # 半开状态


@dataclass
class PendingConversation:
    """等待更新的对话信息"""
    conversation_id: str
    added_time: datetime
    last_attempt_time: Optional[datetime] = None
    attempt_count: int = 0
    last_message_count: int = 0
    last_update_time: Optional[datetime] = None
    next_attempt_time: Optional[datetime] = None
    consecutive_failures: int = 0


class CircuitBreaker:
    """熔断器 - 防止过度请求触发风控"""

    def __init__(self, config: PendingConversationConfig):
        self.failure_threshold = config.circuit_breaker_failure_threshold
        self.recovery_timeout = config.circuit_breaker_recovery_timeout
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state = CircuitBreakerState.CLOSED
        
    def can_execute(self) -> bool:
        """检查是否可以执行请求"""
        if self.state == CircuitBreakerState.CLOSED:
            return True
            
        if self.state == CircuitBreakerState.OPEN:
            if self.last_failure_time and \
               (datetime.now() - self.last_failure_time).total_seconds() > self.recovery_timeout:
                self.state = CircuitBreakerState.HALF_OPEN
                logger.info("🔄 Circuit breaker entering HALF_OPEN state")
                return True
            return False
            
        # HALF_OPEN state
        return True
    
    def record_success(self):
        """记录成功请求"""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.state = CircuitBreakerState.CLOSED
            self.failure_count = 0
            logger.info("✅ Circuit breaker reset to CLOSED state")
    
    def record_failure(self):
        """记录失败请求"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            logger.warning(f"⚠️ Circuit breaker OPEN - too many failures ({self.failure_count})")


class IntelligentScheduler:
    """智能调度器 - 适配不同类型对话的时间特点"""

    def __init__(self, config: PendingConversationConfig):
        self.config = config
        self.update_intervals = config.update_intervals
        self.max_attempts = config.max_attempts
        self.random_offset_range = config.random_offset_range
        
    def calculate_next_interval(self, attempt_count: int) -> int:
        """计算下次更新间隔"""
        # 获取基础间隔
        base_interval = self.update_intervals[min(attempt_count, len(self.update_intervals) - 1)]
        
        # 添加随机偏移避免同时请求
        random_offset = random.uniform(-self.random_offset_range, self.random_offset_range)
        interval = int(base_interval * (1 + random_offset))
        
        return interval
    
    def should_continue_updating(self, pending_conv: PendingConversation) -> bool:
        """判断是否应该继续更新"""
        # 检查最大尝试次数
        if pending_conv.attempt_count >= self.max_attempts:
            return False

        # 检查对话年龄
        age_hours = (datetime.now() - pending_conv.added_time).total_seconds() / 3600
        if age_hours > self.config.max_conversation_age_hours:
            return False

        # 检查连续失败次数
        if pending_conv.consecutive_failures > self.config.max_consecutive_failures:
            return False

        return True


class PendingConversationManager:
    """后台对话更新管理器"""

    def __init__(self, config: PendingConversationConfig = None, websocket_server=None):
        self.config = config or DEFAULT_CONFIG
        self.pending_conversations: Dict[str, PendingConversation] = {}
        self.scheduler = IntelligentScheduler(self.config)
        self.circuit_breaker = CircuitBreaker(self.config)
        self.websocket_server = websocket_server
        self.is_running = False
        self.update_tasks: Dict[str, asyncio.Task] = {}

    def set_websocket_server(self, websocket_server):
        """设置WebSocket服务器实例"""
        self.websocket_server = websocket_server
        if websocket_server:
            logger.info("🔌 WebSocket server set for pending conversation manager")
        else:
            logger.warning("⚠️ WebSocket server set to None - some features may be limited")

    async def start(self):
        """启动管理器"""
        if self.is_running:
            return
            
        self.is_running = True
        logger.info("🚀 Pending Conversation Manager started")
        
        # 启动清理任务
        asyncio.create_task(self._cleanup_task())
    
    async def stop(self):
        """停止管理器"""
        self.is_running = False
        
        # 取消所有更新任务
        for task in self.update_tasks.values():
            task.cancel()
        self.update_tasks.clear()
        
        logger.info("🛑 Pending Conversation Manager stopped")
    
    async def add_pending_conversation(self, conversation_id: str, auto_config: bool = True):
        """添加等待更新的对话"""
        if conversation_id in self.pending_conversations:
            logger.debug(f"📝 Conversation {conversation_id} already in pending list")
            return

        # 获取对话信息和消息数量
        async for db in get_db():
            try:
                # 获取对话信息
                conv_query = select(Conversation).where(Conversation.id == conversation_id)
                conv_result = await db.execute(conv_query)
                conversation = conv_result.scalar_one_or_none()

                # 获取消息
                msg_query = select(Message).where(Message.conversation_id == conversation_id)
                msg_result = await db.execute(msg_query)
                messages = msg_result.scalars().all()
                message_count = len(messages)

                # 智能选择配置
                if auto_config and conversation:
                    optimal_config = get_config_by_conversation_type(
                        conversation.title,
                        conversation.mode
                    )
                    if optimal_config != self.config:
                        logger.info(f"🎯 Using optimized config for conversation {conversation_id}: {conversation.mode or 'standard'}")
                        # 为这个对话创建专用的调度器
                        # 注意：这里我们保持全局配置，但记录推荐配置用于日志

                break
            except Exception as e:
                logger.error(f"❌ Error getting conversation info: {e}")
                message_count = 0
                break
        
        pending_conv = PendingConversation(
            conversation_id=conversation_id,
            added_time=datetime.now(),
            last_message_count=message_count
        )
        
        self.pending_conversations[conversation_id] = pending_conv
        logger.info(f"➕ Added conversation {conversation_id} to pending list (current messages: {message_count})")
        
        # 启动更新任务
        await self._schedule_update(conversation_id)
    
    async def remove_pending_conversation(self, conversation_id: str):
        """移除等待更新的对话"""
        if conversation_id in self.pending_conversations:
            # 取消更新任务
            if conversation_id in self.update_tasks:
                self.update_tasks[conversation_id].cancel()
                del self.update_tasks[conversation_id]
            
            del self.pending_conversations[conversation_id]
            
            async for db in get_db():
                try:
                    # 更新数据库状态
                    query = select(Conversation).where(Conversation.id == conversation_id)
                    result = await db.execute(query)
                    conversation = result.scalar_one_or_none()

                    if conversation:
                        conversation.status = ConversationStatus.COMPLETED
                        await db.commit()
                        logger.info(f"✅ Conversation {conversation_id} status set to COMPLETED")
                        break
                except Exception as e:
                    logger.error(f"❌ Error updating conversation status: {e}")
                    await db.rollback()
        
            logger.info(f"➖ Removed conversation {conversation_id} from pending list")
    
    async def _schedule_update(self, conversation_id: str):
        """调度对话更新"""
        if not self.is_running:
            return
            
        pending_conv = self.pending_conversations.get(conversation_id)
        if not pending_conv:
            return
            
        # 计算等待时间
        interval = self.scheduler.calculate_next_interval(pending_conv.attempt_count)
        pending_conv.next_attempt_time = datetime.now() + timedelta(seconds=interval)
        
        # 创建更新任务
        async def update_task():
            try:
                await asyncio.sleep(interval)
                await self._update_conversation(conversation_id)
            except asyncio.CancelledError:
                logger.debug(f"🚫 Update task cancelled for {conversation_id}")
            except Exception as e:
                logger.error(f"❌ Error in update task for {conversation_id}: {e}")
        
        # 取消之前的任务
        if conversation_id in self.update_tasks:
            self.update_tasks[conversation_id].cancel()
        
        # 启动新任务
        self.update_tasks[conversation_id] = asyncio.create_task(update_task())

        logger.debug(f"📅 Conversation {conversation_id} Next update interval: {interval}s (attempt {pending_conv.attempt_count + 1})")
    
    async def _update_conversation(self, conversation_id: str):
        """更新单个对话"""
        pending_conv = self.pending_conversations.get(conversation_id)
        if not pending_conv:
            return
            
        # 检查熔断器
        if not self.circuit_breaker.can_execute():
            logger.warning(f"⚠️ Circuit breaker open, skipping update for {conversation_id}")
            await self._schedule_update(conversation_id)  # 重新调度
            return
        
        # 检查是否应该继续更新
        if not self.scheduler.should_continue_updating(pending_conv):
            logger.info(f"🛑 Stopping updates for {conversation_id} (max attempts/age reached)")
            await self.remove_pending_conversation(conversation_id)
            return
        
        try:
            logger.info(f"🔄 Updating conversation {conversation_id} (attempt {pending_conv.attempt_count + 1})")

            # 发起浏览器请求
            success = await self._request_conversation_update(conversation_id)

            if success:
                logger.debug(f"🌐 Browser request sent for {conversation_id}, waiting for data processing...")

                # 等待浏览器处理并检查数据（带重试）
                has_new_data = await self._check_for_new_data_with_delay(conversation_id, pending_conv)

                if has_new_data:
                    logger.success(f"✅ New data detected for {conversation_id}")
                    self.circuit_breaker.record_success()
                    await self.remove_pending_conversation(conversation_id)
                    return
                else:
                    logger.debug(f"📊 No new data for {conversation_id} after waiting and retries")
                    self.circuit_breaker.record_success()
            else:
                logger.warning(f"⚠️ Failed to send browser request for {conversation_id}")
                self.circuit_breaker.record_failure()
                pending_conv.consecutive_failures += 1

            # 更新尝试信息
            pending_conv.attempt_count += 1
            pending_conv.last_attempt_time = datetime.now()

            # 调度下次更新
            await self._schedule_update(conversation_id)
            
        except Exception as e:
            logger.error(f"❌ Error updating conversation {conversation_id}: {e}")
            self.circuit_breaker.record_failure()
            pending_conv.consecutive_failures += 1
            pending_conv.attempt_count += 1
            await self._schedule_update(conversation_id)
    
    async def _request_conversation_update(self, conversation_id: str) -> bool:
        """发起浏览器请求更新对话 - 简化版本，只发起网页请求"""
        try:
            # 检查WebSocket服务器是否可用
            if not self.websocket_server:
                logger.warning(f"⚠️ WebSocket server not available for conversation {conversation_id}")
                return False

            # 构造对话URL
            url = f"https://chatgpt.com/c/{conversation_id}"

            # 发送简单的打开命令，类似get_conversation的设计
            command_data = {
                "url": url,
                "newTab": False,
                "focus": True,
            }

            success = await self.websocket_server.send_command(
                MessageType.OPEN_CHATGPT, command_data
            )

            if success:
                logger.debug(f"🌐 Successfully requested conversation page: {url}")
                return True
            else:
                logger.warning(f"⚠️ Failed to request conversation page: {url}")
                return False

        except Exception as e:
            logger.error(f"❌ Error requesting conversation update: {e}")
            return False

    async def _check_for_new_data_with_delay(self, conversation_id: str, pending_conv: PendingConversation) -> bool:
        """等待一段时间后检查数据库中是否有新数据，支持重试"""
        # 首先等待初始延迟，让浏览器有时间处理
        initial_delay = self.config.data_check_delay_seconds
        logger.debug(f"⏳ Waiting {initial_delay}s for browser to process conversation {conversation_id}")
        await asyncio.sleep(initial_delay)

        # 进行多次检查，每次间隔一定时间
        for retry in range(self.config.data_check_retries):
            logger.debug(f"🔍 Checking for new data (attempt {retry + 1}/{self.config.data_check_retries})")

            has_new_data = await self._check_for_new_data(conversation_id, pending_conv)
            if has_new_data:
                logger.debug(f"✅ New data found on attempt {retry + 1}")
                return True

            # 如果不是最后一次尝试，等待重试间隔
            if retry < self.config.data_check_retries - 1:
                retry_delay = self.config.data_check_retry_interval
                logger.debug(f"⏳ No new data yet, waiting {retry_delay}s before retry...")
                await asyncio.sleep(retry_delay)

        logger.debug(f"📊 No new data found after {self.config.data_check_retries} attempts")
        return False

    async def _check_for_new_data(self, conversation_id: str, pending_conv: PendingConversation) -> bool:
        """检查数据库中是否有新数据"""
        db_gen = get_db()
        try:
            db = await db_gen.__anext__()

            # 获取当前消息
            query = select(Message).where(Message.conversation_id == conversation_id)
            result = await db.execute(query)
            messages = result.scalars().all()

            current_count = len(messages)
            logger.debug(f"Conversation {conversation_id}: {current_count} messages, last check: {pending_conv.last_message_count}")

            # 检查消息数量是否增加
            if current_count > pending_conv.last_message_count:
                logger.debug(f"✅ New messages detected: {current_count} > {pending_conv.last_message_count}")
                # 更新消息计数，避免重复检测
                pending_conv.last_message_count = current_count
                pending_conv.last_update_time = datetime.now()
                return True

            # 检查最后一条消息是否为assistant (ChatGPT响应)
            if messages:
                last_message = max(messages, key=lambda m: m.created_at or datetime.min)
                logger.debug(f"Conversation {conversation_id}: Last message role is {last_message.role}")
                if last_message.role == "assistant" and \
                   (not pending_conv.last_update_time or
                    last_message.created_at > pending_conv.last_update_time):
                    logger.debug("✅ New assistant message detected")
                    # 更新时间戳，避免重复检测
                    pending_conv.last_update_time = last_message.created_at
                    return True

            logger.debug(f"📊 No new data detected for {conversation_id}")
            return False

        except Exception as e:
            logger.error(f"❌ Error checking for new data: {e}")
            return False
        finally:
            try:
                await db_gen.aclose()
            except Exception:
                pass
    
    async def _cleanup_task(self):
        """清理任务 - 定期清理过期的对话"""
        while self.is_running:
            try:
                cleanup_interval = self.config.cleanup_interval_hours * 3600
                await asyncio.sleep(cleanup_interval)

                expired_conversations = []
                for conv_id, pending_conv in self.pending_conversations.items():
                    age_hours = (datetime.now() - pending_conv.added_time).total_seconds() / 3600
                    if age_hours > self.config.max_conversation_age_hours:
                        expired_conversations.append(conv_id)

                for conv_id in expired_conversations:
                    await self.remove_pending_conversation(conv_id)
                    logger.info(f"🧹 Cleaned up expired conversation {conv_id}")

                # 同时更新数据库中过期的pending状态
                await self._update_expired_pending_status()

            except Exception as e:
                logger.error(f"❌ Error in cleanup task: {e}")

    async def _update_expired_pending_status(self):
        """更新数据库中过期的pending状态"""
        async for db in get_db():
            try:
                # 计算过期时间
                expire_time = datetime.now() - timedelta(hours=self.config.auto_complete_timeout_hours)

                # 更新过期的pending对话为completed状态
                update_query = (
                    update(Conversation)
                    .where(
                        Conversation.status == ConversationStatus.PENDING,
                        Conversation.updated_at < expire_time
                    )
                    .values(status=ConversationStatus.COMPLETED)
                )

                result = await db.execute(update_query)
                await db.commit()

                if result.rowcount > 0:
                    logger.info(f"🔄 Auto-completed {result.rowcount} expired pending conversations")

                break
            except Exception as e:
                logger.error(f"❌ Error updating expired pending status: {e}")
                break
    
    def get_status(self) -> dict:
        """获取管理器状态"""
        return {
            "is_running": self.is_running,
            "pending_count": len(self.pending_conversations),
            "circuit_breaker_state": self.circuit_breaker.state.value,
            "config": {
                "data_check_delay_seconds": self.config.data_check_delay_seconds,
                "data_check_retries": self.config.data_check_retries,
                "data_check_retry_interval": self.config.data_check_retry_interval,
                "update_intervals": self.config.update_intervals,
                "max_attempts": self.config.max_attempts,
            },
            "pending_conversations": [
                {
                    "conversation_id": conv_id,
                    "added_time": pending_conv.added_time.isoformat(),
                    "attempt_count": pending_conv.attempt_count,
                    "consecutive_failures": pending_conv.consecutive_failures,
                    "last_message_count": pending_conv.last_message_count,
                    "last_update_time": pending_conv.last_update_time.isoformat() if pending_conv.last_update_time else None,
                    "last_attempt_time": pending_conv.last_attempt_time.isoformat() if pending_conv.last_attempt_time else None,
                    "next_attempt_time": pending_conv.next_attempt_time.isoformat() if pending_conv.next_attempt_time else None,
                }
                for conv_id, pending_conv in self.pending_conversations.items()
            ]
        }


# 全局管理器实例 - WebSocket服务器将在FastAPI启动时设置
pending_manager = PendingConversationManager(websocket_server=None)


def set_global_websocket_server(websocket_server):
    """为全局pending manager设置WebSocket服务器"""
    pending_manager.set_websocket_server(websocket_server)
