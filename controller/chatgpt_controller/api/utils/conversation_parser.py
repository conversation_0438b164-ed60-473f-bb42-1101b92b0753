"""
Conversation Data Parser

Functions for parsing ChatGPT conversation API responses and extracting
structured message data for storage in the database.
"""

import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..models import Conversation, Message
from ..schemas import ChatGPTConversationData, MessageData
from .timestamp_resolver import timestamp_resolver
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


def parse_chatgpt_conversation_data(conversation_data: Dict[str, Any]) -> Optional[ChatGPTConversationData]:
    """Parse ChatGPT conversation API response data"""
    try:
        # Validate and parse the conversation data
        parsed_data = ChatGPTConversationData(**conversation_data)
        return parsed_data
    except Exception as e:
        logger.error(f"❌ Error parsing ChatGPT conversation data: {e}")
        logger.debug(f"🔍 Raw data: {conversation_data}")
        return None


async def extract_messages_from_conversation(conversation_data: ChatGPTConversationData) -> List[MessageData]:
    """Extract messages from ChatGPT conversation data"""
    messages = []
    
    try:
        # Traverse the conversation mapping to extract messages
        for node in conversation_data.mapping.values():
            if node.message and node.message.content.parts:
                # Skip system messages that are visually hidden
                if (node.message.metadata.is_visually_hidden_from_conversation or 
                    not node.message.content.parts or 
                    not any(part.strip() for part in node.message.content.parts)):
                    continue
                
                # Create message data
                message_data = MessageData(
                    id=len(messages) + 1,  # Temporary ID, will be replaced when saved to DB
                    conversation_id=conversation_data.conversation_id,
                    role=node.message.author.role,
                    content="\n".join(node.message.content.parts),
                    content_type=node.message.content.content_type,
                    message_id=node.message.id,
                    parent_id=node.parent,
                    model=node.message.metadata.model_slug,
                    source="chatgpt_api",  # Mark as coming from ChatGPT API
                    created_at=datetime.fromtimestamp(node.message.create_time) if node.message.create_time else datetime.now(),
                    sent_at=datetime.fromtimestamp(node.message.update_time) if node.message.update_time else None,
                    metadata={
                        "weight": node.message.weight,
                        "status": node.message.status,
                        "end_turn": node.message.end_turn,
                        "recipient": node.message.recipient,
                        **node.message.metadata.model_dump(),
                    }
                )
                messages.append(message_data)
        
        # Sort messages by creation time
        messages.sort(key=lambda m: m.created_at)
        
        logger.debug(f"📝 Extracted {len(messages)} messages from conversation {conversation_data.conversation_id}")
        return messages
        
    except Exception as e:
        logger.error(f"❌ Error extracting messages from conversation: {e}")
        logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
        return []


async def update_conversation_from_api_data(
    conversation_id: str,
    conversation_data: ChatGPTConversationData,
    db: AsyncSession
) -> bool:
    """Update conversation and messages from ChatGPT API data with conflict resolution"""
    try:
        logger.info(f"🔄 Processing ChatGPT conversation data for {conversation_id}")

        # Use timestamp resolver to handle conflicts
        resolution_results = await timestamp_resolver.resolve_conversation_conflicts(
            conversation_id, conversation_data, db
        )

        # Commit all changes
        await db.commit()

        # Log resolution results
        logger.success(
            f"✅ Successfully updated conversation {conversation_id}: "
            f"created={resolution_results['messages_created']}, "
            f"updated={resolution_results['messages_updated']}, "
            f"deleted={resolution_results['messages_deleted']}, "
            f"conflicts_resolved={resolution_results['conflicts_resolved']}"
        )

        return True

    except Exception as e:
        logger.error(f"❌ Error updating conversation from API data: {e}")
        logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
        await db.rollback()
        return False
