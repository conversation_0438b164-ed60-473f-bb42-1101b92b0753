"""
Database Migration: Add Message Source Column

Adds the 'source' column to the messages table to track message origins
(user, chatgpt_api, system) and creates an index for efficient querying.
"""

import asyncio
import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from ..database import get_database_url
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


async def add_message_source_column():
    """Add source column to messages table"""
    
    # Get database URL
    database_url = get_database_url()
    
    # Create async engine
    engine = create_async_engine(database_url, echo=False)
    
    # Create session factory
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        try:
            logger.info("🔧 Starting database migration: add message source column")
            
            # Check if column already exists
            check_column_sql = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'messages' AND column_name = 'source'
            """
            
            result = await session.execute(text(check_column_sql))
            existing_column = result.fetchone()
            
            if existing_column:
                logger.info("✅ Source column already exists, skipping migration")
                return
            
            # Add source column
            logger.info("📝 Adding source column to messages table...")
            add_column_sql = """
            ALTER TABLE messages 
            ADD COLUMN source VARCHAR(50) NOT NULL DEFAULT 'user'
            """
            await session.execute(text(add_column_sql))
            
            # Create index on source column
            logger.info("📝 Creating index on source column...")
            create_index_sql = """
            CREATE INDEX ix_msg_source ON messages (source)
            """
            await session.execute(text(create_index_sql))
            
            # Update existing messages based on role
            logger.info("📝 Updating existing messages with appropriate source...")
            update_assistant_sql = """
            UPDATE messages 
            SET source = 'chatgpt_api' 
            WHERE role = 'assistant' AND source = 'user'
            """
            await session.execute(text(update_assistant_sql))
            
            update_system_sql = """
            UPDATE messages 
            SET source = 'system' 
            WHERE role = 'system' AND source = 'user'
            """
            await session.execute(text(update_system_sql))
            
            # Commit changes
            await session.commit()
            
            logger.success("✅ Successfully added message source column and updated existing data")
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            await session.rollback()
            raise
        finally:
            await engine.dispose()


async def rollback_message_source_column():
    """Rollback: Remove source column from messages table"""
    
    # Get database URL
    database_url = get_database_url()
    
    # Create async engine
    engine = create_async_engine(database_url, echo=False)
    
    # Create session factory
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        try:
            logger.info("🔧 Rolling back database migration: remove message source column")
            
            # Drop index
            logger.info("📝 Dropping index on source column...")
            drop_index_sql = "DROP INDEX IF EXISTS ix_msg_source"
            await session.execute(text(drop_index_sql))
            
            # Drop column
            logger.info("📝 Removing source column from messages table...")
            drop_column_sql = "ALTER TABLE messages DROP COLUMN IF EXISTS source"
            await session.execute(text(drop_column_sql))
            
            # Commit changes
            await session.commit()
            
            logger.success("✅ Successfully rolled back message source column migration")
            
        except Exception as e:
            logger.error(f"❌ Rollback failed: {e}")
            await session.rollback()
            raise
        finally:
            await engine.dispose()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        asyncio.run(rollback_message_source_column())
    else:
        asyncio.run(add_message_source_column())
