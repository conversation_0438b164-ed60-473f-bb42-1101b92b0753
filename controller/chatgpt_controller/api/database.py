"""
Database Configuration and Session Management

SQLAlchemy setup with SQLite backend for conversation caching and persistence.
"""

import os
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData, text

from ..core.config import get_config
from ..utils.logging import RichLogger

# Create logger
logger = RichLogger(__name__)

# Database engine and session
engine = None
async_session_maker = None


class Base(DeclarativeBase):
    """Base class for all database models"""
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s"
        }
    )


async def init_db():
    """Initialize database connection and create tables"""
    global engine, async_session_maker
    
    config = get_config()
    
    # Create data directory if it doesn't exist
    db_path = config.database.url.replace("sqlite+aiosqlite:///", "")
    db_dir = os.path.dirname(db_path)
    if db_dir and not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        logger.info(f"Created database directory: {db_dir}")
    
    # Create async engine
    engine = create_async_engine(
        config.database.url,
        echo=config.database.echo,
        pool_pre_ping=True,
        pool_recycle=3600,
    )
    
    # Create session maker
    async_session_maker = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    
    # Import models to ensure they're registered
    from .models import Conversation, Message, ConversationCache
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info(f"Database initialized: {config.database.url}")


async def close_db():
    """Close database connections"""
    global engine
    
    if engine:
        await engine.dispose()
        logger.info("Database connections closed")


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session
    
    Yields:
        AsyncSession: Database session
    """
    if not async_session_maker:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db_session() -> AsyncSession:
    """
    Get a database session for use outside of FastAPI dependency injection
    
    Returns:
        AsyncSession: Database session
    """
    if not async_session_maker:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    return async_session_maker()


# Health check function
async def check_db_health() -> bool:
    """
    Check database connectivity

    Returns:
        bool: True if database is healthy, False otherwise
    """
    try:
        session = await get_db_session()
        try:
            # Simple query to test connection
            result = await session.execute(text("SELECT 1"))
            return result.scalar() == 1
        finally:
            await session.close()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False
