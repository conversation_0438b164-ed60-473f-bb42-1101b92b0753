"""
Pending Conversation Configuration

配置后台对话更新管理器的参数，针对Deep Research对话优化
"""

from dataclasses import dataclass
from typing import List


@dataclass
class PendingConversationConfig:
    """后台对话更新配置"""
    
    # 调度器配置
    update_intervals: List[int]  # 更新间隔序列（秒）
    max_attempts: int           # 最大尝试次数
    random_offset_range: float  # 随机偏移范围 (0.0-1.0)
    max_conversation_age_hours: int  # 对话最大年龄（小时）
    max_consecutive_failures: int    # 最大连续失败次数
    
    # 熔断器配置
    circuit_breaker_failure_threshold: int  # 熔断器失败阈值
    circuit_breaker_recovery_timeout: int   # 熔断器恢复超时（秒）
    
    # 对话状态配置
    pending_trigger_age_minutes: int  # 触发pending状态的对话年龄（分钟）
    auto_complete_timeout_hours: int  # 自动完成超时（小时）
    
    # 批处理配置
    batch_update_size: int      # 批量更新大小
    cleanup_interval_hours: int # 清理间隔（小时）

    # 数据检查配置
    data_check_delay_seconds: int = 5   # 发送更新命令后等待多久检查数据
    data_check_retries: int = 3         # 检查数据的重试次数
    data_check_retry_interval: int = 3  # 重试间隔（秒）


# Deep Research 对话配置
# 考虑到Deep Research对话可能需要10-30分钟的响应时间
DEEP_RESEARCH_CONFIG = PendingConversationConfig(
    # 调度间隔：2分钟 -> 5分钟 -> 10分钟 -> 15分钟 -> 20分钟 -> 30分钟
    update_intervals=[120, 300, 600, 900, 1200, 1800],
    max_attempts=20,  # 最多尝试20次（约10小时）
    random_offset_range=0.3,  # ±30%随机偏移
    max_conversation_age_hours=12,  # 12小时后停止更新
    max_consecutive_failures=6,     # 连续失败6次后停止
    
    # 熔断器：5次失败后熔断，10分钟后恢复
    circuit_breaker_failure_threshold=5,
    circuit_breaker_recovery_timeout=600,
    
    # 对话状态：30分钟内的对话才考虑pending，6小时后自动完成
    pending_trigger_age_minutes=30,
    auto_complete_timeout_hours=6,
    
    # 批处理：每次最多更新5个对话，每2小时清理一次
    batch_update_size=5,
    cleanup_interval_hours=2,

    # 数据检查：发送命令后等待5秒再检查，重试2次，每次间隔3秒
    data_check_delay_seconds=5,
    data_check_retries=2,
    data_check_retry_interval=3,
)

# 普通对话配置
# 适用于常规ChatGPT对话，响应时间通常在1-5分钟
STANDARD_CONFIG = PendingConversationConfig(
    # 调度间隔：30秒 -> 1分钟 -> 2分钟 -> 5分钟 -> 10分钟
    update_intervals=[10, 30, 60, 120, 300, 600],
    max_attempts=15,  # 最多尝试15次（约2小时）
    random_offset_range=0.2,  # ±20%随机偏移
    max_conversation_age_hours=3,   # 3小时后停止更新
    max_consecutive_failures=5,     # 连续失败5次后停止
    
    # 熔断器：3次失败后熔断，5分钟后恢复
    circuit_breaker_failure_threshold=3,
    circuit_breaker_recovery_timeout=300,
    
    # 对话状态：15分钟内的对话才考虑pending，2小时后自动完成
    pending_trigger_age_minutes=15,
    auto_complete_timeout_hours=2,
    
    # 批处理：每次最多更新3个对话，每小时清理一次
    batch_update_size=3,
    cleanup_interval_hours=1,

    # 数据检查：发送命令后等待5秒再检查，重试2次，每次间隔3秒
    data_check_delay_seconds=5,
    data_check_retries=10,
    data_check_retry_interval=1,
)

# 快速模式配置
# 适用于需要快速响应的场景
FAST_CONFIG = PendingConversationConfig(
    # 调度间隔：15秒 -> 30秒 -> 1分钟 -> 2分钟 -> 5分钟
    update_intervals=[15, 30, 60, 120, 300],
    max_attempts=10,  # 最多尝试10次（约30分钟）
    random_offset_range=0.1,  # ±10%随机偏移
    max_conversation_age_hours=1,   # 1小时后停止更新
    max_consecutive_failures=3,     # 连续失败3次后停止
    
    # 熔断器：2次失败后熔断，2分钟后恢复
    circuit_breaker_failure_threshold=2,
    circuit_breaker_recovery_timeout=120,
    
    # 对话状态：5分钟内的对话才考虑pending，30分钟后自动完成
    pending_trigger_age_minutes=5,
    auto_complete_timeout_hours=0.5,
    
    # 批处理：每次最多更新2个对话，每30分钟清理一次
    batch_update_size=2,
    cleanup_interval_hours=0.5,

    # 数据检查：发送命令后等待5秒再检查，重试2次，每次间隔2秒
    data_check_delay_seconds=5,
    data_check_retries=2,
    data_check_retry_interval=2,
)


def get_config_by_mode(mode: str = None) -> PendingConversationConfig:
    """根据对话模式获取配置"""
    if mode and "research" in mode.lower():
        return DEEP_RESEARCH_CONFIG
    elif mode and mode.lower() in ["fast", "quick", "speed"]:
        return FAST_CONFIG
    else:
        return STANDARD_CONFIG


def get_config_by_conversation_type(conversation_title: str = None, mode: str = None) -> PendingConversationConfig:
    """根据对话类型智能选择配置"""
    
    # 检查标题中的关键词
    if conversation_title:
        title_lower = conversation_title.lower()
        research_keywords = ["research", "analyze", "deep", "comprehensive", "detailed", "thorough"]
        fast_keywords = ["quick", "fast", "brief", "short", "simple"]
        
        if any(keyword in title_lower for keyword in research_keywords):
            return DEEP_RESEARCH_CONFIG
        elif any(keyword in title_lower for keyword in fast_keywords):
            return FAST_CONFIG
    
    # 检查模式
    if mode:
        return get_config_by_mode(mode)
    
    # 默认使用标准配置
    return STANDARD_CONFIG


# 导出默认配置
DEFAULT_CONFIG = STANDARD_CONFIG
