"""
Custom API Exceptions

Defines custom exception classes for the ChatGPT Controller API.
"""

from datetime import datetime
from typing import Optional, Dict, Any


class APIException(Exception):
    """Base API exception"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        self.timestamp = datetime.now()
        super().__init__(self.message)


class ValidationException(APIException):
    """Validation error exception"""
    
    def __init__(self, message: str = "Validation failed", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 422, details)


class NotFoundError(APIException):
    """Resource not found exception"""
    
    def __init__(self, resource: str, identifier: str = None):
        message = f"{resource} not found"
        if identifier:
            message += f": {identifier}"
        super().__init__(message, 404)


class ConflictError(APIException):
    """Resource conflict exception"""
    
    def __init__(self, message: str = "Resource conflict", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 409, details)


class ConnectionError(APIException):
    """WebSocket connection error"""
    
    def __init__(self, message: str = "WebSocket connection error"):
        super().__init__(message, 503)


class TimeoutError(APIException):
    """Operation timeout error"""
    
    def __init__(self, operation: str, timeout: float):
        message = f"Operation '{operation}' timed out after {timeout}s"
        super().__init__(message, 408)


class RateLimitError(APIException):
    """Rate limit exceeded error"""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, 429)


class DatabaseError(APIException):
    """Database operation error"""
    
    def __init__(self, message: str = "Database error", details: Optional[Dict[str, Any]] = None):
        super().__init__(message, 500, details)


class BrowserControlError(APIException):
    """Browser control operation error"""

    def __init__(self, operation: str, message: str = None):
        self.operation = operation
        self.error_message = message
        error_message = f"Browser control operation '{operation}' failed"
        if message:
            error_message += f": {message}"
        super().__init__(error_message, 500)


class ConversationError(APIException):
    """Conversation-related error"""
    
    def __init__(self, conversation_id: str, message: str):
        error_message = f"Conversation {conversation_id}: {message}"
        super().__init__(error_message, 400)
