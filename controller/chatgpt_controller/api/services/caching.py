"""
Conversation Cache Service

Manages intelligent caching of conversation content with content analysis
and cache invalidation strategies.
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from ..database import get_db_session
from ..models import Conversation, ConversationCache, Message
from ...utils.logging import RichLogger

# Create logger
logger = RichLogger(__name__)


class ConversationCacheService:
    """
    Service for managing conversation content caching
    """
    
    def __init__(self):
        # Cache settings
        self.default_ttl_hours = 24
        self.max_cache_size_mb = 100  # Maximum cache size per conversation
        self.content_change_threshold = 0.1  # 10% content change threshold
        
    async def should_update_cache(
        self,
        conversation_id: str,
        new_content: str,
        force_update: bool = False
    ) -> bool:
        """
        Determine if cache should be updated based on content analysis
        
        Args:
            conversation_id: Conversation ID
            new_content: New content to compare
            force_update: Force cache update
            
        Returns:
            True if cache should be updated
        """
        if force_update:
            return True
        
        try:
            async with get_db_session() as db:
                # Get latest cache entry
                latest_cache = await db.execute(
                    select(ConversationCache)
                    .where(ConversationCache.conversation_id == conversation_id)
                    .order_by(ConversationCache.created_at.desc())
                    .limit(1)
                )
                
                cache_entry = latest_cache.scalar_one_or_none()
                
                if not cache_entry:
                    # No cache exists, should update
                    return True
                
                # Check if cache is expired
                if cache_entry.expires_at and cache_entry.expires_at < datetime.now():
                    return True
                
                # Compare content
                old_content = cache_entry.raw_content
                
                # Calculate content similarity
                similarity = self._calculate_content_similarity(old_content, new_content)
                
                # Update if content has changed significantly
                change_ratio = 1.0 - similarity
                should_update = change_ratio > self.content_change_threshold
                
                if should_update:
                    logger.info(f"📊 Content change detected for {conversation_id}: {change_ratio:.2%}")
                
                return should_update
                
        except Exception as e:
            logger.error(f"❌ Error checking cache update criteria: {e}")
            return True  # Default to updating on error
    
    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """
        Calculate similarity between two content strings
        
        Args:
            content1: First content string
            content2: Second content string
            
        Returns:
            Similarity ratio (0.0 to 1.0)
        """
        try:
            # Simple approach: compare content lengths and hashes
            if not content1 or not content2:
                return 0.0
            
            # If content is identical
            if content1 == content2:
                return 1.0
            
            # Compare lengths
            len1, len2 = len(content1), len(content2)
            length_ratio = min(len1, len2) / max(len1, len2)
            
            # Compare hashes of normalized content
            hash1 = hashlib.md5(content1.strip().encode()).hexdigest()
            hash2 = hashlib.md5(content2.strip().encode()).hexdigest()
            
            if hash1 == hash2:
                return 1.0
            
            # For JSON content, try structural comparison
            try:
                json1 = json.loads(content1)
                json2 = json.loads(content2)
                
                # Compare message counts if it's conversation data
                if isinstance(json1, dict) and isinstance(json2, dict):
                    messages1 = self._extract_messages_from_json(json1)
                    messages2 = self._extract_messages_from_json(json2)
                    
                    if messages1 is not None and messages2 is not None:
                        if len(messages1) == len(messages2):
                            return 0.9  # Same message count, likely similar
                        else:
                            # Different message counts
                            return min(len(messages1), len(messages2)) / max(len(messages1), len(messages2))
                
            except json.JSONDecodeError:
                pass
            
            # Fallback to length-based similarity
            return length_ratio * 0.8  # Reduce score for non-identical content
            
        except Exception as e:
            logger.error(f"❌ Error calculating content similarity: {e}")
            return 0.5  # Default similarity
    
    def _extract_messages_from_json(self, data: Dict[str, Any]) -> Optional[List]:
        """Extract messages from JSON conversation data"""
        try:
            # Common patterns for ChatGPT conversation data
            if 'mapping' in data:
                return list(data['mapping'].values())
            elif 'messages' in data:
                return data['messages']
            elif isinstance(data.get('conversation'), dict):
                conv_data = data['conversation']
                if 'mapping' in conv_data:
                    return list(conv_data['mapping'].values())
                elif 'messages' in conv_data:
                    return conv_data['messages']
            
            return None
            
        except Exception:
            return None
    
    async def get_cached_content(
        self,
        conversation_id: str,
        max_age_hours: Optional[float] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Get cached content for a conversation
        
        Args:
            conversation_id: Conversation ID
            max_age_hours: Maximum age of cache in hours
            
        Returns:
            Cached content or None if not available
        """
        try:
            async with get_db_session() as db:
                query = select(ConversationCache).where(
                    ConversationCache.conversation_id == conversation_id,
                    ConversationCache.is_valid == True
                )
                
                # Add age filter if specified
                if max_age_hours:
                    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
                    query = query.where(ConversationCache.created_at >= cutoff_time)
                
                # Get most recent valid cache
                query = query.order_by(ConversationCache.created_at.desc()).limit(1)
                
                result = await db.execute(query)
                cache_entry = result.scalar_one_or_none()
                
                if not cache_entry:
                    return None
                
                # Update last accessed time
                cache_entry.last_accessed = datetime.now()
                await db.commit()
                
                # Return parsed content
                content = cache_entry.get_parsed_content()
                
                return {
                    "content": content,
                    "cache_info": {
                        "cache_id": cache_entry.id,
                        "created_at": cache_entry.created_at,
                        "content_size": cache_entry.content_size,
                        "content_hash": cache_entry.content_hash,
                        "cache_source": cache_entry.cache_source
                    }
                }
                
        except Exception as e:
            logger.error(f"❌ Error getting cached content: {e}")
            return None
    
    async def invalidate_cache(
        self,
        conversation_id: str,
        reason: str = "manual"
    ) -> bool:
        """
        Invalidate cache for a conversation
        
        Args:
            conversation_id: Conversation ID
            reason: Reason for invalidation
            
        Returns:
            True if cache was invalidated
        """
        try:
            async with get_db_session() as db:
                # Mark all cache entries as invalid
                cache_entries = await db.execute(
                    select(ConversationCache).where(
                        ConversationCache.conversation_id == conversation_id,
                        ConversationCache.is_valid == True
                    )
                )
                
                entries = cache_entries.scalars().all()
                invalidated_count = 0
                
                for entry in entries:
                    entry.is_valid = False
                    invalidated_count += 1
                
                # Update conversation cache status
                conversation_result = await db.execute(
                    select(Conversation).where(Conversation.id == conversation_id)
                )
                conversation = conversation_result.scalar_one_or_none()
                
                if conversation:
                    conversation.is_cached = False
                    conversation.updated_at = datetime.now()
                
                await db.commit()
                
                if invalidated_count > 0:
                    logger.info(f"🗑️ Invalidated {invalidated_count} cache entries for {conversation_id} (reason: {reason})")
                
                return invalidated_count > 0
                
        except Exception as e:
            logger.error(f"❌ Error invalidating cache: {e}")
            return False
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        
        Returns:
            Dict with cache statistics
        """
        try:
            async with get_db_session() as db:
                # Total cache entries
                total_entries = await db.execute(
                    select(ConversationCache.id)
                )
                total_count = len(total_entries.scalars().all())
                
                # Valid cache entries
                valid_entries = await db.execute(
                    select(ConversationCache.id).where(ConversationCache.is_valid == True)
                )
                valid_count = len(valid_entries.scalars().all())
                
                # Total cache size
                size_result = await db.execute(
                    select(ConversationCache.content_size).where(ConversationCache.is_valid == True)
                )
                sizes = size_result.scalars().all()
                total_size_bytes = sum(sizes)
                
                # Cache by source
                source_result = await db.execute(
                    select(ConversationCache.cache_source, ConversationCache.id)
                    .where(ConversationCache.is_valid == True)
                )
                
                source_counts = {}
                for source, _ in source_result.all():
                    source_counts[source] = source_counts.get(source, 0) + 1
                
                return {
                    "total_entries": total_count,
                    "valid_entries": valid_count,
                    "invalid_entries": total_count - valid_count,
                    "total_size_bytes": total_size_bytes,
                    "total_size_mb": total_size_bytes / (1024 * 1024),
                    "by_source": source_counts,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Error getting cache stats: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
