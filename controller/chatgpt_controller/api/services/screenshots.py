"""
Screenshot Service

Handles screenshot capture for debugging and operation traceability.
"""

import os
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from ...utils.logging import RichLogger
from ...core.config import get_config

# Create logger
logger = RichLogger(__name__)


class ScreenshotService:
    """
    Service for capturing and managing screenshots
    """
    
    def __init__(self, websocket_server=None):
        self.websocket_server = websocket_server
        self.config = get_config()
        
        # Screenshot settings
        self.screenshots_dir = Path("screenshots")
        self.max_screenshots = 1000  # Maximum number of screenshots to keep
        self.default_format = "png"
        self.default_quality = 90
        
        # Ensure screenshots directory exists
        self.screenshots_dir.mkdir(exist_ok=True)
    
    async def capture_screenshot(
        self,
        operation: str = "debug",
        conversation_id: Optional[str] = None,
        format: str = "png",
        quality: int = 90,
        full_page: bool = False
    ) -> Dict[str, Any]:
        """
        Capture a screenshot for debugging/traceability
        
        Args:
            operation: Operation name for filename
            conversation_id: Optional conversation ID
            format: Image format (png, jpeg)
            quality: Image quality (1-100)
            full_page: Whether to capture full page
            
        Returns:
            Dict with screenshot info
        """
        try:
            if not self.websocket_server:
                raise RuntimeError("WebSocket server not available")
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
            filename_parts = [timestamp, operation]
            
            if conversation_id:
                filename_parts.append(conversation_id[:8])  # First 8 chars of conversation ID
            
            filename = f"{'_'.join(filename_parts)}.{format}"
            file_path = self.screenshots_dir / filename
            
            logger.info(f"📸 Capturing screenshot: {filename}")
            
            # Capture screenshot using WebSocket server
            screenshot_response = await self.websocket_server.take_screenshot(
                format=format,
                quality=quality,
                full_page=full_page,
                timeout=10.0
            )
            
            if not screenshot_response.success:
                raise RuntimeError(f"Screenshot capture failed: {screenshot_response.error}")
            
            # Get screenshot data
            screenshot_data = screenshot_response.data
            
            if 'dataUrl' in screenshot_data:
                # Extract base64 data from data URL
                data_url = screenshot_data['dataUrl']
                if data_url.startswith('data:image/'):
                    # Remove data URL prefix
                    header, base64_data = data_url.split(',', 1)
                    
                    # Decode and save
                    import base64
                    image_data = base64.b64decode(base64_data)
                    
                    with open(file_path, 'wb') as f:
                        f.write(image_data)
                    
                    file_size = len(image_data)
                    
                else:
                    raise RuntimeError("Invalid data URL format")
            else:
                raise RuntimeError("No screenshot data received")
            
            # Cleanup old screenshots
            await self._cleanup_old_screenshots()
            
            result = {
                "file_path": str(file_path),
                "filename": filename,
                "file_size": file_size,
                "format": format,
                "quality": quality,
                "full_page": full_page,
                "operation": operation,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat(),
                "success": True
            }
            
            logger.success(f"✅ Screenshot saved: {filename} ({file_size} bytes)")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error capturing screenshot: {e}")
            return {
                "error": str(e),
                "operation": operation,
                "conversation_id": conversation_id,
                "timestamp": datetime.now().isoformat(),
                "success": False
            }
    
    async def capture_operation_screenshot(
        self,
        operation: str,
        conversation_id: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Capture screenshot for a specific operation
        
        Args:
            operation: Operation name
            conversation_id: Optional conversation ID
            **kwargs: Additional screenshot parameters
            
        Returns:
            Dict with screenshot info
        """
        return await self.capture_screenshot(
            operation=operation,
            conversation_id=conversation_id,
            **kwargs
        )
    
    async def capture_error_screenshot(
        self,
        error_context: str,
        conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Capture screenshot when an error occurs
        
        Args:
            error_context: Error context for filename
            conversation_id: Optional conversation ID
            
        Returns:
            Dict with screenshot info
        """
        return await self.capture_screenshot(
            operation=f"error_{error_context}",
            conversation_id=conversation_id,
            format="png",
            quality=100  # High quality for error analysis
        )
    
    async def capture_conversation_screenshot(
        self,
        conversation_id: str,
        stage: str = "current"
    ) -> Dict[str, Any]:
        """
        Capture screenshot of a conversation
        
        Args:
            conversation_id: Conversation ID
            stage: Stage of conversation (start, current, end, etc.)
            
        Returns:
            Dict with screenshot info
        """
        return await self.capture_screenshot(
            operation=f"conversation_{stage}",
            conversation_id=conversation_id,
            format="png",
            quality=85,
            full_page=True  # Capture full conversation
        )
    
    async def _cleanup_old_screenshots(self):
        """Clean up old screenshots to maintain storage limits"""
        try:
            # Get all screenshot files
            screenshot_files = list(self.screenshots_dir.glob("*.png")) + \
                             list(self.screenshots_dir.glob("*.jpg")) + \
                             list(self.screenshots_dir.glob("*.jpeg"))
            
            # Sort by modification time (newest first)
            screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Remove excess files
            if len(screenshot_files) > self.max_screenshots:
                files_to_remove = screenshot_files[self.max_screenshots:]
                
                for file_path in files_to_remove:
                    try:
                        file_path.unlink()
                        logger.debug(f"🗑️ Removed old screenshot: {file_path.name}")
                    except Exception as e:
                        logger.error(f"❌ Error removing screenshot {file_path}: {e}")
                
                logger.info(f"🧹 Cleaned up {len(files_to_remove)} old screenshots")
                
        except Exception as e:
            logger.error(f"❌ Error during screenshot cleanup: {e}")
    
    def get_screenshot_info(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a screenshot file
        
        Args:
            filename: Screenshot filename
            
        Returns:
            Dict with file info or None if not found
        """
        try:
            file_path = self.screenshots_dir / filename
            
            if not file_path.exists():
                return None
            
            stat = file_path.stat()
            
            return {
                "filename": filename,
                "file_path": str(file_path),
                "file_size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "exists": True
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting screenshot info: {e}")
            return None
    
    def list_screenshots(self, limit: int = 50) -> list:
        """
        List recent screenshots
        
        Args:
            limit: Maximum number of screenshots to return
            
        Returns:
            List of screenshot info dicts
        """
        try:
            # Get all screenshot files
            screenshot_files = list(self.screenshots_dir.glob("*.png")) + \
                             list(self.screenshots_dir.glob("*.jpg")) + \
                             list(self.screenshots_dir.glob("*.jpeg"))
            
            # Sort by modification time (newest first)
            screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Limit results
            screenshot_files = screenshot_files[:limit]
            
            # Get info for each file
            screenshots = []
            for file_path in screenshot_files:
                info = self.get_screenshot_info(file_path.name)
                if info:
                    screenshots.append(info)
            
            return screenshots
            
        except Exception as e:
            logger.error(f"❌ Error listing screenshots: {e}")
            return []
    
    async def delete_screenshot(self, filename: str) -> bool:
        """
        Delete a screenshot file
        
        Args:
            filename: Screenshot filename
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            file_path = self.screenshots_dir / filename
            
            if file_path.exists():
                file_path.unlink()
                logger.info(f"🗑️ Deleted screenshot: {filename}")
                return True
            else:
                logger.warning(f"⚠️ Screenshot not found: {filename}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error deleting screenshot {filename}: {e}")
            return False
