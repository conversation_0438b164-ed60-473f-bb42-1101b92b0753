"""
WebSocket Event Monitoring Service

Monitors WebSocket events and automatically caches conversation content
when detecting requests to ChatGPT backend API endpoints.
"""

import asyncio
import json
import hashlib
import re
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Set
from urllib.parse import urlparse

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..database import get_db_session
from ..models import Conversation, ConversationCache, Message
from ...core.message_types import MessageType
from ...core.events import EventType, subscribe_to_event
from ...utils.logging import RichLogger

# Create logger
logger = RichLogger(__name__)


class WebSocketMonitoringService:
    """
    Service for monitoring WebSocket events and caching conversation data
    """
    
    def __init__(self, websocket_server=None):
        self.websocket_server = websocket_server
        self.running = False
        self.monitored_conversations: Set[str] = set()
        self.cache_service = None
        
        # Cache settings
        self.cache_ttl_hours = 24
        self.min_content_size = 100  # Minimum content size to cache
        self.max_cache_entries_per_conversation = 10
        
        # Setup event handlers
        self._setup_event_handlers()
    
    def _setup_event_handlers(self):
        """Setup WebSocket event handlers"""
        try:
            # Subscribe to monitored API events
            subscribe_to_event(EventType.MESSAGE_RECEIVED, self._handle_message_received)
            logger.info("📡 WebSocket event handlers registered")
        except Exception as e:
            logger.error(f"❌ Error setting up event handlers: {e}")
    
    async def start(self):
        """Start the monitoring service"""
        if self.running:
            logger.warning("⚠️ Monitoring service already running")
            return
        
        self.running = True
        logger.info("🚀 WebSocket monitoring service started")
        
        # Start background tasks
        asyncio.create_task(self._cleanup_old_cache_entries())
    
    async def stop(self):
        """Stop the monitoring service"""
        self.running = False
        logger.info("🛑 WebSocket monitoring service stopped")
    
    def _extract_conversation_id_from_url(self, url: str) -> Optional[str]:
        """Extract conversation ID from API URL"""
        # Pattern for ChatGPT conversation API URLs
        patterns = [
            r'/backend-api/conversation/([a-f0-9-]+)',
            r'/c/([a-f0-9-]+)',
            r'conversation[/_]([a-f0-9-]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _should_cache_content(self, content: str, conversation_id: str) -> bool:
        """Determine if content should be cached based on intelligent logic"""
        try:
            # Check minimum content size
            if len(content) < self.min_content_size:
                return False
            
            # Try to parse as JSON to check if it's valid conversation data
            try:
                data = json.loads(content)
                
                # Check if it contains conversation messages
                if isinstance(data, dict):
                    # Look for message indicators
                    has_messages = any(key in data for key in ['messages', 'conversation', 'mapping'])
                    if has_messages:
                        return True
                    
                    # Check for conversation metadata
                    has_metadata = any(key in data for key in ['title', 'create_time', 'update_time'])
                    if has_metadata:
                        return True
                
            except json.JSONDecodeError:
                # Not JSON, check if it's substantial text content
                if len(content.strip()) > 500:  # Substantial text content
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Error checking cache criteria: {e}")
            return False
    
    def _calculate_content_hash(self, content: str) -> str:
        """Calculate SHA256 hash of content"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    async def _handle_message_received(self, event_data: Dict[str, Any]):
        """Handle incoming WebSocket messages"""
        try:
            message_type = event_data.get('message_type')
            data = event_data.get('data', {})
            
            # Handle monitored API responses
            if message_type == 'MONITORED_API_RESPONSE_DATA':
                await self._handle_api_response_data(data)
            elif message_type == 'MONITORED_API_COMPLETED':
                await self._handle_api_completed(data)
                
        except Exception as e:
            logger.error(f"❌ Error handling message: {e}")
    
    async def _handle_api_response_data(self, data: Dict[str, Any]):
        """Handle API response data for caching"""
        try:
            url = data.get('url', '')
            content = data.get('responseData', '')
            
            # Extract conversation ID from URL
            conversation_id = self._extract_conversation_id_from_url(url)
            if not conversation_id:
                return
            
            # Check if we should cache this content
            if not self._should_cache_content(content, conversation_id):
                return
            
            logger.info(f"💾 Caching content for conversation {conversation_id}")
            
            # Cache the content
            await self._cache_conversation_content(
                conversation_id=conversation_id,
                content=content,
                url=url,
                source="websocket_api"
            )
            
        except Exception as e:
            logger.error(f"❌ Error handling API response data: {e}")
    
    async def _handle_api_completed(self, data: Dict[str, Any]):
        """Handle completed API requests"""
        try:
            url = data.get('url', '')
            conversation_id = self._extract_conversation_id_from_url(url)
            
            if conversation_id:
                # Add to monitored conversations
                self.monitored_conversations.add(conversation_id)
                logger.debug(f"📊 Monitoring conversation {conversation_id}")
                
        except Exception as e:
            logger.error(f"❌ Error handling API completed: {e}")
    
    async def _cache_conversation_content(
        self,
        conversation_id: str,
        content: str,
        url: str,
        source: str = "api"
    ):
        """Cache conversation content in database"""
        try:
            async with get_db_session() as db:
                # Calculate content hash
                content_hash = self._calculate_content_hash(content)
                
                # Check if we already have this exact content
                existing_cache = await db.execute(
                    select(ConversationCache).where(
                        ConversationCache.conversation_id == conversation_id,
                        ConversationCache.content_hash == content_hash
                    )
                )
                
                if existing_cache.scalar_one_or_none():
                    logger.debug(f"🔄 Content already cached for {conversation_id}")
                    return
                
                # Create cache key
                cache_key = f"{conversation_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # Create cache entry
                cache_entry = ConversationCache(
                    conversation_id=conversation_id,
                    cache_key=cache_key,
                    content_hash=content_hash,
                    content_size=len(content),
                    raw_content=content,
                    cache_source=source,
                    request_url=url,
                    expires_at=datetime.now() + timedelta(hours=self.cache_ttl_hours),
                    created_at=datetime.now()
                )
                
                # Try to parse content as JSON for structured storage
                try:
                    parsed_content = json.loads(content)
                    cache_entry.parsed_content = parsed_content
                except json.JSONDecodeError:
                    pass
                
                db.add(cache_entry)
                
                # Update conversation cache status
                conversation_result = await db.execute(
                    select(Conversation).where(Conversation.id == conversation_id)
                )
                conversation = conversation_result.scalar_one_or_none()
                
                if conversation:
                    conversation.is_cached = True
                    conversation.updated_at = datetime.now()
                
                await db.commit()
                
                logger.success(f"✅ Cached content for conversation {conversation_id} (size: {len(content)} bytes)")
                
                # Cleanup old cache entries for this conversation
                await self._cleanup_conversation_cache(db, conversation_id)
                
        except Exception as e:
            logger.error(f"❌ Error caching conversation content: {e}")
    
    async def _cleanup_conversation_cache(self, db: AsyncSession, conversation_id: str):
        """Clean up old cache entries for a conversation"""
        try:
            # Get all cache entries for this conversation, ordered by creation time
            cache_entries = await db.execute(
                select(ConversationCache)
                .where(ConversationCache.conversation_id == conversation_id)
                .order_by(ConversationCache.created_at.desc())
            )
            
            entries = cache_entries.scalars().all()
            
            # Keep only the most recent entries
            if len(entries) > self.max_cache_entries_per_conversation:
                entries_to_delete = entries[self.max_cache_entries_per_conversation:]
                
                for entry in entries_to_delete:
                    await db.delete(entry)
                
                logger.debug(f"🧹 Cleaned up {len(entries_to_delete)} old cache entries for {conversation_id}")
                
        except Exception as e:
            logger.error(f"❌ Error cleaning up conversation cache: {e}")
    
    async def _cleanup_old_cache_entries(self):
        """Periodic cleanup of expired cache entries"""
        while self.running:
            try:
                await asyncio.sleep(3600)  # Run every hour
                
                async with get_db_session() as db:
                    # Delete expired cache entries
                    expired_entries = await db.execute(
                        select(ConversationCache).where(
                            ConversationCache.expires_at < datetime.now()
                        )
                    )
                    
                    entries_to_delete = expired_entries.scalars().all()
                    
                    for entry in entries_to_delete:
                        await db.delete(entry)
                    
                    if entries_to_delete:
                        await db.commit()
                        logger.info(f"🧹 Cleaned up {len(entries_to_delete)} expired cache entries")
                
            except Exception as e:
                logger.error(f"❌ Error during cache cleanup: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
