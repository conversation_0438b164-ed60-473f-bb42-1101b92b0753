"""
Pydantic Models for API Request/Response Validation

Defines all request and response models for the ChatGPT Controller API.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from enum import Enum
from .models import ConversationStatus

from pydantic import BaseModel, Field, validator, ConfigDict


class ChatGPTMode(str, Enum):
    """ChatGPT conversation modes"""
    RESEARCH = "research"
    REASON = "reason"
    SEARCH = "search"
    CANVAS = "canvas"
    PICTURE_V2 = "picture_v2"


class MessageRole(str, Enum):
    """Message role values"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


# Base response models
class BaseResponse(BaseModel):
    """Base response model"""
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class ErrorResponse(BaseResponse):
    """Error response model"""
    success: bool = False
    error_type: str
    error_details: Optional[Dict[str, Any]] = None


# System status models
class ConnectionStatus(BaseModel):
    """WebSocket connection status"""
    connected: bool
    client_count: int
    last_ping: Optional[datetime] = None
    uptime_seconds: float


class SystemHealth(BaseModel):
    """System health metrics"""
    database_healthy: bool
    websocket_healthy: bool
    memory_usage_mb: float
    cpu_usage_percent: float


class StatusResponse(BaseResponse):
    """System status response"""
    connection_status: ConnectionStatus
    system_health: SystemHealth


# Conversation models
class ConversationStartRequest(BaseModel):
    """Request to start a new conversation"""
    mode: Optional[ChatGPTMode] = None
    init_prompt: str = Field(..., min_length=1, max_length=10000)
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "mode": "research",
                "init_prompt": "Help me understand quantum computing"
            }
        }
    )


class ConversationStartResponse(BaseResponse):
    """Response for conversation start"""
    conversation_id: str
    url: str
    redirect_url: Optional[str] = None


class MessageRequest(BaseModel):
    """Request to send a message to conversation"""
    message: str = Field(..., min_length=1, max_length=10000)
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "message": "Can you explain this in more detail?"
            }
        }
    )


class MessageResponse(BaseResponse):
    """Response for message sending"""
    message_id: Optional[str] = None
    sent_at: datetime


class MessageData(BaseModel):
    """Message data model"""
    id: int
    conversation_id: str
    role: str
    content: str
    content_type: str = "text"
    message_id: Optional[str] = None
    parent_id: Optional[str] = None
    model: Optional[str] = None
    source: str = "user"  # user, chatgpt_api, system
    created_at: datetime
    sent_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class ConversationData(BaseModel):
    """Conversation data model"""
    id: str
    title: Optional[str] = None
    mode: Optional[str] = None
    init_prompt: Optional[str] = None
    status: ConversationStatus
    is_cached: bool
    created_at: datetime
    updated_at: datetime
    last_accessed: Optional[datetime] = None
    url: Optional[str] = None
    redirect_url: Optional[str] = None
    message_count: int = 0


class ConversationResponse(BaseResponse):
    """Response for conversation query"""
    conversation: ConversationData
    messages: List[MessageData] = []


class ConversationListResponse(BaseResponse):
    """Response for conversation list"""
    conversations: List[ConversationData]
    total: int
    page: int = 1
    page_size: int = 50


# Cache models
class CacheData(BaseModel):
    """Cache entry data"""
    id: int
    conversation_id: str
    cache_key: str
    content_hash: str
    content_size: int
    is_valid: bool
    cache_source: str
    created_at: datetime
    expires_at: Optional[datetime] = None
    last_accessed: Optional[datetime] = None


# WebSocket monitoring models
class WebSocketEventData(BaseModel):
    """WebSocket event data"""
    event_type: str
    conversation_id: Optional[str] = None
    url: Optional[str] = None
    content: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)


# Screenshot and debugging models
class ScreenshotRequest(BaseModel):
    """Request for screenshot capture"""
    conversation_id: Optional[str] = None
    operation: str = "debug"
    format: str = "png"
    quality: int = Field(default=90, ge=1, le=100)


class ScreenshotResponse(BaseResponse):
    """Response for screenshot capture"""
    file_path: str
    file_size: int
    timestamp: datetime


# Pagination models
class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=50, ge=1, le=100)
    
    @validator('page_size')
    def validate_page_size(cls, v):
        if v > 100:
            raise ValueError('page_size cannot exceed 100')
        return v


# Query parameters
class ConversationQueryParams(PaginationParams):
    """Query parameters for conversation listing"""
    status: Optional[ConversationStatus] = None
    mode: Optional[ChatGPTMode] = None
    search: Optional[str] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None


# Health check models
class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0.0"
    uptime_seconds: float
    checks: Dict[str, bool]


# ChatGPT Conversation API Models
class ChatGPTAuthor(BaseModel):
    """ChatGPT message author"""
    role: str
    name: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ChatGPTMessageContent(BaseModel):
    """ChatGPT message content"""
    content_type: str
    parts: List[str] = Field(default_factory=list)
    model_set_context: Optional[str] = None
    repository: Optional[str] = None
    repo_summary: Optional[str] = None
    structured_context: Optional[str] = None


class ChatGPTMessageMetadata(BaseModel):
    """ChatGPT message metadata"""
    is_visually_hidden_from_conversation: Optional[bool] = None
    selected_github_repos: List[str] = Field(default_factory=list)
    serialization_metadata: Dict[str, Any] = Field(default_factory=dict)
    request_id: Optional[str] = None
    message_source: Optional[str] = None
    timestamp_: Optional[str] = None
    message_type: Optional[str] = None
    model_slug: Optional[str] = None
    default_model_slug: Optional[str] = None
    parent_id: Optional[str] = None
    model_switcher_deny: Union[List[str], List[Dict[str, Any]]] = Field(default_factory=list)
    citations: List[Dict[str, Any]] = Field(default_factory=list)
    content_references: List[Dict[str, Any]] = Field(default_factory=list)


class ChatGPTMessage(BaseModel):
    """ChatGPT message structure"""
    id: str
    author: ChatGPTAuthor
    create_time: Optional[float] = None
    update_time: Optional[float] = None
    content: ChatGPTMessageContent
    status: str
    end_turn: Optional[bool] = None
    weight: float = 0.0
    metadata: ChatGPTMessageMetadata = Field(default_factory=ChatGPTMessageMetadata)
    recipient: str = "all"
    channel: Optional[str] = None


class ChatGPTMappingNode(BaseModel):
    """ChatGPT conversation mapping node"""
    id: str
    message: Optional[ChatGPTMessage] = None
    parent: Optional[str] = None
    children: List[str] = Field(default_factory=list)


class ChatGPTConversationData(BaseModel):
    """ChatGPT conversation API response data"""
    title: Optional[str]
    create_time: float
    update_time: float
    mapping: Dict[str, ChatGPTMappingNode]
    moderation_results: List[Dict[str, Any]] = Field(default_factory=list)
    current_node: str
    plugin_ids: Optional[List[str]] = None
    conversation_id: str
    conversation_template_id: Optional[str] = None
    gizmo_id: Optional[str] = None
    gizmo_type: Optional[str] = None
    is_archived: bool = False
    is_starred: Optional[bool] = None
    safe_urls: List[str] = Field(default_factory=list)
    blocked_urls: List[str] = Field(default_factory=list)
    default_model_slug: str = "auto"
    conversation_origin: Optional[str] = None
    voice: Optional[str] = None
    async_status: Optional[str] = None
    disabled_tool_ids: List[str] = Field(default_factory=list)
    is_do_not_remember: bool = False
    memory_scope: str = "global_enabled"
    sugar_item_id: Optional[str] = None


class ConversationUpdateEvent(BaseModel):
    """Event data for conversation updates"""
    conversation_id: str
    url: str
    conversation_data: ChatGPTConversationData
    timestamp: datetime = Field(default_factory=datetime.now)
    source: str = "api_monitor"


# OpenAI-compatible models (for future implementation)
class OpenAIMessage(BaseModel):
    """OpenAI-compatible message format"""
    role: str
    content: str


class OpenAIChatCompletionRequest(BaseModel):
    """OpenAI-compatible chat completion request"""
    model: str = "gpt-4"
    messages: List[OpenAIMessage]
    temperature: Optional[float] = Field(default=1.0, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(default=None, ge=1)
    stream: bool = False


class OpenAIChatCompletionResponse(BaseModel):
    """OpenAI-compatible chat completion response"""
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]
