"""
Message types and enums for ChatGPT Controller

Defines all message types, directions, and related enums used throughout the application.
Aligned with browser extension's TypeScript interface definitions.
"""

from enum import Enum
from typing import Dict, Any
from dataclasses import dataclass
from datetime import datetime
import random
import time


class MessageType(Enum):
    """Enumeration of WebSocket message types - aligned with browser extension"""

    # Connection management
    CONNECT = "CONNECT"
    DISCONNECT = "DISCONNECT"
    PING = "PING"
    PONG = "PONG"
    HEARTBEAT = "HEARTBEAT"  # Heartbeat message from client
    HEARTBEAT_RESPONSE = "HEARTBEAT_RESPONSE"  # Heartbeat response to client
    ERROR = "ERROR"
    INIT = "INIT"  # Plugin initialization message

    # Browser control commands
    OPEN_CHATGPT = "OPEN_CHATGPT"
    SET_WINDOW_SIZE = "SET_WINDOW_SIZE"
    TAKE_SCREENSHOT = "TAKE_SCREENSHOT"

    # Element query commands
    ELEMENT_QUERY = "ELEMENT_QUERY"
    PAGE_INFO = "PAGE_INFO"
    ELEMENT_WATCH = "ELEMENT_WATCH"
    BATCH_ELEMENT_QUERY = "BATCH_ELEMENT_QUERY"
    ELEMENT_SCREENSHOT = "ELEMENT_SCREENSHOT"

    # Response messages
    ELEMENT_QUERY_RESPONSE = "ELEMENT_QUERY_RESPONSE"
    ELEMENT_QUERY_RESULT = "ELEMENT_QUERY_RESULT"  # Alternative name for element query response
    ELEMENT_EVENT_MESSAGE = "ELEMENT_EVENT_MESSAGE"
    BROWSER_CONTROL_RESPONSE = "BROWSER_CONTROL_RESPONSE"

    # ChatGPT and user interaction messages
    CHATGPT_MESSAGE = "CHATGPT_MESSAGE"
    USER_INFO = "USER_INFO"
    CHAT_REQUIREMENTS = "CHAT_REQUIREMENTS"
    OPERATION_RESULT = "OPERATION_RESULT"

    # Monitored API messages
    MONITORED_API_REQUEST = "MONITORED_API_REQUEST"
    MONITORED_API_HEADERS = "MONITORED_API_HEADERS"
    MONITORED_API_RESPONSE_HEADERS = "MONITORED_API_RESPONSE_HEADERS"
    MONITORED_API_COMPLETED = "MONITORED_API_COMPLETED"
    MONITORED_API_RESPONSE_DATA = "MONITORED_API_RESPONSE_DATA"

    # Unknown message type
    UNKNOWN = "UNKNOWN"


class ConnectionStatus(Enum):
    """WebSocket connection status - aligned with browser extension"""

    DISCONNECTED = "DISCONNECTED"
    CONNECTING = "CONNECTING"
    CONNECTED = "CONNECTED"
    ERROR = "ERROR"


class SelectorType(Enum):
    """Element selector types - aligned with browser extension"""

    XPATH = "xpath"
    CSS = "css"
    ID = "id"
    CLASS = "class"
    TAG = "tag"
    TEXT = "text"
    ATTRIBUTE = "attribute"


class ElementActionType(Enum):
    """Element action types - aligned with browser extension"""

    CLICK = "click"
    INPUT = "input"
    SCROLL = "scroll"
    HOVER = "hover"
    FOCUS = "focus"
    BLUR = "blur"
    GET_ATTRIBUTE = "getAttribute"
    SET_TEXT = "setText"


@dataclass
class BaseMessage:
    """Base message interface - aligned with browser extension"""

    type: str
    id: str
    timestamp: str
    data: Dict[str, Any] = None
    success: bool = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        result = {
            "type": self.type,
            "id": self.id,
            "timestamp": self.timestamp,
        }
        if self.data is not None:
            result["data"] = self.data
        return result

@dataclass
class ConnectionInfo:
    """WebSocket connection information - aligned with browser extension"""

    client_id: str
    remote_address: str
    connected_at: datetime
    status: ConnectionStatus = ConnectionStatus.DISCONNECTED
    remote_port: int = 0
    user_agent: str = ""
    client_type: str = ""
    last_activity: datetime = None

    def __post_init__(self):
        """Set default last_activity if not provided"""
        if self.last_activity is None:
            self.last_activity = self.connected_at

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "clientId": self.client_id,
            "remoteAddress": self.remote_address,
            "remotePort": self.remote_port,
            "connectedAt": self.connected_at.isoformat(),
            "lastActivity": self.last_activity.isoformat(),
            "status": self.status.value,
            "userAgent": self.user_agent,
            "clientType": self.client_type,
        }


@dataclass
class ConnectMessage(BaseMessage):
    """Connect message - aligned with browser extension"""

    def __init__(self, url: str = None):
        super().__init__(
            type=MessageType.CONNECT.value,
            id=generate_message_id(),
            timestamp=datetime.now().isoformat(),
            data={"url": url} if url else None
        )


@dataclass
class DisconnectMessage(BaseMessage):
    """Disconnect message - aligned with browser extension"""

    def __init__(self, data: Dict[str, Any] = None):
        super().__init__(
            type=MessageType.DISCONNECT.value,
            id=generate_message_id(),
            timestamp=datetime.now().isoformat(),
            data=data or {}
        )


@dataclass
class ErrorMessage(BaseMessage):
    """Error message - aligned with browser extension"""

    def __init__(self, message: str, code: str = None):
        super().__init__(
            type=MessageType.ERROR.value,
            id=generate_message_id(),
            timestamp=datetime.now().isoformat(),
            data={"message": message, "code": code}
        )


# Utility functions for message creation

def generate_message_id(prefix: str = "msg") -> str:
    """Generate unique message ID - aligned with browser extension"""
    timestamp = int(time.time() * 1000)
    random_part = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=9))
    return f"{prefix}-{timestamp}-{random_part}"


def create_base_message(message_type: str, data: Dict[str, Any] = None) -> BaseMessage:
    """Create base message - aligned with browser extension"""
    return BaseMessage(
        type=message_type,
        id=generate_message_id(),
        timestamp=datetime.now().isoformat(),
        data=data
    )


def create_error_message(message: str, code: str = None) -> ErrorMessage:
    """Create error message - aligned with browser extension"""
    return ErrorMessage(message=message, code=code)


def is_message_type(type_str: str) -> bool:
    """Check if string is valid message type - aligned with browser extension"""
    return any(msg_type.value == type_str for msg_type in MessageType)


# Message type categories for filtering and organization - aligned with browser extension
INCOMING_MESSAGE_TYPES = {
    MessageType.CONNECT,
    MessageType.DISCONNECT,
    MessageType.PONG,
    MessageType.HEARTBEAT,
    MessageType.INIT,
    MessageType.ELEMENT_QUERY_RESPONSE,
    MessageType.ELEMENT_QUERY_RESULT,
    MessageType.ELEMENT_EVENT_MESSAGE,
    MessageType.BROWSER_CONTROL_RESPONSE,
    MessageType.CHATGPT_MESSAGE,
    MessageType.USER_INFO,
    MessageType.CHAT_REQUIREMENTS,
    MessageType.OPERATION_RESULT,
    MessageType.MONITORED_API_REQUEST,
    MessageType.MONITORED_API_HEADERS,
    MessageType.MONITORED_API_RESPONSE_HEADERS,
    MessageType.MONITORED_API_COMPLETED,
    MessageType.MONITORED_API_RESPONSE_DATA,
}

OUTGOING_MESSAGE_TYPES = {
    MessageType.PING,
    MessageType.HEARTBEAT_RESPONSE,
    MessageType.OPEN_CHATGPT,
    MessageType.SET_WINDOW_SIZE,
    MessageType.TAKE_SCREENSHOT,
    MessageType.ELEMENT_QUERY,
    MessageType.PAGE_INFO,
    MessageType.ELEMENT_WATCH,
    MessageType.BATCH_ELEMENT_QUERY,
    MessageType.ELEMENT_SCREENSHOT,
}

SYSTEM_MESSAGE_TYPES = {
    MessageType.CONNECT,
    MessageType.DISCONNECT,
    MessageType.PING,
    MessageType.PONG,
    MessageType.HEARTBEAT,
    MessageType.HEARTBEAT_RESPONSE,
    MessageType.ERROR,
    MessageType.INIT,
}

BROWSER_CONTROL_COMMAND_TYPES = {
    MessageType.OPEN_CHATGPT,
    MessageType.SET_WINDOW_SIZE,
    MessageType.TAKE_SCREENSHOT,
}

ELEMENT_COMMAND_TYPES = {
    MessageType.ELEMENT_QUERY,
    MessageType.PAGE_INFO,
    MessageType.ELEMENT_WATCH,
    MessageType.BATCH_ELEMENT_QUERY,
    MessageType.ELEMENT_SCREENSHOT,
}

RESPONSE_MESSAGE_TYPES = {
    MessageType.ELEMENT_QUERY_RESPONSE,
    MessageType.ELEMENT_QUERY_RESULT,
    MessageType.ELEMENT_EVENT_MESSAGE,
    MessageType.BROWSER_CONTROL_RESPONSE,
}


def get_message_category(message_type: MessageType) -> str:
    """Get the category of a message type - aligned with browser extension"""
    if message_type in INCOMING_MESSAGE_TYPES:
        return "incoming"
    elif message_type in OUTGOING_MESSAGE_TYPES:
        return "outgoing"
    elif message_type in SYSTEM_MESSAGE_TYPES:
        return "system"
    else:
        return "unknown"


def is_browser_control_command(message_type: MessageType) -> bool:
    """Check if message type is a browser control command - aligned with browser extension"""
    return message_type in BROWSER_CONTROL_COMMAND_TYPES


def is_element_command(message_type: MessageType) -> bool:
    """Check if message type is an element command - aligned with browser extension"""
    return message_type in ELEMENT_COMMAND_TYPES


def is_response_message(message_type: MessageType) -> bool:
    """Check if message type is a response message - aligned with browser extension"""
    return message_type in RESPONSE_MESSAGE_TYPES


def is_system_message(message_type: MessageType) -> bool:
    """Check if message type is a system message - aligned with browser extension"""
    return message_type in SYSTEM_MESSAGE_TYPES


def get_message_type_description(message_type: MessageType) -> str:
    """Get human-readable description of message type - aligned with browser extension"""
    descriptions = {
        MessageType.CONNECT: "WebSocket connection establishment",
        MessageType.DISCONNECT: "WebSocket connection termination",
        MessageType.PING: "Connection ping",
        MessageType.PONG: "Connection pong response",
        MessageType.HEARTBEAT: "Connection heartbeat from client",
        MessageType.HEARTBEAT_RESPONSE: "Heartbeat response to client",
        MessageType.ERROR: "Error message",
        MessageType.INIT: "Plugin initialization message",
        MessageType.OPEN_CHATGPT: "Open ChatGPT in browser",
        MessageType.SET_WINDOW_SIZE: "Set browser window size",
        MessageType.TAKE_SCREENSHOT: "Take page screenshot",
        MessageType.ELEMENT_QUERY: "Query page elements",
        MessageType.PAGE_INFO: "Get page information",
        MessageType.ELEMENT_WATCH: "Watch element events",
        MessageType.BATCH_ELEMENT_QUERY: "Batch element query",
        MessageType.ELEMENT_SCREENSHOT: "Take element screenshot",
        MessageType.ELEMENT_QUERY_RESPONSE: "Element query result from browser extension",
        MessageType.ELEMENT_QUERY_RESULT: "Element query result from browser extension (alternative name)",
        MessageType.PAGE_INFO_RESULT: "Page information result from browser extension",
        MessageType.ELEMENT_EVENT_MESSAGE: "Element event notification from browser extension",
        MessageType.BROWSER_CONTROL_RESPONSE: "Browser control operation response",
        MessageType.CHATGPT_MESSAGE: "ChatGPT message forwarded from plugin",
        MessageType.USER_INFO: "User information message",
        MessageType.CHAT_REQUIREMENTS: "Chat requirements message",
        MessageType.OPERATION_RESULT: "Operation result message",
        MessageType.MONITORED_API_REQUEST: "Monitored API request",
        MessageType.MONITORED_API_HEADERS: "Monitored API headers",
        MessageType.MONITORED_API_RESPONSE_HEADERS: "Monitored API response headers",
        MessageType.MONITORED_API_COMPLETED: "Monitored API completed",
        MessageType.MONITORED_API_RESPONSE_DATA: "Monitored API response data",
        MessageType.UNKNOWN: "Unknown message type",
    }
    return descriptions.get(message_type, "Unknown message type")


MessageDirection = ConnectionStatus  # Temporary compatibility
