"""
Message handling system for ChatGPT Controller

Provides message parsing, validation, and routing functionality for WebSocket messages.
Aligned with browser extension's TypeScript interface definitions.
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List

from .message_types import (
    MessageType,
    BaseMessage,
    INCOMING_MESSAGE_TYPES,
    OUTGOING_MESSAGE_TYPES,
    is_message_type,
    create_base_message,
)

class MessageParser:
    """Parses and validates WebSocket messages - aligned with browser extension"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def parse_message(self, raw_message: str) -> BaseMessage:
        """Parse raw WebSocket message - aligned with browser extension"""
        try:
            message_data = json.loads(raw_message)
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON: {e}")
            # Return error message
            return BaseMessage(
                type=MessageType.ERROR.value,
                id="error-" + str(datetime.now().timestamp()),
                timestamp=datetime.now().isoformat(),
                data={"message": f"Invalid JSON: {e}"}
            )

        # Extract known BaseMessage fields
        base_fields = {
            'type': message_data.get('type'),
            'id': message_data.get('id'),
            'timestamp': message_data.get('timestamp'),
            'success': message_data.get('success')
        }

        # Put any extra fields into the data field
        extra_fields = {k: v for k, v in message_data.items()
                       if k not in ['type', 'id', 'timestamp', 'success', 'data']}

        # Handle data field - merge existing data with extra fields
        existing_data = message_data.get('data', {})
        if extra_fields or existing_data:
            if isinstance(existing_data, dict):
                base_fields['data'] = {**existing_data, **extra_fields}
            else:
                # If existing data is not a dict, preserve it and add extra fields
                base_fields['data'] = {'original_data': existing_data, **extra_fields}

        # Create BaseMessage from parsed data
        return BaseMessage(**base_fields)


class MessageRouter:
    """Routes messages to appropriate handlers"""

    def __init__(self):
        self.handlers: Dict[MessageType, List[Callable]] = {}
        self.global_handlers: List[Callable] = []
        self.logger = logging.getLogger(__name__)

    def add_handler(self, message_type: MessageType, handler: Callable):
        """Add message handler for specific type"""
        if message_type not in self.handlers:
            self.handlers[message_type] = []
        self.handlers[message_type].append(handler)
        self.logger.debug(f"Added handler for {message_type.value}")

    def add_global_handler(self, handler: Callable):
        """Add global handler that receives all messages"""
        self.global_handlers.append(handler)
        self.logger.debug("Added global message handler")

    def remove_handler(self, message_type: MessageType, handler: Callable):
        """Remove message handler"""
        if message_type in self.handlers:
            try:
                self.handlers[message_type].remove(handler)
                self.logger.debug(f"Removed handler for {message_type.value}")
            except ValueError:
                pass

    def remove_global_handler(self, handler: Callable):
        """Remove global handler"""
        try:
            self.global_handlers.remove(handler)
            self.logger.debug("Removed global message handler")
        except ValueError:
            pass

    def route_message(
        self, message: BaseMessage, context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Route message to appropriate handlers - aligned with browser extension"""
        success = True
        context = context or {}

        # Get message type
        try:
            message_type = MessageType(message.type)
        except ValueError:
            self.logger.warning(f"Unknown message type: {message.type}")
            return False

        # Call global handlers first
        for handler in self.global_handlers:
            try:
                handler(message, context)
            except Exception as e:
                import traceback
                self.logger.error(f"Global handler error: {e}")
                self.logger.error(f"Traceback: {traceback.format_exc()}")
                success = False
                raise

        # Call specific handlers
        handlers = self.handlers.get(message_type, [])
        for handler in handlers:
            try:
                handler(message, context)
            except Exception as e:
                import traceback
                self.logger.error(f"Traceback: {traceback.format_exc()}")
                self.logger.error(f"Handler error for {message_type}: {e}")
                success = False
                raise

        if not handlers and not self.global_handlers:
            self.logger.warning(f"No handlers for message type: {message_type}")

        return success


class MessageBuilder:
    """Builds WebSocket messages - aligned with browser extension"""

    @staticmethod
    def build_command(
        command_type: MessageType,
        data: Optional[Dict[str, Any]] = None,
        message_id: Optional[str] = None,
    ) -> str:
        """Build command message"""
        message = create_base_message(command_type.value, data)
        if message_id:
            message.id = message_id
        return json.dumps(message.to_dict(), ensure_ascii=False)

    @staticmethod
    def build_error(
        error_message: str, original_message_id: Optional[str] = None
    ) -> str:
        """Build error message"""
        message = {
            "type": MessageType.ERROR.value,
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat(),
            "data": {"error": error_message, "originalMessageId": original_message_id},
        }

        return json.dumps(message, ensure_ascii=False)


class MessageHandler:
    """Main message handling system"""

    def __init__(self):
        self.parser = MessageParser()
        self.router = MessageRouter()
        self.logger = logging.getLogger(__name__)

        # Statistics
        self.stats = {
            "messages_received": 0,
            "messages_sent": 0,
            "invalid_messages": 0,
            "errors": 0,
            "messages_by_type": {},
        }

    def handle_incoming_message(
        self, raw_message: str, context: Optional[Dict[str, Any]] = None
    ) -> BaseMessage:
        """Handle incoming WebSocket message - aligned with browser extension"""
        self.stats["messages_received"] += 1

        # Parse message
        message = self.parser.parse_message(raw_message)

        # Update statistics
        self._update_stats(message)

        # Route message
        try:
            self.router.route_message(message, context)
        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error(f"Error routing message: {e}")
            raise

        return message

    def handle_outgoing_message(
        self, raw_message: str, context: Optional[Dict[str, Any]] = None
    ) -> BaseMessage:
        """Handle outgoing WebSocket message - aligned with browser extension"""
        self.stats["messages_sent"] += 1

        # Parse message
        message = self.parser.parse_message(raw_message)

        # Update statistics
        self._update_stats(message)

        # Route message (for logging/monitoring)
        try:
            self.router.route_message(message, context)
        except Exception as e:
            self.stats["errors"] += 1
            self.logger.error(f"Error routing outgoing message: {e}")

        return message

    def add_handler(self, message_type: MessageType, handler: Callable):
        """Add message handler"""
        self.router.add_handler(message_type, handler)

    def add_global_handler(self, handler: Callable):
        """Add global handler"""
        self.router.add_global_handler(handler)

    def remove_handler(self, message_type: MessageType, handler: Callable):
        """Remove message handler"""
        self.router.remove_handler(message_type, handler)

    def remove_global_handler(self, handler: Callable):
        """Remove global handler"""
        self.router.remove_global_handler(handler)

    def get_stats(self) -> Dict[str, Any]:
        """Get message handling statistics"""
        return self.stats.copy()

    def reset_stats(self):
        """Reset statistics"""
        self.stats = {
            "messages_received": 0,
            "messages_sent": 0,
            "invalid_messages": 0,
            "errors": 0,
            "messages_by_type": {},
        }

    def _update_stats(self, message: BaseMessage):
        """Update message statistics - aligned with browser extension"""
        msg_type = message.type
        if msg_type not in self.stats["messages_by_type"]:
            self.stats["messages_by_type"][msg_type] = 0

        self.stats["messages_by_type"][msg_type] += 1
