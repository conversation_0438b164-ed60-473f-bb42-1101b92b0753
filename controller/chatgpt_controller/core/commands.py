"""
Browser Extension Compatible Command Definitions and Builders

Provides predefined commands for browser extension operations that can be sent to the plugin.
Aligned with browser extension's TypeScript interface definitions.
"""

from typing import Dict, Any, Optional, List
from .message_types import MessageType, create_base_message, generate_message_id
from ..models.element_models import (
    create_element_query_command,
    create_page_info_command,
    create_element_watch_command,
    create_batch_element_query_command,
    create_element_screenshot_command
)
from datetime import datetime


class BrowserControlCommands:
    """Browser control commands - aligned with browser extension"""

    @staticmethod
    def open_chatgpt(
        url: Optional[str] = None,
        new_tab: bool = True,
        focus: bool = True
    ) -> Dict[str, Any]:
        """Create open ChatGPT command - aligned with browser extension"""
        data = {}
        if url:
            data["url"] = url
        data["newTab"] = new_tab
        data["focus"] = focus

        return create_base_message(MessageType.OPEN_CHATGPT.value, data).to_dict()

    @staticmethod
    def set_window_size(
        width: int,
        height: int,
        window_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create set window size command - aligned with browser extension"""
        data = {
            "width": width,
            "height": height
        }
        if window_id is not None:
            data["windowId"] = window_id

        return create_base_message(MessageType.SET_WINDOW_SIZE.value, data).to_dict()

    @staticmethod
    def take_screenshot(
        format: str = "png",
        quality: int = 90,
        full_page: bool = False,
        tab_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """Create take screenshot command - aligned with browser extension"""
        data = {
            "format": format,
            "quality": quality,
            "fullPage": full_page
        }
        if tab_id is not None:
            data["tabId"] = tab_id

        return create_base_message(MessageType.TAKE_SCREENSHOT.value, data).to_dict()


class ElementCommands:
    """Element query commands - aligned with browser extension"""

    @staticmethod
    def element_query(
        selector_type: str,
        selector_value: str,
        actions: Optional[List[Dict[str, Any]]] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create element query command - aligned with browser extension"""
        command = create_element_query_command(
            selector_type=selector_type,
            selector_value=selector_value,
            actions=actions,
            options=options
        )
        return command.model_dump()

    @staticmethod
    def page_info(info_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Create page info command - aligned with browser extension"""
        command = create_page_info_command(info_types=info_types)
        return command.model_dump()

    @staticmethod
    def element_watch(
        selector_type: str,
        selector_value: str,
        events: List[str],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create element watch command - aligned with browser extension"""
        command = create_element_watch_command(
            selector_type=selector_type,
            selector_value=selector_value,
            events=events,
            options=options
        )
        return command.model_dump()

    @staticmethod
    def batch_element_query(
        queries: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create batch element query command - aligned with browser extension"""
        command = create_batch_element_query_command(queries=queries)
        return command.model_dump()

    @staticmethod
    def element_screenshot(
        selector_type: str,
        selector_value: str,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create element screenshot command - aligned with browser extension"""
        command = create_element_screenshot_command(
            selector_type=selector_type,
            selector_value=selector_value,
            options=options
        )
        return command.model_dump()


    @staticmethod
    def click_element(
        selector_type: str,
        selector_value: str,
        x: Optional[int] = None,
        y: Optional[int] = None,
        delay: int = 0
    ) -> Dict[str, Any]:
        """Create click element command - aligned with browser extension"""
        action_params = {"delay": delay}
        if x is not None and y is not None:
            action_params.update({"x": x, "y": y})

        return ElementCommands.element_query(
            selector_type=selector_type,
            selector_value=selector_value,
            actions=[{
                "type": "click",
                "params": action_params
            }]
        )

    @staticmethod
    def input_text(
        selector_type: str,
        selector_value: str,
        text: str,
        delay: int = 0
    ) -> Dict[str, Any]:
        """Create input text command - aligned with browser extension"""
        return ElementCommands.element_query(
            selector_type=selector_type,
            selector_value=selector_value,
            actions=[{
                "type": "input",
                "params": {"text": text, "delay": delay}
            }]
        )

    @staticmethod
    def get_element_attribute(
        selector_type: str,
        selector_value: str,
        attribute: str
    ) -> Dict[str, Any]:
        """Create get element attribute command - aligned with browser extension"""
        return ElementCommands.element_query(
            selector_type=selector_type,
            selector_value=selector_value,
            actions=[{
                "type": "getAttribute",
                "params": {"attribute": attribute}
            }]
        )


class ConnectionCommands:
    """Connection commands - aligned with browser extension"""

    @staticmethod
    def ping() -> Dict[str, Any]:
        """Create ping command - aligned with browser extension"""
        return create_base_message(MessageType.PING.value, {}).to_dict()

    @staticmethod
    def connect(url: Optional[str] = None) -> Dict[str, Any]:
        """Create connect command - aligned with browser extension"""
        data = {}
        if url:
            data["url"] = url
        return create_base_message(MessageType.CONNECT.value, data).to_dict()

    @staticmethod
    def disconnect() -> Dict[str, Any]:
        """Create disconnect command - aligned with browser extension"""
        return create_base_message(MessageType.DISCONNECT.value, {}).to_dict()

    @staticmethod
    def heartbeat_response(
        original_message_id: str,
        client_id: str,
        status: str = "alive"
    ) -> Dict[str, Any]:
        """Create heartbeat response - aligned with browser extension"""
        data = {
            "server_time": datetime.now().isoformat(),
            "client_id": client_id,
            "status": status
        }
        message = create_base_message(MessageType.HEARTBEAT_RESPONSE.value, data)
        message.id = original_message_id  # Use same ID for correlation
        return message.to_dict()


class CommandValidator:
    """Simple command validator for browser extension compatibility"""

    @staticmethod
    def validate_selector(selector_type: str, selector_value: str) -> List[str]:
        """Validate element selector"""
        errors = []

        valid_types = ["xpath", "css", "id", "class", "tag", "text", "attribute"]
        if selector_type not in valid_types:
            errors.append(f"Invalid selector type: {selector_type}")

        if not selector_value or not selector_value.strip():
            errors.append("Selector value cannot be empty")

        return errors

    @staticmethod
    def validate_window_size(width: int, height: int) -> List[str]:
        """Validate window size parameters"""
        errors = []

        if width < 100 or width > 4000:
            errors.append("Width must be between 100 and 4000")

        if height < 100 or height > 4000:
            errors.append("Height must be between 100 and 4000")

        return errors

    @staticmethod
    def validate_screenshot_options(format: str, quality: int) -> List[str]:
        """Validate screenshot options"""
        errors = []

        if format not in ["png", "jpeg"]:
            errors.append("Format must be 'png' or 'jpeg'")

        if quality < 1 or quality > 100:
            errors.append("Quality must be between 1 and 100")

        return errors


# Utility functions for command creation

def create_browser_command(command_type: str, **kwargs) -> Dict[str, Any]:
    """Create browser control command with validation"""
    if command_type == MessageType.OPEN_CHATGPT.value:
        return BrowserControlCommands.open_chatgpt(**kwargs)
    elif command_type == MessageType.SET_WINDOW_SIZE.value:
        return BrowserControlCommands.set_window_size(**kwargs)
    elif command_type == MessageType.TAKE_SCREENSHOT.value:
        return BrowserControlCommands.take_screenshot(**kwargs)
    else:
        raise ValueError(f"Unknown browser command type: {command_type}")


def create_element_command(command_type: str, **kwargs) -> Dict[str, Any]:
    """Create element query command with validation"""
    if command_type == MessageType.ELEMENT_QUERY.value:
        return ElementCommands.element_query(**kwargs)
    elif command_type == MessageType.PAGE_INFO.value:
        return ElementCommands.page_info(**kwargs)
    elif command_type == MessageType.ELEMENT_WATCH.value:
        return ElementCommands.element_watch(**kwargs)
    elif command_type == MessageType.BATCH_ELEMENT_QUERY.value:
        return ElementCommands.batch_element_query(**kwargs)
    elif command_type == MessageType.ELEMENT_SCREENSHOT.value:
        return ElementCommands.element_screenshot(**kwargs)
    else:
        raise ValueError(f"Unknown element command type: {command_type}")


def create_connection_command(command_type: str, **kwargs) -> Dict[str, Any]:
    """Create connection command with validation"""
    if command_type == MessageType.PING.value:
        return ConnectionCommands.ping()
    elif command_type == MessageType.CONNECT.value:
        return ConnectionCommands.connect(**kwargs)
    elif command_type == MessageType.DISCONNECT.value:
        return ConnectionCommands.disconnect()
    elif command_type == MessageType.HEARTBEAT_RESPONSE.value:
        return ConnectionCommands.heartbeat_response(**kwargs)
    else:
        raise ValueError(f"Unknown connection command type: {command_type}")


def validate_command(command_data: Dict[str, Any]) -> List[str]:
    """Validate command data structure"""
    errors = []

    if "type" not in command_data:
        errors.append("Command must have a 'type' field")

    if "id" not in command_data:
        errors.append("Command must have an 'id' field")

    if "timestamp" not in command_data:
        errors.append("Command must have a 'timestamp' field")

    return errors
