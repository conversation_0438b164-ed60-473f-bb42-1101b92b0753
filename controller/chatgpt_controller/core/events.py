"""
Event system for ChatGPT Controller

Provides a flexible event system for decoupled communication between components.
"""

import logging
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class EventType(Enum):
    """Event type enumeration"""

    # Server events
    SERVER_STARTED = "server_started"
    SERVER_STOPPED = "server_stopped"
    SERVER_ERROR = "server_error"

    # Client connection events
    CLIENT_CONNECTED = "client_connected"
    CLIENT_DISCONNECTED = "client_disconnected"
    CLIENT_ERROR = "client_error"

    # Message events
    MESSAGE_RECEIVED = "message_received"
    MESSAGE_SENT = "message_sent"
    MESSAGE_ERROR = "message_error"

    # Command events
    COMMAND_EXECUTED = "command_executed"
    COMMAND_FAILED = "command_failed"
    COMMAND_TIMEOUT = "command_timeout"

    # GUI events
    GUI_STARTED = "gui_started"
    GUI_CLOSED = "gui_closed"
    GUI_ERROR = "gui_error"

    # Configuration events
    CONFIG_LOADED = "config_loaded"
    CONFIG_SAVED = "config_saved"
    CONFIG_CHANGED = "config_changed"

    # Custom events
    CUSTOM = "custom"


@dataclass
class Event:
    """Event data structure"""

    event_type: EventType
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    event_id: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary"""
        return {
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "data": self.data,
            "event_id": self.event_id,
        }


class EventEmitter:
    """Event emitter for publishing and subscribing to events"""

    def __init__(self, name: str = "EventEmitter"):
        self.name = name
        self.listeners: Dict[EventType, List[Callable]] = {}
        self.global_listeners: List[Callable] = []
        self.logger = logging.getLogger(__name__)
        self.event_history: List[Event] = []
        self.max_history = 1000

    def on(self, event_type: EventType, listener: Callable):
        """Subscribe to an event type"""
        if event_type not in self.listeners:
            self.listeners[event_type] = []

        self.listeners[event_type].append(listener)
        self.logger.debug(f"Added listener for {event_type.value}")

    def off(self, event_type: EventType, listener: Callable):
        """Unsubscribe from an event type"""
        if event_type in self.listeners:
            try:
                self.listeners[event_type].remove(listener)
                self.logger.debug(f"Removed listener for {event_type.value}")
            except ValueError:
                pass

    def on_any(self, listener: Callable):
        """Subscribe to all events"""
        self.global_listeners.append(listener)
        self.logger.debug("Added global event listener")

    def off_any(self, listener: Callable):
        """Unsubscribe from all events"""
        try:
            self.global_listeners.remove(listener)
            self.logger.debug("Removed global event listener")
        except ValueError:
            pass

    def emit(
        self, event_type: EventType, data: Dict[str, Any] = None, event_id: str = None
    ) -> Event:
        """Emit an event"""
        import uuid

        event = Event(
            event_type=event_type,
            timestamp=datetime.now(),
            source=self.name,
            data=data or {},
            event_id=event_id or str(uuid.uuid4()),
        )

        # Add to history
        self.event_history.append(event)
        if len(self.event_history) > self.max_history:
            self.event_history.pop(0)

        # Call global listeners
        for listener in self.global_listeners:
            try:
                listener(event)
            except Exception as e:
                self.logger.error(f"Error in global event listener: {e}")

        # Call specific listeners
        listeners = self.listeners.get(event_type, [])
        for listener in listeners:
            try:
                listener(event)
            except Exception as e:
                self.logger.error(
                    f"Error in event listener for {event_type.value}: {e}"
                )

        self.logger.debug(f"Emitted event: {event_type.value}")
        return event

    def emit_async(self, event_type: EventType, data: Dict[str, Any] = None):
        """Emit event asynchronously (non-blocking)"""
        import threading

        def emit_in_thread():
            self.emit(event_type, data)

        thread = threading.Thread(target=emit_in_thread, daemon=True)
        thread.start()

    def get_event_history(
        self, event_type: Optional[EventType] = None, limit: int = 100
    ) -> List[Event]:
        """Get event history"""
        if event_type:
            filtered_events = [
                e for e in self.event_history if e.event_type == event_type
            ]
            return filtered_events[-limit:] if filtered_events else []
        else:
            return self.event_history[-limit:]

    def clear_history(self):
        """Clear event history"""
        self.event_history.clear()

    def get_listener_count(self, event_type: Optional[EventType] = None) -> int:
        """Get number of listeners"""
        if event_type:
            return len(self.listeners.get(event_type, []))
        else:
            total = len(self.global_listeners)
            for listeners in self.listeners.values():
                total += len(listeners)
            return total

    def get_stats(self) -> Dict[str, Any]:
        """Get event statistics"""
        stats = {
            "total_events": len(self.event_history),
            "total_listeners": self.get_listener_count(),
            "global_listeners": len(self.global_listeners),
            "events_by_type": {},
            "listeners_by_type": {},
        }

        # Count events by type
        for event in self.event_history:
            event_type = event.event_type.value
            stats["events_by_type"][event_type] = (
                stats["events_by_type"].get(event_type, 0) + 1
            )

        # Count listeners by type
        for event_type, listeners in self.listeners.items():
            stats["listeners_by_type"][event_type.value] = len(listeners)

        return stats


class EventBus:
    """Global event bus for application-wide events"""

    _instance: Optional["EventBus"] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.emitter = EventEmitter("GlobalEventBus")
        return cls._instance

    def emit(self, event_type: EventType, data: Dict[str, Any] = None) -> Event:
        """Emit event on global bus"""
        return self.emitter.emit(event_type, data)

    def on(self, event_type: EventType, listener: Callable):
        """Subscribe to event on global bus"""
        self.emitter.on(event_type, listener)

    def off(self, event_type: EventType, listener: Callable):
        """Unsubscribe from event on global bus"""
        self.emitter.off(event_type, listener)

    def on_any(self, listener: Callable):
        """Subscribe to all events on global bus"""
        self.emitter.on_any(listener)

    def off_any(self, listener: Callable):
        """Unsubscribe from all events on global bus"""
        self.emitter.off_any(listener)

    def get_stats(self) -> Dict[str, Any]:
        """Get global event bus statistics"""
        return self.emitter.get_stats()

    def get_history(
        self, event_type: Optional[EventType] = None, limit: int = 100
    ) -> List[Event]:
        """Get event history from global bus"""
        return self.emitter.get_event_history(event_type, limit)


# Convenience functions for global event bus
def emit_event(event_type: EventType, data: Dict[str, Any] = None) -> Event:
    """Emit event on global event bus"""
    return EventBus().emit(event_type, data)


def subscribe_to_event(event_type: EventType, listener: Callable):
    """Subscribe to event on global event bus"""
    EventBus().on(event_type, listener)


def unsubscribe_from_event(event_type: EventType, listener: Callable):
    """Unsubscribe from event on global event bus"""
    EventBus().off(event_type, listener)


def subscribe_to_all_events(listener: Callable):
    """Subscribe to all events on global event bus"""
    EventBus().on_any(listener)


def unsubscribe_from_all_events(listener: Callable):
    """Unsubscribe from all events on global event bus"""
    EventBus().off_any(listener)


# Event listener decorators
def event_listener(event_type: EventType):
    """Decorator to register event listener"""

    def decorator(func: Callable):
        subscribe_to_event(event_type, func)
        return func

    return decorator


def global_event_listener(func: Callable):
    """Decorator to register global event listener"""
    subscribe_to_all_events(func)
    return func
