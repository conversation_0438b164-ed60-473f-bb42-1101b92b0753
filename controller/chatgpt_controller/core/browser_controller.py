"""
Browser controller implementation

This module provides a concrete implementation of browser control operations
that can be used by WebSocket server, GUI, and other components.
"""

import asyncio
import uuid
from typing import Optional, Dict, Any
import logging

from ..interfaces.browser_control import <PERSON><PERSON>rowserController, CommandResponse
from ..core.message_types import MessageType, create_base_message


class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(IBrowserController):
    """
    Concrete implementation of browser control operations
    
    This class handles the actual communication with the browser extension
    through WebSocket messages and manages command responses.
    """

    def __init__(self, message_sender):
        """
        Initialize browser controller
        
        Args:
            message_sender: Function to send messages to browser extension
                           Should have signature: async def send_message(message) -> bool
        """
        self.message_sender = message_sender
        self.logger = logging.getLogger(__name__)
        self._pending_commands: Dict[str, asyncio.Future] = {}

    async def _send_command(
        self,
        command_type: MessageType,
        data: Optional[Dict[str, Any]] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """
        Send a command to the browser extension and wait for response
        
        Args:
            command_type: Type of command to send
            data: Command data
            timeout: Command timeout
            
        Returns:
            Command response
        """
        # Generate unique message ID
        message_id = str(uuid.uuid4())
        
        # Create message
        message = create_base_message(command_type.value, data or {})
        message.id = message_id
        
        # Create future for response
        future = asyncio.Future()
        self._pending_commands[message_id] = future
        
        try:
            # Send message
            success = await self.message_sender(message.to_dict())
            
            if not success:
                return CommandResponse(
                    success=False,
                    error="Failed to send message to browser extension",
                    message_id=message_id
                )
            
            # Wait for response
            timeout = timeout or 30.0
            try:
                response_data = await asyncio.wait_for(future, timeout=timeout)
                return CommandResponse(
                    success=response_data.get("success", False),
                    data=response_data.get("data"),
                    error=response_data.get("error"),
                    message_id=message_id
                )
            except asyncio.TimeoutError:
                return CommandResponse(
                    success=False,
                    error=f"Command timed out after {timeout} seconds",
                    message_id=message_id
                )
                
        finally:
            # Clean up pending command
            self._pending_commands.pop(message_id, None)

    def handle_response(self, message_id: str, response_data: Dict[str, Any]):
        """
        Handle response from browser extension
        
        Args:
            message_id: ID of the original message
            response_data: Response data from browser
        """
        future = self._pending_commands.get(message_id)
        if future and not future.done():
            future.set_result(response_data)

    async def open_chatgpt(
        self,
        url: Optional[str] = None,
        new_tab: bool = True,
        focus: bool = True,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Open ChatGPT in the browser"""
        data = {
            "url": url,
            "newTab": new_tab,
            "focus": focus,
        }
        
        return await self._send_command(
            MessageType.OPEN_CHATGPT,
            data,
            timeout
        )

    async def set_window_size(
        self,
        width: int,
        height: int,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Set browser window size"""
        data = {
            "width": width,
            "height": height,
        }
        
        return await self._send_command(
            MessageType.SET_WINDOW_SIZE,
            data,
            timeout
        )

    async def take_screenshot(
        self,
        format: str = "png",
        quality: Optional[int] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Take a screenshot of the current page"""
        data = {
            "format": format,
        }
        
        if quality is not None:
            data["quality"] = quality
            
        return await self._send_command(
            MessageType.TAKE_SCREENSHOT,
            data,
            timeout
        )

    async def ping(
        self,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Ping the browser extension"""
        return await self._send_command(
            MessageType.PING,
            {},
            timeout
        )
