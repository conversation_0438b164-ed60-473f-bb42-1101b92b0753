"""
Element Query Types for ChatGPT Controller

Defines data types and structures for element query operations.
"""

from enum import Enum
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime


class SelectorType(Enum):
    """Element selector types"""
    XPATH = "xpath"
    CSS = "css"
    ID = "id"
    CLASS = "class"
    TAG = "tag"
    TEXT = "text"
    ATTRIBUTE = "attribute"


class ElementActionType(Enum):
    """Element action types"""
    CLICK = "click"
    INPUT = "input"
    SCROLL = "scroll"
    HOVER = "hover"
    FOCUS = "focus"
    BLUR = "blur"
    GET_ATTRIBUTE = "getAttribute"
    SET_TEXT = "setText"


@dataclass
class ElementSelector:
    """Element selector configuration"""
    type: SelectorType
    value: str
    options: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": self.type.value,
            "value": self.value,
            "options": self.options or {}
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ElementSelector":
        return cls(
            type=SelectorType(data["type"]),
            value=data["value"],
            options=data.get("options")
        )


@dataclass
class ElementAction:
    """Element action configuration"""
    type: ElementActionType
    params: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": self.type.value,
            "params": self.params or {}
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ElementAction":
        return cls(
            type=ElementActionType(data["type"]),
            params=data.get("params")
        )


@dataclass
class ElementPosition:
    """Element position information"""
    x: float
    y: float
    page_x: float
    page_y: float
    width: float
    height: float
    top: float
    left: float
    right: float
    bottom: float

    def to_dict(self) -> Dict[str, Any]:
        return {
            "x": self.x,
            "y": self.y,
            "pageX": self.page_x,
            "pageY": self.page_y,
            "width": self.width,
            "height": self.height,
            "top": self.top,
            "left": self.left,
            "right": self.right,
            "bottom": self.bottom
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ElementPosition":
        return cls(
            x=data["x"],
            y=data["y"],
            page_x=data["pageX"],
            page_y=data["pageY"],
            width=data["width"],
            height=data["height"],
            top=data["top"],
            left=data["left"],
            right=data["right"],
            bottom=data["bottom"]
        )


@dataclass
class ElementInfo:
    """Complete element information"""
    selector: str
    selector_type: SelectorType
    position: ElementPosition
    styles: Dict[str, str]
    attributes: Dict[str, Any]
    is_visible: bool
    is_in_viewport: bool
    has_children: bool
    children_count: int
    parent_selector: Optional[str] = None
    xpath: Optional[str] = None
    css_path: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "selector": self.selector,
            "selectorType": self.selector_type.value,
            "position": self.position.to_dict(),
            "styles": self.styles,
            "attributes": self.attributes,
            "isVisible": self.is_visible,
            "isInViewport": self.is_in_viewport,
            "hasChildren": self.has_children,
            "childrenCount": self.children_count,
            "parentSelector": self.parent_selector,
            "xpath": self.xpath,
            "cssPath": self.css_path
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ElementInfo":
        return cls(
            selector=data["selector"],
            selector_type=SelectorType(data["selectorType"]),
            position=ElementPosition.from_dict(data["position"]),
            styles=data["styles"],
            attributes=data["attributes"],
            is_visible=data["isVisible"],
            is_in_viewport=data["isInViewport"],
            has_children=data["hasChildren"],
            children_count=data["childrenCount"],
            parent_selector=data.get("parentSelector"),
            xpath=data.get("xpath"),
            css_path=data.get("cssPath")
        )


@dataclass
class ElementQueryCommand:
    """Element query command"""
    id: str
    timestamp: datetime
    selector: ElementSelector
    actions: Optional[List[ElementAction]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "ELEMENT_QUERY",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "selector": self.selector.to_dict(),
            "actions": [action.to_dict() for action in (self.actions or [])]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ElementQueryCommand":
        return cls(
            id=data["id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            selector=ElementSelector.from_dict(data["selector"]),
            actions=[ElementAction.from_dict(action) for action in data.get("actions", [])]
        )


@dataclass
class PageInfoCommand:
    """Page information query command"""
    id: str
    timestamp: datetime
    info: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "PAGE_INFO",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "info": self.info
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PageInfoCommand":
        return cls(
            id=data["id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            info=data["info"]
        )


@dataclass
class ElementWatchCommand:
    """Element watch command"""
    id: str
    timestamp: datetime
    selector: ElementSelector
    events: List[str]
    options: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "ELEMENT_WATCH",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "selector": self.selector.to_dict(),
            "events": self.events,
            "options": self.options or {}
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ElementWatchCommand":
        return cls(
            id=data["id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            selector=ElementSelector.from_dict(data["selector"]),
            events=data["events"],
            options=data.get("options")
        )


@dataclass
class BatchElementQuery:
    """Single query in batch"""
    id: str
    selector: ElementSelector
    actions: Optional[List[ElementAction]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "selector": self.selector.to_dict(),
            "actions": [action.to_dict() for action in (self.actions or [])]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BatchElementQuery":
        return cls(
            id=data["id"],
            selector=ElementSelector.from_dict(data["selector"]),
            actions=[ElementAction.from_dict(action) for action in data.get("actions", [])]
        )


@dataclass
class BatchElementQueryCommand:
    """Batch element query command"""
    id: str
    timestamp: datetime
    queries: List[BatchElementQuery]

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "BATCH_ELEMENT_QUERY",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "queries": [query.to_dict() for query in self.queries]
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BatchElementQueryCommand":
        return cls(
            id=data["id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            queries=[BatchElementQuery.from_dict(query) for query in data["queries"]]
        )


@dataclass
class ElementScreenshotCommand:
    """Element screenshot command"""
    id: str
    timestamp: datetime
    selector: ElementSelector
    options: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "type": "ELEMENT_SCREENSHOT",
            "id": self.id,
            "timestamp": self.timestamp.isoformat(),
            "selector": self.selector.to_dict(),
            "options": self.options or {}
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ElementScreenshotCommand":
        return cls(
            id=data["id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            selector=ElementSelector.from_dict(data["selector"]),
            options=data.get("options")
        )


# Union type for all element commands
ElementCommand = Union[
    ElementQueryCommand,
    PageInfoCommand,
    ElementWatchCommand,
    BatchElementQueryCommand,
    ElementScreenshotCommand
]


def create_element_command(command_type: str, data: Dict[str, Any]) -> ElementCommand:
    """Factory function to create element commands"""
    command_map = {
        "ELEMENT_QUERY": ElementQueryCommand,
        "PAGE_INFO": PageInfoCommand,
        "ELEMENT_WATCH": ElementWatchCommand,
        "BATCH_ELEMENT_QUERY": BatchElementQueryCommand,
        "ELEMENT_SCREENSHOT": ElementScreenshotCommand
    }
    
    command_class = command_map.get(command_type)
    if not command_class:
        raise ValueError(f"Unknown element command type: {command_type}")
    
    return command_class.from_dict(data)
