"""
Configuration management for ChatGPT Controller

Provides centralized configuration management with support for JSON files,
environment variables, and runtime configuration updates.
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict


@dataclass
class ServerConfig:
    """WebSocket server configuration"""

    host: str = "localhost"
    port: int = 8765
    ping_interval: int = 20
    ping_timeout: int = 10
    max_clients: int = 10
    max_message_size: int = 1024 * 1024  # 1MB

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class GUIConfig:
    """GUI application configuration"""

    window_width: int = 1200
    window_height: int = 800
    auto_scroll: bool = True
    show_timestamps: bool = True
    max_messages: int = 1000
    theme: str = "dark"
    font_family: str = "Consolas"
    font_size: int = 9
    language: str = "auto"  # auto, en, zh

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class LoggingConfig:
    """Logging configuration"""

    level: str = "INFO"
    file: str = "logs/controller.log"
    max_size: str = "10MB"
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    console_output: bool = True

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class DebugConfig:
    """Debug and monitoring configuration"""

    auto_save_interval: int = 30
    export_format: str = "json"
    include_raw_messages: bool = True
    enable_statistics: bool = True
    performance_monitoring: bool = False

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class DatabaseConfig:
    """Database configuration"""

    url: str = "sqlite+aiosqlite:///data/chatgpt_controller.db"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10
    pool_timeout: int = 30
    pool_recycle: int = 3600

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class APIConfig:
    """API server configuration"""

    host: str = "localhost"
    port: int = 8000
    reload: bool = False
    workers: int = 1
    log_level: str = "info"
    cors_origins: list = None
    rate_limit_requests: int = 100
    rate_limit_window: int = 60

    def __post_init__(self):
        if self.cors_origins is None:
            self.cors_origins = ["*"]

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class AuthConfig:
    """Authentication configuration"""

    enabled: bool = True
    api_keys: list = None
    require_auth_for_websocket: bool = True
    require_auth_for_api: bool = True
    auth_header_name: str = "Authorization"
    auth_scheme: str = "Bearer"

    def __post_init__(self):
        if self.api_keys is None:
            # Default API key for development - should be changed in production
            self.api_keys = ["dev-api-key-12345"]

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    def is_valid_api_key(self, api_key: str) -> bool:
        """Check if the provided API key is valid"""
        if not self.enabled:
            return True
        return api_key in self.api_keys


class Config:
    """Main configuration class"""

    DEFAULT_CONFIG_FILE = "config.json"

    def __init__(self, config_file: Optional[Path] = None):
        self.config_file = config_file or Path(self.DEFAULT_CONFIG_FILE)

        # Initialize with default configurations
        self.server = ServerConfig()
        self.gui = GUIConfig()
        self.logging = LoggingConfig()
        self.debug = DebugConfig()
        self.database = DatabaseConfig()
        self.api = APIConfig()
        self.auth = AuthConfig()

        # Load configuration if file exists
        if self.config_file.exists():
            self.load_from_file(self.config_file)

        # Override with environment variables
        self._load_from_environment()

    def load_from_file(self, config_file: Path):
        """Load configuration from JSON file"""
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config_data = json.load(f)

            # Update configurations
            if "server" in config_data:
                self._update_dataclass(self.server, config_data["server"])

            if "gui" in config_data:
                self._update_dataclass(self.gui, config_data["gui"])

            if "logging" in config_data:
                self._update_dataclass(self.logging, config_data["logging"])

            if "debug" in config_data:
                self._update_dataclass(self.debug, config_data["debug"])

            if "database" in config_data:
                self._update_dataclass(self.database, config_data["database"])

            if "api" in config_data:
                self._update_dataclass(self.api, config_data["api"])

            if "auth" in config_data:
                self._update_dataclass(self.auth, config_data["auth"])

            self.config_file = config_file
            logging.info(f"Configuration loaded from {config_file}")

        except FileNotFoundError:
            logging.warning(f"Config file not found: {config_file}")
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in config file: {e}")
        except Exception as e:
            logging.error(f"Error loading config: {e}")

    def save_to_file(self, config_file: Optional[Path] = None):
        """Save configuration to JSON file"""
        target_file = config_file or self.config_file

        try:
            # Create directory if it doesn't exist
            target_file.parent.mkdir(parents=True, exist_ok=True)

            config_data = {
                "server": self.server.to_dict(),
                "gui": self.gui.to_dict(),
                "logging": self.logging.to_dict(),
                "debug": self.debug.to_dict(),
                "database": self.database.to_dict(),
                "api": self.api.to_dict(),
                "auth": self.auth.to_dict(),
            }

            with open(target_file, "w", encoding="utf-8") as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)

            logging.info(f"Configuration saved to {target_file}")

        except Exception as e:
            logging.error(f"Error saving config: {e}")

    def _update_dataclass(self, target_obj, source_dict: Dict[str, Any]):
        """Update dataclass fields from dictionary"""
        for key, value in source_dict.items():
            if hasattr(target_obj, key):
                setattr(target_obj, key, value)

    def _load_from_environment(self):
        """Load configuration from environment variables"""
        # Server configuration
        if host := os.getenv("CHATGPT_CONTROLLER_HOST"):
            self.server.host = host

        if port := os.getenv("CHATGPT_CONTROLLER_PORT"):
            try:
                self.server.port = int(port)
            except ValueError:
                logging.warning(f"Invalid port in environment: {port}")

        # Logging configuration
        if log_level := os.getenv("CHATGPT_CONTROLLER_LOG_LEVEL"):
            self.logging.level = log_level.upper()

        if log_file := os.getenv("CHATGPT_CONTROLLER_LOG_FILE"):
            self.logging.file = log_file

        # Authentication configuration
        if api_keys := os.getenv("CHATGPT_CONTROLLER_API_KEYS"):
            # Support comma-separated API keys
            self.auth.api_keys = [key.strip() for key in api_keys.split(",") if key.strip()]

        if auth_enabled := os.getenv("CHATGPT_CONTROLLER_AUTH_ENABLED"):
            self.auth.enabled = auth_enabled.lower() in ("true", "1", "yes", "on")

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key.split(".")

        if len(keys) < 2:
            return default

        section = keys[0]
        field = keys[1]

        if section == "server" and hasattr(self.server, field):
            return getattr(self.server, field)
        elif section == "gui" and hasattr(self.gui, field):
            return getattr(self.gui, field)
        elif section == "logging" and hasattr(self.logging, field):
            return getattr(self.logging, field)
        elif section == "debug" and hasattr(self.debug, field):
            return getattr(self.debug, field)

        return default

    def set(self, key: str, value: Any):
        """Set configuration value using dot notation"""
        keys = key.split(".")

        if len(keys) < 2:
            return

        section = keys[0]
        field = keys[1]

        if section == "server" and hasattr(self.server, field):
            setattr(self.server, field, value)
        elif section == "gui" and hasattr(self.gui, field):
            setattr(self.gui, field, value)
        elif section == "logging" and hasattr(self.logging, field):
            setattr(self.logging, field, value)
        elif section == "debug" and hasattr(self.debug, field):
            setattr(self.debug, field, value)

    def to_dict(self) -> Dict[str, Any]:
        """Convert entire configuration to dictionary"""
        return {
            "server": self.server.to_dict(),
            "gui": self.gui.to_dict(),
            "logging": self.logging.to_dict(),
            "debug": self.debug.to_dict(),
            "database": self.database.to_dict(),
            "api": self.api.to_dict(),
            "auth": self.auth.to_dict(),
        }

    def validate(self) -> list:
        """Validate configuration and return list of errors"""
        errors = []

        # Validate server configuration
        if not (1 <= self.server.port <= 65535):
            errors.append(f"Invalid server port: {self.server.port}")

        if self.server.ping_interval <= 0:
            errors.append(f"Invalid ping interval: {self.server.ping_interval}")

        if self.server.max_clients <= 0:
            errors.append(f"Invalid max clients: {self.server.max_clients}")

        # Validate GUI configuration
        if self.gui.window_width <= 0 or self.gui.window_height <= 0:
            errors.append("Invalid window dimensions")

        if self.gui.max_messages <= 0:
            errors.append(f"Invalid max messages: {self.gui.max_messages}")

        # Validate logging configuration
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.logging.level.upper() not in valid_levels:
            errors.append(f"Invalid log level: {self.logging.level}")

        return errors


# Global configuration instance
_config_instance: Optional[Config] = None


def get_config() -> Config:
    """Get global configuration instance"""
    global _config_instance
    if _config_instance is None:
        _config_instance = Config()
    return _config_instance


def set_config(config: Config):
    """Set global configuration instance"""
    global _config_instance
    _config_instance = config


def load_config(config_file: Path) -> Config:
    """Load configuration from file and set as global instance"""
    config = Config(config_file)
    set_config(config)
    return config
