"""
Core module for ChatGPT Controller

Contains the fundamental components including configuration, message handling,
and base classes used throughout the application.
"""

from .config import Config, get_config
from .message_types import MessageType, BaseMessage, ConnectionStatus
from .message_handler import MessageHandler, MessageParser
from .commands import BrowserControlCommands, ElementCommands, ConnectionCommands
from .events import EventEmitter, Event

__all__ = [
    "Config",
    "get_config",
    "MessageType",
    "BaseMessage",
    "ConnectionStatus",
    "MessageHandler",
    "MessageParser",
    "BrowserControlCommands",
    "ElementCommands",
    "ConnectionCommands",
    "EventEmitter",
    "Event",
]
