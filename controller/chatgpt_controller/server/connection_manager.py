"""
Connection manager for WebSocket server

Manages client connections, connection state, and connection-related operations.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Set, Optional, List
import websockets
from websockets.protocol import State

from ..core.message_types import ConnectionInfo, ConnectionStatus
from ..core.events import <PERSON>Emitter, EventType


class ConnectionManager:
    """Manages WebSocket client connections"""

    def __init__(self):
        self.connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.connection_info: Dict[str, ConnectionInfo] = {}
        self.logger = logging.getLogger(__name__)
        self.events = EventEmitter("ConnectionManager")

        # Connection limits and settings
        self.max_connections = 10
        self.connection_timeout = 300  # 5 minutes

    def add_connection(
        self,
        websocket: websockets.WebSocketServerProtocol,
        user_agent: str = "",
        client_type: str = "",
    ) -> str:
        """Add a new connection"""
        if len(self.connections) >= self.max_connections:
            raise ConnectionError(
                f"Maximum connections ({self.max_connections}) reached"
            )

        client_id = str(uuid.uuid4())
        remote_address = (
            websocket.remote_address[0] if websocket.remote_address else "unknown"
        )
        remote_port = websocket.remote_address[1] if websocket.remote_address else 0

        # Store connection
        self.connections[client_id] = websocket

        # Store connection info
        self.connection_info[client_id] = ConnectionInfo(
            client_id=client_id,
            remote_address=remote_address,
            remote_port=remote_port,
            connected_at=datetime.now(),
            status=ConnectionStatus.CONNECTED,
            user_agent=user_agent,
            client_type=client_type,
        )

        self.logger.info(
            f"Client connected: {client_id} from {remote_address}:{remote_port}"
        )

        # Emit connection event
        self.events.emit(
            EventType.CLIENT_CONNECTED,
            {
                "client_id": client_id,
                "remote_address": remote_address,
                "remote_port": remote_port,
                "user_agent": user_agent,
                "client_type": client_type,
            },
        )

        return client_id

    def remove_connection(self, client_id: str):
        """Remove a connection"""
        if client_id in self.connections:
            connection_info = self.connection_info.get(client_id)

            # Update status
            if connection_info:
                connection_info.status = ConnectionStatus.DISCONNECTED

            # Remove from active connections
            del self.connections[client_id]

            self.logger.info(f"Client disconnected: {client_id}")

            # Emit disconnection event
            self.events.emit(
                EventType.CLIENT_DISCONNECTED,
                {
                    "client_id": client_id,
                    "connection_info": connection_info.to_dict()
                    if connection_info
                    else {},
                },
            )

    def get_connection(
        self, client_id: str
    ) -> Optional[websockets.WebSocketServerProtocol]:
        """Get connection by client ID"""
        return self.connections.get(client_id)

    def get_connection_info(self, client_id: str) -> Optional[ConnectionInfo]:
        """Get connection info by client ID"""
        return self.connection_info.get(client_id)

    def get_all_connections(self) -> Dict[str, websockets.WebSocketServerProtocol]:
        """Get all active connections"""
        return self.connections.copy()

    def get_all_connection_info(self) -> Dict[str, ConnectionInfo]:
        """Get all connection info"""
        return self.connection_info.copy()

    def get_connection_count(self) -> int:
        """Get number of active connections"""
        return len(self.connections)

    def find_connection_by_websocket(
        self, websocket: websockets.WebSocketServerProtocol
    ) -> Optional[str]:
        """Find client ID by websocket instance"""
        for client_id, ws in self.connections.items():
            if ws == websocket:
                return client_id
        return None

    def update_connection_info(self, client_id: str, **kwargs):
        """Update connection information"""
        if client_id in self.connection_info:
            connection_info = self.connection_info[client_id]

            for key, value in kwargs.items():
                if hasattr(connection_info, key):
                    setattr(connection_info, key, value)

    def is_connection_active(self, client_id: str) -> bool:
        """Check if connection is active"""
        if client_id not in self.connections:
            return False

        websocket = self.connections[client_id]
        return websocket.state is State.OPEN

    async def close_connection(self, client_id: str, code: int = 1000, reason: str = ""):
        """Close a specific connection"""
        if client_id in self.connections:
            websocket = self.connections[client_id]
            try:
                await websocket.close(code, reason)
            except Exception as e:
                self.logger.error(f"Error closing connection {client_id}: {e}")

            self.remove_connection(client_id)

    async def close_all_connections(self, code: int = 1000, reason: str = "Server shutdown"):
        """Close all connections"""
        if not self.connections:
            return

        tasks = []
        for client_id, websocket in self.connections.items():
            tasks.append(websocket.close(code, reason))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        self.connections.clear()

        # Update all connection statuses
        for connection_info in self.connection_info.values():
            connection_info.status = ConnectionStatus.DISCONNECTED

    def get_connections_by_type(self, client_type: str) -> List[str]:
        """Get connections by client type"""
        matching_clients = []
        for client_id, info in self.connection_info.items():
            if info.client_type == client_type and client_id in self.connections:
                matching_clients.append(client_id)
        return matching_clients

    def get_connection_stats(self) -> Dict[str, any]:
        """Get connection statistics"""
        stats = {
            "total_connections": len(self.connections),
            "max_connections": self.max_connections,
            "connections_by_type": {},
            "connections_by_status": {},
            "average_connection_time": 0,
        }

        # Count by type and status
        total_connection_time = 0
        for info in self.connection_info.values():
            # By type
            client_type = info.client_type or "unknown"
            stats["connections_by_type"][client_type] = (
                stats["connections_by_type"].get(client_type, 0) + 1
            )

            # By status
            status = info.status.value
            stats["connections_by_status"][status] = (
                stats["connections_by_status"].get(status, 0) + 1
            )

            # Connection time
            if info.status == ConnectionStatus.CONNECTED:
                connection_time = (datetime.now() - info.connected_at).total_seconds()
                total_connection_time += connection_time

        # Calculate average connection time
        if len(self.connections) > 0:
            stats["average_connection_time"] = total_connection_time / len(
                self.connections
            )

        return stats

    def cleanup_stale_connections(self):
        """Clean up stale connections"""
        stale_connections = []
        current_time = datetime.now()

        for client_id, info in self.connection_info.items():
            if client_id in self.connections:
                # Check if connection is still open
                websocket = self.connections[client_id]
                if websocket.state is not State.OPEN:
                    stale_connections.append(client_id)
                    continue

                # Check for timeout
                connection_age = (current_time - info.connected_at).total_seconds()
                if connection_age > self.connection_timeout:
                    stale_connections.append(client_id)

        # Remove stale connections
        for client_id in stale_connections:
            self.logger.warning(f"Removing stale connection: {client_id}")
            self.remove_connection(client_id)

        return len(stale_connections)

    def set_max_connections(self, max_connections: int):
        """Set maximum number of connections"""
        self.max_connections = max_connections
        self.logger.info(f"Maximum connections set to {max_connections}")

    def set_connection_timeout(self, timeout_seconds: int):
        """Set connection timeout"""
        self.connection_timeout = timeout_seconds
        self.logger.info(f"Connection timeout set to {timeout_seconds} seconds")
