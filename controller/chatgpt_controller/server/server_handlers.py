"""
Server message handlers for ChatGPT Controller

Provides default message handlers for server-side message processing.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from ..core.message_types import MessageType, BaseMessage
from ..core.events import EventType, emit_event


class ServerMessageHandlers:
    """Default message handlers for server-side processing"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def _get_timestamp_str(self, timestamp) -> str:
        """Convert timestamp to string, handling both datetime objects and strings"""
        if isinstance(timestamp, str):
            return timestamp
        elif hasattr(timestamp, 'isoformat'):
            return timestamp.isoformat()
        else:
            return str(timestamp)

    def handle_init_message(
        self, message: BaseMessage, context: Dict[str, Any]
    ):
        """Handle plugin initialization message"""
        data = message.data or {}
        client_id = context.get("client_id", "unknown")

        # Extract client information
        client_type = data.get("clientType", "unknown")
        url = data.get("url", "")
        user_agent = data.get("userAgent", "")

        self.logger.info(
            f"Plugin initialized - Client: {client_id}, Type: {client_type}, URL: {url}"
        )

        # Update connection info if connection manager is available
        if "connection_manager" in context:
            connection_manager = context["connection_manager"]
            connection_manager.update_connection_info(
                client_id, client_type=client_type, user_agent=user_agent
            )

        # Emit initialization event
        emit_event(
            EventType.CLIENT_CONNECTED,
            {
                "client_id": client_id,
                "client_type": client_type,
                "url": url,
                "user_agent": user_agent,
                "initialization_data": data,
            },
        )

    def handle_heartbeat(self, message: BaseMessage, context: Dict[str, Any]):
        """Handle heartbeat message from client"""
        client_id = context.get("client_id", "unknown")
        websocket = context.get("websocket")

        # Log heartbeat (debug level to avoid spam)
        self.logger.debug(f"💓 Heartbeat received from client {client_id}")

        # Extract heartbeat data
        data = message.data or {}
        client_type = data.get("clientType", "unknown")
        last_activity = data.get("lastActivity", 0)

        # Send heartbeat response
        if websocket:
            try:
                from ..core.commands import ConnectionCommands
                import asyncio
                import json

                # Create heartbeat response using the command builder
                response_message = ConnectionCommands.heartbeat_response(
                    original_message_id=message.id,
                    client_id=client_id,
                    status="alive"
                )

                # Send response
                asyncio.create_task(
                    websocket.send(json.dumps(response_message))
                )

                self.logger.debug(f"💓 Heartbeat response sent to client {client_id}")

            except Exception as e:
                self.logger.error(f"❌ Failed to send heartbeat response to {client_id}: {e}")

        # Update client activity tracking (optional)
        # This could be used for connection monitoring and statistics
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "heartbeat",
                "client_type": client_type,
                "last_activity": last_activity,
                "timestamp": message.timestamp,
            },
        )

    def handle_chatgpt_message(
        self, message: BaseMessage, context: Dict[str, Any]
    ):
        """Handle ChatGPT message forwarded from plugin"""
        data = message.data or {}
        client_id = context.get("client_id", "unknown")

        # Log message details
        message_preview = str(data).replace("\n", " ")[:100]
        self.logger.info(f"ChatGPT message from {client_id}: {message_preview}...")

        # Emit message received event
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "chatgpt_message",
                "data": data,
                "timestamp": message.timestamp,
            },
        )

    def handle_monitored_api_request(
        self, msg: BaseMessage, context: Dict[str, Any]
    ):
        """Handle monitored API request from plugin"""
        data = msg.data
        client_id = context.get("client_id", "unknown")

        # Extract API information
        api_type = data.get("api_type", "unknown")
        api_endpoint = data.get("api_endpoint", "")
        request_data = data.get("request", {})
        metadata = data.get("metadata", {})

        # Log API request details
        method = request_data.get("method", "")
        url = request_data.get("url", "")
        has_body = request_data.get("requestBody", {}).get("hasData", False)

        self.logger.info(
            f"Monitored API Request [{api_type}] from {client_id}: {method} {url} "
            f"(has_body: {has_body})"
        )

        # Log request body if present
        if has_body:
            request_body = request_data.get("requestBody", {}).get("raw")
            if request_body:
                body_preview = str(request_body).replace("\n", " ")[:200]
                self.logger.debug(f"Request body preview: {body_preview}...")

        # Emit monitored API event
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "monitored_api_request",
                "api_type": api_type,
                "api_endpoint": api_endpoint,
                "method": method,
                "url": url,
                "request_data": request_data,
                "metadata": metadata,
                "timestamp": self._get_timestamp_str(msg.timestamp),
            },
        )

    def handle_monitored_api_headers(
        self, msg: BaseMessage, context: Dict[str, Any]
    ):
        """Handle monitored API headers from plugin"""
        data = msg.data
        client_id = context.get("client_id", "unknown")

        api_type = data.get("api_type", "unknown")
        api_endpoint = data.get("api_endpoint", "")

        self.logger.debug(f"Monitored API Headers [{api_type}] from {client_id}")

        # Emit headers event
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "monitored_api_headers",
                "api_type": api_type,
                "api_endpoint": api_endpoint,
                "data": data,
                "timestamp": self._get_timestamp_str(msg.timestamp),
            },
        )

    def handle_monitored_api_response_headers(
        self, msg: BaseMessage, context: Dict[str, Any]
    ):
        """Handle monitored API response headers from plugin"""
        data = msg.data
        client_id = context.get("client_id", "unknown")

        api_type = data.get("api_type", "unknown")
        api_endpoint = data.get("api_endpoint", "")
        response_data = data.get("response", {})
        status_code = response_data.get("statusCode", 0)

        self.logger.debug(
            f"Monitored API Response Headers [{api_type}] from {client_id}: {status_code}"
        )

        # Emit response headers event
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "monitored_api_response_headers",
                "api_type": api_type,
                "api_endpoint": api_endpoint,
                "status_code": status_code,
                "data": data,
                "timestamp": self._get_timestamp_str(msg.timestamp),
            },
        )

    def handle_monitored_api_completed(
        self, parsed_message: BaseMessage, context: Dict[str, Any]
    ):
        """Handle monitored API completion from plugin"""
        data = parsed_message.data
        client_id = context.get("client_id", "unknown")

        api_type = data.get("api_type", "unknown")
        api_endpoint = data.get("api_endpoint", "")
        response_data = data.get("response", {})
        status_code = response_data.get("statusCode", 0)
        timing = data.get("timing", {})

        self.logger.info(
            f"Monitored API Completed [{api_type}] from {client_id}: {status_code} "
            f"(timestamp: {timing.get('timeStamp', 'unknown')})"
        )

        # Emit completion event
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "monitored_api_completed",
                "api_type": api_type,
                "api_endpoint": api_endpoint,
                "status_code": status_code,
                "timing": timing,
                "data": data,
                "timestamp": self._get_timestamp_str(parsed_message.timestamp),
            },
        )

    def handle_monitored_api_response_data(
        self, parsed_message: BaseMessage, context: Dict[str, Any]
    ):
        """Handle monitored API response data from plugin"""
        data = parsed_message.data
        client_id = context.get("client_id", "unknown")

        api_type = data.get("api_type", "unknown")
        api_endpoint = data.get("api_endpoint", "")
        response_data = data.get("responseData")

        # Log response data
        if response_data:
            data_preview = str(response_data).replace("\n", " ")[:200]
            self.logger.info(
                f"Monitored API Response Data [{api_type}] from {client_id}: {data_preview}..."
            )

        # Emit response data event
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "monitored_api_response_data",
                "api_type": api_type,
                "api_endpoint": api_endpoint,
                "response_data": response_data,
                "data": data,
                "timestamp": self._get_timestamp_str(parsed_message.timestamp),
            },
        )
    def handle_operation_result(
        self, msg: BaseMessage, context: Dict[str, Any]
    ):
        """Handle operation result from plugin"""
        data = msg.data
        client_id = context.get("client_id", "unknown")

        success = data.get("success", None)
        error = data.get("error", "")
        operation_id = data.get("id", "unknown")

        if success:
            self.logger.info(
                f"Operation {operation_id} succeeded for client {client_id}"
            )
            emit_event(
                EventType.COMMAND_EXECUTED,
                {
                    "client_id": client_id,
                    "operation_id": operation_id,
                    "success": True,
                    "data": data,
                },
            )
        else:
            self.logger.warning(
                f"Operation {operation_id} failed for client {client_id}: {error}"
            )
            emit_event(
                EventType.COMMAND_FAILED,
                {
                    "client_id": client_id,
                    "operation_id": operation_id,
                    "error": error,
                    "data": data,
                },
            )

    def handle_browser_control_response(
        self, msg: BaseMessage, context: Dict[str, Any]
    ):
        """Handle browser control response from plugin"""
        data = msg.data
        self.logger.info(f"msg stat: {msg.success}")
        success = msg.success

        client_id = context.get("client_id", "unknown")
        error = data.get("error", "")
        response_data = data.get("data", {})

        if success:
            self.logger.info(
                f"Browser control operation completed successfully from {client_id}"
            )

            # Log specific response data based on operation type
            if "tabId" in response_data:
                self.logger.info(f"  - Tab ID: {response_data['tabId']}")
            if "windowId" in response_data:
                self.logger.info(f"  - Window ID: {response_data['windowId']}")
            if "dataUrl" in response_data:
                self.logger.info(f"  - Screenshot captured (format: {response_data.get('format', 'unknown')})")
            if "width" in response_data and "height" in response_data:
                self.logger.info(f"  - Window size: {response_data['width']}x{response_data['height']}")
        else:
            self.logger.warning(
                f"Browser control operation failed from {client_id}: {error}"
            )

        # Emit browser control response event
        emit_event(
            EventType.MESSAGE_RECEIVED,
            {
                "client_id": client_id,
                "message_type": "browser_control_response",
                "success": success,
                "data": data,
                "timestamp": self._get_timestamp_str(msg.timestamp), 
            },
        )

    def handle_ping(self, msg: BaseMessage, context: Dict[str, Any]):
        """Handle ping message"""
        client_id = context.get("client_id", "unknown")
        websocket = context.get("websocket")

        if websocket:
            # Send pong response
            import asyncio
            import json

            pong_message = {
                "type": MessageType.PONG.value,
                "id": msg.id,
                "timestamp": datetime.now().isoformat(),
                "data": {},
            }

            async def send_pong():
                try:
                    await websocket.send(json.dumps(pong_message))
                    self.logger.debug(f"Sent pong to client {client_id}")
                except Exception as e:
                    self.logger.error(f"Failed to send pong to client {client_id}: {e}")

            asyncio.create_task(send_pong())

    def handle_pong(self, msg: BaseMessage, context: Dict[str, Any]):
        """Handle pong message"""
        client_id = context.get("client_id", "unknown")
        self.logger.debug(f"Received pong from client {client_id}")

    def handle_error(self, msg: BaseMessage, context: Dict[str, Any]):
        """Handle error message"""
        data = msg.data
        client_id = context.get("client_id", "unknown")

        error_message = data.get("error", "Unknown error")
        original_message_id = data.get("originalMessageId", "")

        self.logger.error(
            f"Error from client {client_id}: {error_message} (Original: {original_message_id})"
        )

        # Emit error event
        emit_event(
            EventType.MESSAGE_ERROR,
            {
                "client_id": client_id,
                "error_message": error_message,
                "original_message_id": original_message_id,
                "data": data,
            },
        )

    def handle_unknown_message(
        self, parsed_message: BaseMessage, context: Dict[str, Any]
    ):
        """Handle unknown message type"""
        client_id = context.get("client_id", "unknown")
        message_type = (
            parsed_message.raw_message[:100] if parsed_message.raw_message else "empty"
        )

        self.logger.warning(
            f"Unknown message type from client {client_id}: {message_type}"
        )

        # Emit unknown message event
        emit_event(
            EventType.MESSAGE_ERROR,
            {
                "client_id": client_id,
                "error_type": "unknown_message_type",
                "message_preview": message_type,
                "validation_errors": parsed_message.validation_errors,
            },
        )

    def get_handler_mapping(self) -> Dict[MessageType, callable]:
        """Get mapping of message types to handlers"""
        return {
            MessageType.INIT: self.handle_init_message,
            MessageType.HEARTBEAT: self.handle_heartbeat,
            MessageType.CHATGPT_MESSAGE: self.handle_chatgpt_message,
            MessageType.OPERATION_RESULT: self.handle_operation_result,
            MessageType.BROWSER_CONTROL_RESPONSE: self.handle_browser_control_response,
            # Monitored API handlers
            MessageType.MONITORED_API_REQUEST: self.handle_monitored_api_request,
            MessageType.MONITORED_API_HEADERS: self.handle_monitored_api_headers,
            MessageType.MONITORED_API_RESPONSE_HEADERS: self.handle_monitored_api_response_headers,
            MessageType.MONITORED_API_COMPLETED: self.handle_monitored_api_completed,
            MessageType.MONITORED_API_RESPONSE_DATA: self.handle_monitored_api_response_data,
            # System handlers
            MessageType.PING: self.handle_ping,
            MessageType.PONG: self.handle_pong,
            MessageType.ERROR: self.handle_error,
            MessageType.UNKNOWN: self.handle_unknown_message,
        }

    def register_handlers(self, message_handler):
        """Register all handlers with a message handler instance"""
        handler_mapping = self.get_handler_mapping()

        for message_type, handler_func in handler_mapping.items():
            message_handler.add_handler(message_type, handler_func)

        self.logger.info(f"Registered {len(handler_mapping)} server message handlers")


class StatisticsHandler:
    """Handler for collecting message statistics"""

    def __init__(self):
        self.stats = {
            "messages_by_type": {},
            "messages_by_client": {},
            "errors_by_type": {},
            "total_messages": 0,
            "total_errors": 0,
            "start_time": datetime.now(),
        }
        self.logger = logging.getLogger(__name__)

    def handle_any_message(
        self, message: BaseMessage, context: Dict[str, Any]
    ):
        """Handle any message for statistics"""
        client_id = context.get("client_id", "unknown")
        message_type = message.type

        # Update total count
        self.stats["total_messages"] += 1

        # Update by type
        if message_type not in self.stats["messages_by_type"]:
            self.stats["messages_by_type"][message_type] = 0
        self.stats["messages_by_type"][message_type] += 1

        # Update by client
        if client_id not in self.stats["messages_by_client"]:
            self.stats["messages_by_client"][client_id] = 0
        self.stats["messages_by_client"][client_id] += 1

    def get_stats(self) -> Dict[str, Any]:
        """Get current statistics"""
        uptime = (datetime.now() - self.stats["start_time"]).total_seconds()

        return {
            **self.stats,
            "uptime_seconds": uptime,
            "messages_per_minute": (self.stats["total_messages"] / max(uptime / 60, 1)),
        }

    def reset_stats(self):
        """Reset statistics"""
        self.stats = {
            "messages_by_type": {},
            "messages_by_client": {},
            "errors_by_type": {},
            "total_messages": 0,
            "total_errors": 0,
            "start_time": datetime.now(),
        }
        self.logger.info("Statistics reset")
