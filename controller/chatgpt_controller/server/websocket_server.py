"""
WebSocket Server for ChatGPT Controller

Main WebSocket server implementation with connection management, message handling,
and integration with the core message system.
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, Set, Optional, Callable, Any, List
from datetime import datetime
import uuid
from websockets.protocol import State

from ..core.config import get_config
from ..core.message_types import MessageType, BaseMessage, MessageDirection
from ..core.message_handler import <PERSON><PERSON>and<PERSON>, MessageBuilder
from ..core.events import EventEmitter, EventType, emit_event
from ..interfaces.browser_control import IWebSocketServer, CommandResponse
from ..core.browser_controller import BrowserController
from .connection_manager import ConnectionManager
from .server_handlers import ServerMessageHandlers, StatisticsHandler
from ..handlers.element_handler import ElementQueryManager


class WebSocketServer(IWebSocketServer):
    """WebSocket server for communicating with ChatGPT Forward Plugin"""

    def __init__(self, host: str = None, port: int = None):
        self.config = get_config()
        self.host = host or self.config.server.host
        self.port = port or self.config.server.port

        # Core components
        self.connection_manager = ConnectionManager()
        self.message_handler = MessageHandler()
        self.server_handlers = ServerMessageHandlers()
        self.statistics_handler = StatisticsHandler()

        # Element query manager
        self.element_query_manager = ElementQueryManager(self)

        # Browser controller for command operations
        self.browser_controller = BrowserController(self._send_message_to_all)

        # Server state
        self.server = None
        self.running = False
        self.start_time = None
        self.logger = logging.getLogger(__name__)

        # Events
        self.events = EventEmitter("WebSocketServer")

        # Setup message handlers
        self._setup_message_handlers()

        # Callbacks for external integration
        self.on_message_received: Optional[Callable] = None
        self.on_message_sent: Optional[Callable] = None
        self.on_client_connected: Optional[Callable] = None
        self.on_client_disconnected: Optional[Callable] = None

    def _setup_message_handlers(self):
        """Setup default message handlers"""
        # Register server handlers
        self.server_handlers.register_handlers(self.message_handler)

        # Register statistics handler as global handler
        self.message_handler.add_global_handler(
            self.statistics_handler.handle_any_message
        )

        # Setup element query handlers
        self.element_query_manager.setup_handlers(self.message_handler)

        # Setup browser control response handler
        self.message_handler.add_handler(
            MessageType.BROWSER_CONTROL_RESPONSE, self._handle_browser_control_response
        )

        # Setup connection manager event handlers
        self.connection_manager.events.on(
            EventType.CLIENT_CONNECTED, self._on_client_connected
        )
        self.connection_manager.events.on(
            EventType.CLIENT_DISCONNECTED, self._on_client_disconnected
        )

    async def _authenticate_websocket_connection(self, websocket) -> bool:
        """
        Authenticate WebSocket connection using API key from initial message

        Args:
            websocket: WebSocket connection object

        Returns:
            True if authentication successful, False otherwise
        """
        # Skip authentication if disabled
        if not self.config.auth.enabled or not self.config.auth.require_auth_for_websocket:
            return True

        try:
            # Wait for the first message which should contain authentication
            auth_message = await asyncio.wait_for(websocket.recv(), timeout=10.0)

            try:
                message_data = json.loads(auth_message)
            except json.JSONDecodeError:
                self.logger.warning(f"WebSocket connection from {websocket.remote_address} sent invalid JSON for authentication")
                return False

            # Check if this is an authentication message
            if message_data.get('type') == 'auth' or 'api_key' in message_data:
                api_key = message_data.get('api_key')

                if not api_key:
                    self.logger.warning(f"WebSocket connection from {websocket.remote_address} missing API key in auth message")
                    return False

                # Validate API key
                if not self.config.auth.is_valid_api_key(api_key):
                    self.logger.warning(f"WebSocket connection from {websocket.remote_address} with invalid API key")
                    return False

                self.logger.info(f"WebSocket connection from {websocket.remote_address} authenticated successfully")
                return True
            else:
                # If first message is not auth, check if it contains api_key field
                api_key = message_data.get('api_key')
                if api_key and self.config.auth.is_valid_api_key(api_key):
                    self.logger.info(f"WebSocket connection from {websocket.remote_address} authenticated via message api_key")
                    return True
                else:
                    self.logger.warning(f"WebSocket connection from {websocket.remote_address} first message missing authentication")
                    return False

        except asyncio.TimeoutError:
            self.logger.warning(f"WebSocket connection from {websocket.remote_address} authentication timeout")
            return False
        except Exception as e:
            self.logger.error(f"Error during WebSocket authentication: {e}")
            return False

    async def handle_client(self, websocket):
        """Handle new client connection"""
        client_id = None

        try:
            # Check authentication before accepting connection
            if not await self._authenticate_websocket_connection(websocket):
                await websocket.close(code=1008, reason="Authentication failed")
                return

            # Add connection
            client_id = self.connection_manager.add_connection(websocket)

            self.logger.info(
                f"Client {client_id} connected from {websocket.remote_address}"
            )

            # Handle messages
            async for message in websocket:
                await self._process_message(websocket, message, client_id)

        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"Client {client_id} disconnected normally")
        except Exception as e:
            self.logger.error(f"Error handling client {client_id}: {e}")
        finally:
            if client_id:
                self.connection_manager.remove_connection(client_id)

    async def _process_message(self, websocket, raw_message: str, client_id: str):
        """Process incoming message from client"""
        try:
            # Create context for message handling
            context = {
                "client_id": client_id,
                "websocket": websocket,
                "connection_manager": self.connection_manager,
                "server": self,
            }

            # Handle message
            parsed_message = self.message_handler.handle_incoming_message(
                raw_message, context
            )

            # Call external callback if set
            if self.on_message_received:
                try:
                    self.on_message_received(
                        parsed_message.type,
                        parsed_message.to_dict(),
                        websocket,
                    )
                except Exception as e:
                    self.logger.error(f"Error in external message callback: {e}")

            self.logger.debug(
                f"Processed message from {client_id}: {parsed_message.type}"
            )

        except Exception as e:
            import traceback
            self.logger.error(f"Error processing message from {client_id}: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")

            # Send error response
            try:
                error_message = MessageBuilder.build_error(
                    f"Error processing message: {str(e)}"
                )
                await websocket.send(error_message)
            except Exception:
                pass  # Connection might be closed

    async def send_command(
        self,
        command_type: MessageType,
        data: Optional[Dict[str, Any]] = None,
        target_client: Optional[str] = None,
        message_id: Optional[str] = None
    ) -> bool:
        """Send command to plugin(s)"""
        message_id = message_id or str(uuid.uuid4())
        message = MessageBuilder.build_command(command_type, data, message_id)

        success = False

        # Determine target clients
        if target_client:
            target_clients = (
                [target_client]
                if target_client in self.connection_manager.connections
                else []
            )
        else:
            target_clients = list(self.connection_manager.connections.keys())

        if not target_clients:
            self.logger.warning("No target clients for command")
            return False

        # Send to target clients
        for client_id in target_clients:
            websocket = self.connection_manager.get_connection(client_id)
            if websocket and websocket.state is State.OPEN:
                try:
                    await websocket.send(message)
                    self.logger.info(
                        f"Sent command {command_type.value} to client {client_id}"
                    )
                    success = True

                    # Handle outgoing message for logging
                    context = {
                        "client_id": client_id,
                        "websocket": websocket,
                        "server": self,
                    }
                    self.message_handler.handle_outgoing_message(message, context)

                    # Call external callback if set
                    if self.on_message_sent:
                        try:
                            parsed = self.message_handler.parser.parse_message(
                                message, MessageDirection.OUTGOING
                            )
                            self.on_message_sent(
                                command_type.value, parsed.to_dict(), websocket
                            )
                        except Exception as e:
                            self.logger.error(f"Error in external sent callback: {e}")

                except websockets.exceptions.ConnectionClosed:
                    self.logger.warning(
                        f"Connection to client {client_id} closed while sending"
                    )
                    self.connection_manager.remove_connection(client_id)
                except Exception as e:
                    self.logger.error(
                        f"Error sending message to client {client_id}: {e}"
                    )

        return success

    async def start_server(self):
        """Start the WebSocket server"""
        if self.running:
            self.logger.warning("Server is already running")
            return

        try:
            self.server = await websockets.serve(
                self.handle_client,
                self.host,
                self.port,
                ping_interval=self.config.server.ping_interval,
                ping_timeout=self.config.server.ping_timeout,
                max_size=self.config.server.max_message_size,
            )

            self.running = True
            self.start_time = datetime.now()
            self.logger.info(f"WebSocket server started on {self.host}:{self.port}")

            # Emit server started event
            emit_event(
                EventType.SERVER_STARTED,
                {
                    "host": self.host,
                    "port": self.port,
                    "config": self.config.server.to_dict(),
                },
            )

        except Exception as e:
            import traceback
            self.logger.error(f"Failed to start server: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            emit_event(
                EventType.SERVER_ERROR,
                {"error": str(e), "host": self.host, "port": self.port},
            )
            raise

    async def stop_server(self):
        """Stop the WebSocket server"""
        if not self.running:
            return

        self.running = False

        try:
            # Close all client connections
            await self.connection_manager.close_all_connections()

            # Close server
            if self.server:
                self.server.close()
                await self.server.wait_closed()
                self.server = None

            self.logger.info("WebSocket server stopped")

            # Emit server stopped event
            emit_event(EventType.SERVER_STOPPED, {"host": self.host, "port": self.port})

        except Exception as e:
            self.logger.error(f"Error stopping server: {e}")
            emit_event(EventType.SERVER_ERROR, {"error": str(e), "operation": "stop"})

    def _on_client_connected(self, event):
        """Handle client connected event"""
        if self.on_client_connected:
            try:
                client_id = event.data["client_id"]
                connection_info = self.connection_manager.get_connection_info(client_id)
                if connection_info:
                    self.on_client_connected(
                        self.connection_manager.get_connection(client_id),
                        f"{connection_info.remote_address}:{connection_info.remote_port}",
                    )
            except Exception as e:
                self.logger.error(f"Error in client connected callback: {e}")

    def _on_client_disconnected(self, event):
        """Handle client disconnected event"""
        if self.on_client_disconnected:
            try:
                client_id = event.data["client_id"]
                connection_info = event.data.get("connection_info", {})
                self.on_client_disconnected(
                    None,  # WebSocket is already closed
                    f"{connection_info.get('remote_address', 'unknown')}:{connection_info.get('remote_port', 0)}",
                )
            except Exception as e:
                self.logger.error(f"Error in client disconnected callback: {e}")

    def _handle_browser_control_response(self, msg: BaseMessage, context: Dict[str, Any]):
        """Handle browser control response from extension"""
        if hasattr(msg, 'id') and msg.id:
            # Format response data for browser controller
            # The browser controller expects a response with 'success', 'error', and 'data' fields
            response_data = {
                "success": msg.success,
                "error": msg.data.get("error") if msg.data else None,
                "data": msg.data.get("data") if msg.data else msg.data
            }

            self.logger.debug(f"🔄 Forwarding browser control response: success={response_data['success']}, error={response_data['error']}")

            # Forward formatted response to browser controller
            self.browser_controller.handle_response(msg.id, response_data)

    def get_connected_clients_count(self) -> int:
        """Get number of connected clients"""
        return self.connection_manager.get_connection_count()

    def is_running(self) -> bool:
        """Check if server is running"""
        return self.running

    def get_server_stats(self) -> Dict[str, Any]:
        """Get comprehensive server statistics"""
        connection_stats = self.connection_manager.get_connection_stats()
        message_stats = self.message_handler.get_stats()
        statistics_stats = self.statistics_handler.get_stats()

        return {
            "server": {
                "host": self.host,
                "port": self.port,
                "running": self.running,
                "uptime": statistics_stats.get("uptime_seconds", 0),
            },
            "connections": connection_stats,
            "messages": message_stats,
            "statistics": statistics_stats,
        }

    def add_message_handler(self, message_type: MessageType, handler: Callable):
        """Add custom message handler"""
        self.message_handler.add_handler(message_type, handler)

    def remove_message_handler(self, message_type: MessageType, handler: Callable):
        """Remove message handler"""
        self.message_handler.remove_handler(message_type, handler)

    def cleanup_connections(self):
        """Clean up stale connections"""
        return self.connection_manager.cleanup_stale_connections()

    def set_max_connections(self, max_connections: int):
        """Set maximum number of connections"""
        self.connection_manager.set_max_connections(max_connections)

    def get_connection_info(self, client_id: str = None):
        """Get connection information"""
        if client_id:
            return self.connection_manager.get_connection_info(client_id)
        else:
            return self.connection_manager.get_all_connection_info()

    # Element Query Methods
    def send_element_query(self, command_data: Dict[str, Any]) -> str:
        """Send element query command to plugin"""
        return self.element_query_manager.send_query(command_data)

    def find_element_by_id(self, element_id: str, actions: Optional[List[Dict]] = None) -> str:
        """Find element by ID"""
        command_data = self.element_query_manager.builder.find_element_by_id(element_id, actions)
        return self.send_element_query(command_data)

    def find_element_by_css(self, css_selector: str, actions: Optional[List[Dict]] = None) -> str:
        """Find element by CSS selector"""
        command_data = self.element_query_manager.builder.find_element_by_css(css_selector, actions)
        return self.send_element_query(command_data)

    def click_element_by_id(self, element_id: str, x: Optional[int] = None, y: Optional[int] = None) -> str:
        """Click element by ID"""
        command_data = self.element_query_manager.builder.click_element_by_id(element_id, x, y)
        return self.send_element_query(command_data)

    def input_text_by_id(self, element_id: str, text: str) -> str:
        """Input text to element by ID"""
        command_data = self.element_query_manager.builder.input_text_by_id(element_id, text)
        return self.send_element_query(command_data)

    def get_page_info_query(self) -> str:
        """Get page information via element query"""
        command_data = self.element_query_manager.builder.get_page_info()
        return self.send_element_query(command_data)

    def get_element_query_result(self, query_id: str) -> Optional[Dict[str, Any]]:
        """Get element query result"""
        return self.element_query_manager.handler.get_query_result(query_id)

    def get_element_query_stats(self) -> Dict[str, Any]:
        """Get element query statistics"""
        return self.element_query_manager.handler.get_stats()

    # Browser Control Interface Implementation

    async def open_chatgpt(
        self,
        url: Optional[str] = None,
        new_tab: bool = True,
        focus: bool = True,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Open ChatGPT in the browser"""
        return await self.browser_controller.open_chatgpt(url, new_tab, focus, timeout)

    async def set_window_size(
        self,
        width: int,
        height: int,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Set browser window size"""
        return await self.browser_controller.set_window_size(width, height, timeout)

    async def take_screenshot(
        self,
        format: str = "png",
        quality: Optional[int] = None,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Take a screenshot of the current page"""
        return await self.browser_controller.take_screenshot(format, quality, timeout)

    async def ping(
        self,
        timeout: Optional[float] = None,
    ) -> CommandResponse:
        """Ping the browser extension"""
        return await self.browser_controller.ping(timeout)

    # Connection Manager Interface Implementation

    def is_connected(self) -> bool:
        """Check if any clients are connected"""
        return len(self.connection_manager.connections) > 0

    async def send_message_to_all(self, message: Dict[str, Any]) -> bool:
        """Send a message to all connected clients"""
        return await self._send_message_to_all(message)

    # Server Interface Implementation

    def get_uptime(self) -> float:
        """Get server uptime in seconds"""
        if not self.start_time or not self.running:
            return 0.0
        return (datetime.now() - self.start_time).total_seconds()

    # Helper method for browser controller
    async def _send_message_to_all(self, message: Dict[str, Any]) -> bool:
        """Send message to all connected clients (helper for browser controller)"""
        if not self.connection_manager.connections:
            return False

        success = True
        for client_id, websocket in self.connection_manager.connections.items():
            try:
                if websocket.state == State.OPEN:
                    await websocket.send(json.dumps(message))
                else:
                    success = False
            except Exception as e:
                self.logger.error(f"Error sending message to client {client_id}: {e}")
                success = False

        return success
