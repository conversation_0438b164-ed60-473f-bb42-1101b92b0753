#!/usr/bin/env python3
"""
ChatGPT Forward Plugin Controller

Main application entry point using modular architecture.
"""

import sys
import argparse
import asyncio
from pathlib import Path

# Add the chatgpt_controller package to the path
sys.path.insert(0, str(Path(__file__).parent))

from chatgpt_controller import (
    Config,
    get_config,
    setup_logging,
    WebSocketServer,
)
from chatgpt_controller.core.events import EventType, subscribe_to_event
from chatgpt_controller.utils.logging import RichLogger

# Create logger for this module
logger = RichLogger(__name__)


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="ChatGPT Forward Plugin Controller",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    parser.add_argument("--host", default="localhost", help="WebSocket server host")
    parser.add_argument("--port", type=int, default=8765, help="WebSocket server port")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--config", type=Path, help="Configuration file path")
    parser.add_argument("--no-gui", action="store_true", help="Run without GUI")
    parser.add_argument("--auto-start", action="store_true", help="Auto-start server")

    return parser.parse_args()


def setup_event_handlers():
    """Setup global event handlers"""

    def on_server_started(event):
        logger.server_event(
            f"Server started on {event.data['host']}:{event.data['port']}"
        )

    def on_server_stopped(event):
        logger.server_event("Server stopped")

    def on_client_connected(event):
        client_id = event.data["client_id"]
        client_type = event.data.get("client_type", "unknown")
        logger.client_event(f"Client connected: {client_id} ({client_type})")

    def on_client_disconnected(event):
        client_id = event.data["client_id"]
        logger.client_event(f"Client disconnected: {client_id}")

    def on_message_received(event):
        message_type = event.data.get("message_type", "unknown")
        client_id = event.data.get("client_id", "unknown")
        logger.message_event(
            f"Message received: {message_type} from {client_id}"
        )

    # Subscribe to events
    subscribe_to_event(EventType.SERVER_STARTED, on_server_started)
    subscribe_to_event(EventType.SERVER_STOPPED, on_server_stopped)
    subscribe_to_event(EventType.CLIENT_CONNECTED, on_client_connected)
    subscribe_to_event(EventType.CLIENT_DISCONNECTED, on_client_disconnected)
    subscribe_to_event(EventType.MESSAGE_RECEIVED, on_message_received)


async def run_server_mode(args):
    """Run in server-only mode"""
    logger.info("Starting ChatGPT Controller in server mode...")

    # Setup event handlers
    setup_event_handlers()

    # Create and configure server
    server = WebSocketServer(args.host, args.port)

    try:
        # Start server
        await server.start_server()

        if args.auto_start:
            logger.info("Server started automatically")

        logger.success(f"Server running on {args.host}:{args.port}")
        logger.info("Press Ctrl+C to stop...")

        # Keep server running
        while server.is_running():
            await asyncio.sleep(1)

            # Cleanup stale connections periodically
            if hasattr(server, "cleanup_connections"):
                server.cleanup_connections()

    except KeyboardInterrupt:
        logger.info("\nShutting down server...")
    except Exception as e:
        import traceback
        logger.error(f"Server error: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
    finally:
        await server.stop_server()
        logger.info("Server stopped")


def run_gui_mode(args):
    """Run in GUI mode"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        from chatgpt_controller.gui.main_window import MainWindow

        logger.info("Starting ChatGPT Controller with GUI...")

        app = QApplication(sys.argv)
        app.setApplicationName("ChatGPT Forward Plugin Controller")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("ChatGPT Controller")

        # Set application style
        app.setStyle("Fusion")

        # Create main window
        window = MainWindow()

        # Apply configuration from command line
        if hasattr(window, "connection_panel"):
            window.connection_panel.host_input.setText(args.host)
            window.connection_panel.port_input.setValue(args.port)

        # Auto-start server if requested
        if args.auto_start:
            QTimer.singleShot(1000, window.start_server)

        window.show()

        return app.exec_()

    except ImportError as e:
        logger.error(f"GUI dependencies not available: {e}")
        logger.info("Please install PyQt5 with: uv add PyQt5")
        logger.info("Or run with --no-gui")
        return 1


def main():
    """Main application entry point"""
    args = parse_arguments()

    # Load configuration
    if args.config:
        config = Config(args.config)
    else:
        config = get_config()

    # Setup logging
    log_level = "DEBUG" if args.debug else config.logging.level
    setup_logging(log_level)

    logger.info("ChatGPT Forward Plugin Controller")
    logger.info("=" * 40)

    try:
        if args.no_gui:
            # Run server-only mode
            return asyncio.run(run_server_mode(args))
        else:
            # Run GUI mode
            return run_gui_mode(args)

    except Exception as e:
        logger.error(f"Application error: {e}")
        if args.debug:
            import traceback

            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
