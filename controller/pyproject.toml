[project]
name = "controller"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # "pyautogui>=0.9.54",
    "pyqt5>=5.15.11",
    # "pywinctl>=0.4.1",
    "rich>=13.0.0",
    "websockets>=15.0.1",
    "pydantic>=2.0.0",
    "fastapi[standard]>=0.116.1",
    "sqlalchemy>=2.0.41",
    "alembic>=1.16.4",
    "aiosqlite>=0.21.0",
    "uvicorn>=0.35.0",
    "psutil>=7.0.0",
    "aiohttp>=3.12.14",
]

[dependency-groups]
dev = [
    "ipykernel>=6.30.0",
]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
