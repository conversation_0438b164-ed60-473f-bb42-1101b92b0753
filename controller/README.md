# ChatGPT Forward Plugin Controller

A professional Python-based remote controller with modular architecture, WebSocket server, and PyQt5 GUI for manipulating the ChatGPT Forward Plugin.

## 🏗️ Architecture Overview

The controller features a modular architecture with clear separation of concerns:

```
chatgpt_controller/
├── core/                   # Core business logic
│   ├── config.py          # Configuration management
│   ├── message_types.py   # Message type definitions
│   ├── message_handler.py # Message parsing and routing
│   ├── commands.py        # ChatGPT command definitions
│   ├── element_types.py   # Element query type definitions
│   └── events.py          # Event system
├── handlers/              # Specialized message handlers
│   └── element_handler.py # Element query handler
├── server/                # WebSocket server components
│   ├── websocket_server.py # Main WebSocket server
│   ├── connection_manager.py # Connection management
│   └── server_handlers.py # Server message handlers
├── gui/                   # PyQt5 GUI components
│   ├── main_window.py     # Main application window
│   ├── debug_console.py   # Debug console widget
│   └── panels/            # UI panels and widgets
├── utils/                 # Utilities and helpers
│   ├── logging.py         # Logging utilities
│   ├── helpers.py         # Helper functions
│   ├── paths.py           # Path management
│   └── statistics.py      # Statistics tracking
└── tests/                 # Test suite
    ├── test_core.py       # Core module tests
    ├── test_server.py     # Server module tests
    └── test_utils.py      # Utils module tests
```

## 🚀 Key Improvements

### Modular Design
- **Separation of Concerns**: Each module has a single responsibility
- **Loose Coupling**: Modules communicate through well-defined interfaces
- **High Cohesion**: Related functionality is grouped together
- **Extensibility**: Easy to add new features without modifying existing code

### Enhanced Configuration
- **Dataclass-based Config**: Type-safe configuration with validation
- **Environment Variables**: Support for environment-based configuration
- **JSON Configuration**: Human-readable configuration files
- **Runtime Updates**: Configuration can be modified at runtime

### Element Query System
- **Remote Element Access**: Query and manipulate web page elements remotely
- **Multiple Selector Types**: Support for XPath, CSS, ID, class, tag, text, and attribute selectors
- **Element Operations**: Click, input text, scroll, hover, focus, and more
- **Batch Queries**: Execute multiple element queries in a single request
- **Real-time Events**: Monitor element events and state changes
- **Page Information**: Get comprehensive page details and metadata

### Robust Message Handling
- **Type Safety**: Enum-based message types with validation
- **Error Handling**: Comprehensive error handling and reporting
- **Message Routing**: Flexible message routing system
- **Statistics**: Built-in message statistics and monitoring

### Event-Driven Architecture
- **Event System**: Decoupled communication through events
- **Global Event Bus**: Application-wide event coordination
- **Event History**: Event tracking and debugging
- **Custom Events**: Support for custom event types

### Professional Logging
- **Structured Logging**: Consistent logging format across modules
- **Log Rotation**: Automatic log file rotation
- **Colored Console**: Enhanced console output with colors
- **Context Filters**: Contextual information in log messages

## 📦 Installation

1. Navigate to the controller directory:
   ```bash
   cd controller
   ```

2. Install dependencies (already done):
   ```bash
   uv sync
   ```

## 🎯 Usage

### Quick Start

Test the element query functionality:
```bash
python test_element_query.py
```

Run the interactive demo:
```bash
python interactive_demo.py
```

Run the main application:
```bash
python main.py
```

### Element Query Examples

```python
from chatgpt_controller.server.websocket_server import WebSocketServer

# Create and start server
server = WebSocketServer()
await server.start()

# Find element by ID
query_id = server.find_element_by_id("submit-button")
result = server.wait_for_element_query_result(query_id, 10)

# Click element
query_id = server.click_element_by_id("my-button")

# Input text
query_id = server.input_text_by_id("username", "testuser")

# Get page information
query_id = server.get_page_info()
result = server.wait_for_element_query_result(query_id, 10)
```

### Command Line Options

```bash
# Basic usage
python main_refactored.py

# Server-only mode
python main_refactored.py --no-gui --auto-start

# Custom host and port
python main_refactored.py --host 0.0.0.0 --port 8765

# Debug mode
python main_refactored.py --debug

# Custom configuration
python main_refactored.py --config custom_config.json
```

### Configuration

Create a `config.json` file for custom settings:

```json
{
  "server": {
    "host": "localhost",
    "port": 8765,
    "ping_interval": 20,
    "ping_timeout": 10,
    "max_clients": 10,
    "max_message_size": 1048576
  },
  "gui": {
    "window_width": 1200,
    "window_height": 800,
    "auto_scroll": true,
    "show_timestamps": true,
    "max_messages": 1000,
    "theme": "dark",
    "font_family": "Consolas",
    "font_size": 9
  },
  "logging": {
    "level": "INFO",
    "file": "logs/controller.log",
    "max_size": "10MB",
    "backup_count": 5,
    "console_output": true
  },
  "debug": {
    "auto_save_interval": 30,
    "export_format": "json",
    "include_raw_messages": true,
    "enable_statistics": true,
    "performance_monitoring": false
  }
}
```

## 🔧 Development

### Project Structure

The refactored architecture follows Python best practices:

- **Package Structure**: Proper Python package with `__init__.py` files
- **Import Management**: Clean imports with `__all__` definitions
- **Type Hints**: Comprehensive type annotations
- **Documentation**: Docstrings for all modules and functions
- **Error Handling**: Proper exception handling throughout

### Core Modules

#### Configuration (`core/config.py`)
- Dataclass-based configuration with validation
- Environment variable support
- JSON file loading/saving
- Runtime configuration updates

#### Message Types (`core/message_types.py`)
- Enum-based message type definitions
- Message direction tracking
- Validation error reporting
- Message categorization

#### Message Handler (`core/message_handler.py`)
- Message parsing and validation
- Flexible routing system
- Statistics tracking
- Error handling

#### Commands (`core/commands.py`)
- ChatGPT command definitions
- Command validation
- Command history tracking
- Template-based command building

#### Events (`core/events.py`)
- Event emitter implementation
- Global event bus
- Event history tracking
- Decorator-based event handling

### Server Modules

#### WebSocket Server (`server/websocket_server.py`)
- Async WebSocket server implementation
- Integration with core message system
- Connection management
- Performance monitoring

#### Connection Manager (`server/connection_manager.py`)
- Client connection tracking
- Connection state management
- Connection cleanup
- Statistics reporting

#### Server Handlers (`server/server_handlers.py`)
- Default message handlers
- Plugin integration
- Event emission
- Error handling

### Utility Modules

#### Logging (`utils/logging.py`)
- Centralized logging configuration
- Colored console output
- Log rotation
- Context filters

#### Helpers (`utils/helpers.py`)
- Common utility functions
- Decorators for common patterns
- Data formatting functions
- Validation utilities

#### Paths (`utils/paths.py`)
- Platform-specific path handling
- Directory management
- Path utilities
- File operations

#### Statistics (`utils/statistics.py`)
- Message statistics tracking
- Performance monitoring
- System resource monitoring
- Data export

## 🧪 Testing

Run the comprehensive test suite:

```bash
uv run python test_refactored.py
```

The test suite covers:
- Module imports
- Configuration management
- Message handling
- Command generation
- Event system
- Utility functions
- Server functionality

## 🔄 Migration from Original

The refactored version maintains compatibility with the original plugin while providing:

1. **Better Organization**: Code is organized into logical modules
2. **Improved Maintainability**: Easier to understand and modify
3. **Enhanced Testing**: Comprehensive test coverage
4. **Better Documentation**: Clear documentation for all components
5. **Professional Standards**: Follows Python best practices

### Key Changes

- **Modular Architecture**: Split monolithic files into focused modules
- **Type Safety**: Added comprehensive type hints
- **Configuration**: Improved configuration management
- **Event System**: Added event-driven architecture
- **Error Handling**: Enhanced error handling and reporting
- **Testing**: Added comprehensive test suite
- **Documentation**: Improved documentation and examples

## 🚀 Next Steps

1. **Run Tests**: Verify the refactored architecture works correctly
2. **Test Integration**: Test with the ChatGPT Forward Plugin
3. **Customize Configuration**: Adjust settings for your environment
4. **Extend Functionality**: Add custom features using the modular architecture

## 📝 License

This project is part of the ChatGPT Forward Plugin system.

---

The refactored architecture provides a solid foundation for future development while maintaining all the functionality of the original controller. The modular design makes it easy to extend, test, and maintain.
