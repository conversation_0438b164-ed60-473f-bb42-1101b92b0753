#!/usr/bin/env python3
"""
ChatGPT Controller SDK Demo

A simple demonstration of the ChatGPT Controller SDK functionality.
This script shows how to connect to the browser extension and perform basic operations.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from chatgpt_controller.sdk import (
        ChatGPTControllerSDK,
        setup_sdk_logging,
        create_console
    )
    from rich.console import Console
    from rich.panel import Panel
except ImportError as e:
    logger.error(f"Failed to import SDK: {e}")
    logger.error("Make sure you're in the controller directory and the package is installed")
    sys.exit(1)

# Create Rich console for better output
console = create_console()


async def demo_basic_functionality():
    """Demonstrate basic SDK functionality"""
    
    logger.info("🚀 ChatGPT Controller SDK Demo")
    logger.info("=" * 40)
    
    # Create SDK instance with Rich logging
    sdk = ChatGPTControllerSDK(
        url="ws://localhost:8765",
        timeout=10.0,
        auto_reconnect=True,
        logger=setup_sdk_logging(console=console)
    )
    
    try:
        # Connect to browser extension
        logger.info("📡 Connecting to browser extension...")
        await sdk.connect()
        logger.info("✅ Connected successfully!")
        
        # Test connection with ping
        logger.info("🏓 Testing connection...")
        latency = await sdk.ping()
        logger.info(f"⚡ Connection latency: {latency:.3f} seconds")
        
        # Get connection information
        conn_info = sdk.get_connection_info()
        logger.info(f"📊 Connection state: {conn_info['state']}")
        
        # Get page information
        logger.info("📄 Getting page information...")
        try:
            page_info = await sdk.get_page_info()
            if page_info.success:
                data = page_info.data
                logger.info(f"🌐 Current URL: {data.get('url', 'N/A')}")
                logger.info(f"📝 Page Title: {data.get('title', 'N/A')}")
                logger.info(f"📐 Page Size: {data.get('size', 'N/A')}")
            else:
                logger.warning(f"⚠️  Failed to get page info: {page_info.error}")
        except Exception as e:
            logger.warning(f"⚠️  Could not get page info: {e}")
        
        # Try to find some common elements
        logger.info("🔍 Looking for common page elements...")
        
        # Look for body element (should always exist)
        try:
            body_result = await sdk.find_element_by_css("body")
            if body_result.success:
                elements = body_result.data.get("elements", [])
                logger.info(f"✅ Found body element: {len(elements)} matches")
            else:
                logger.warning(f"⚠️  Could not find body element: {body_result.error}")
        except Exception as e:
            logger.warning(f"⚠️  Error finding body element: {e}")
        
        # Try browser control operations
        logger.info("🌐 Testing browser control...")
        
        # Try to take a screenshot
        try:
            screenshot_result = await sdk.take_screenshot()
            if screenshot_result.success:
                logger.info("📸 Screenshot taken successfully!")
            else:
                logger.warning(f"⚠️  Screenshot failed: {screenshot_result.error}")
        except Exception as e:
            logger.warning(f"⚠️  Could not take screenshot: {e}")
        
        # Try to open ChatGPT (might fail if already open)
        try:
            logger.info("🤖 Attempting to open ChatGPT...")
            chatgpt_result = await sdk.open_chatgpt()
            if chatgpt_result.success:
                logger.info("✅ ChatGPT opened successfully!")
            else:
                logger.info(f"ℹ️  ChatGPT open result: {chatgpt_result.error}")
        except Exception as e:
            logger.info(f"ℹ️  ChatGPT might already be open: {e}")
        
        logger.info("🎉 Demo completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        raise
    
    finally:
        # Disconnect
        logger.info("🔌 Disconnecting...")
        await sdk.disconnect()
        logger.info("👋 Disconnected from browser extension")


async def demo_context_manager():
    """Demonstrate context manager usage"""
    
    logger.info("\n🔄 Context Manager Demo")
    logger.info("-" * 30)
    
    # Using context manager for automatic connection management
    try:
        async with ChatGPTControllerSDK("ws://localhost:8765") as sdk:
            logger.info("✅ Connected via context manager")
            
            # Quick ping test
            latency = await sdk.ping()
            logger.info(f"⚡ Ping: {latency:.3f}s")
            
            # Get basic page info
            page_info = await sdk.get_page_info(["url", "title"])
            if page_info.success:
                logger.info(f"📄 Page: {page_info.data.get('title', 'Unknown')}")
        
        logger.info("✅ Automatically disconnected via context manager")
        
    except Exception as e:
        logger.error(f"❌ Context manager demo failed: {e}")


async def demo_error_handling():
    """Demonstrate error handling"""
    
    logger.info("\n🛡️  Error Handling Demo")
    logger.info("-" * 30)
    
    try:
        async with ChatGPTControllerSDK("ws://localhost:8765") as sdk:
            # Try to find a non-existent element with short timeout
            try:
                result = await sdk.find_element_by_id("definitely-does-not-exist", timeout=1.0)
                logger.info(f"🔍 Search result: {result.success}")
            except Exception as e:
                logger.info(f"✅ Caught expected error: {type(e).__name__}: {e}")
            
            # Try an operation that might fail
            try:
                await sdk.click_element_by_css("non-existent-button", timeout=1.0)
            except Exception as e:
                logger.info(f"✅ Caught click error: {type(e).__name__}: {e}")
        
        logger.info("✅ Error handling demo completed")
        
    except Exception as e:
        logger.error(f"❌ Error handling demo failed: {e}")


async def main():
    """Main demo function"""
    
    print("🎯 ChatGPT Controller SDK Demo")
    print("=" * 50)
    print("This demo will test the SDK connection and basic functionality.")
    print("Make sure the WebSocket server is running on localhost:8765")
    print("and the browser extension is loaded and connected.")
    print()
    
    try:
        # Run basic functionality demo
        await demo_basic_functionality()
        
        # Run context manager demo
        await demo_context_manager()
        
        # Run error handling demo
        await demo_error_handling()
        
        print("\n🎉 All demos completed successfully!")
        print("The SDK is working correctly and ready for use.")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Demo failed: {e}")
        print("Check that:")
        print("1. The WebSocket server is running (python main.py --no-gui --auto-start)")
        print("2. The browser extension is loaded and connected")
        print("3. The extension is connected to ws://localhost:8765")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
