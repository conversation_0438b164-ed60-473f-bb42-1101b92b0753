#!/usr/bin/env python3
"""
ChatGPT Controller API Server

Standalone FastAPI server that can run alongside or independently of the WebSocket server.
"""

import sys
import asyncio
import argparse
from pathlib import Path

# Add the chatgpt_controller package to the path
sys.path.insert(0, str(Path(__file__).parent))

from chatgpt_controller import get_config, setup_logging
from chatgpt_controller.api.app import create_app
from chatgpt_controller.api.services import WebSocketMonitoringService, ScreenshotService
from chatgpt_controller.server.websocket_server import WebSocketServer

from chatgpt_controller.utils.logging import RichLogger

# Create logger
logger = RichLogger(__name__)


async def run_api_server_only(args):
    """Run only the FastAPI server without WebSocket server"""
    try:
        import uvicorn
        
        config = get_config()
        
        # Create FastAPI app
        app = create_app(config)
        
        logger.info("🚀 Starting ChatGPT Controller API Server (API only mode)")
        logger.info(f"📡 API Server: http://{config.api.host}:{config.api.port}")
        logger.info("⚠️  WebSocket server not running - some features may be limited")
        
        # Run with uvicorn
        uvicorn_config = uvicorn.Config(
            app,
            host=config.api.host,
            port=config.api.port,
            log_level=config.api.log_level,
            reload=config.api.reload and args.debug,
            workers=1,  # Always use 1 worker for development
        )
        
        server = uvicorn.Server(uvicorn_config)
        await server.serve()
        
    except Exception as e:
        logger.error(f"❌ Error running API server: {e}")
        return 1


async def run_combined_servers(args):
    """Run both WebSocket and FastAPI servers concurrently"""
    try:
        import uvicorn
        
        config = get_config()
        
        # Create WebSocket server
        websocket_server = WebSocketServer(args.ws_host, args.ws_port)
        
        # Create FastAPI app
        app = create_app(config)
        
        # Store WebSocket server in app state for API access
        app.state.websocket_server = websocket_server

        # Create and start monitoring service
        monitoring_service = WebSocketMonitoringService(websocket_server)
        app.state.monitoring_service = monitoring_service

        # Create screenshot service
        screenshot_service = ScreenshotService(websocket_server)
        app.state.screenshot_service = screenshot_service
        
        logger.info("🚀 Starting ChatGPT Controller with combined servers")
        logger.info(f"🔌 WebSocket Server: ws://{args.ws_host}:{args.ws_port}")
        logger.info(f"📡 API Server: http://{config.api.host}:{config.api.port}")
        
        # Start WebSocket server
        await websocket_server.start_server()

        # Start monitoring service
        await monitoring_service.start()
        
        # Create uvicorn server
        uvicorn_config = uvicorn.Config(
            app,
            host=config.api.host,
            port=config.api.port,
            log_level=config.api.log_level,
            reload=False,  # Disable reload in combined mode
            workers=1,
        )
        
        api_server = uvicorn.Server(uvicorn_config)
        
        # Run both servers concurrently
        try:
            # Start API server in background
            api_task = asyncio.create_task(api_server.serve())
            
            logger.success("✅ Both servers started successfully")
            logger.info("Press Ctrl+C to stop...")
            
            # Keep running until interrupted
            while websocket_server.is_running():
                await asyncio.sleep(1)
                
                # Check if API server is still running
                if api_task.done():
                    logger.warning("⚠️ API server stopped unexpectedly")
                    break
            
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested...")
        finally:
            # Cleanup
            logger.info("🧹 Cleaning up servers...")
            
            # Stop monitoring service
            if monitoring_service:
                await monitoring_service.stop()

            # Stop WebSocket server
            if websocket_server.is_running():
                await websocket_server.stop_server()
            
            # Stop API server
            if not api_task.done():
                api_task.cancel()
                try:
                    await api_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("✅ Cleanup completed")
        
    except Exception as e:
        logger.error(f"❌ Error running combined servers: {e}")
        return 1


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="ChatGPT Controller API Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )
    
    # Server mode
    parser.add_argument(
        "--mode",
        choices=["api-only", "combined"],
        default="combined",
        help="Server mode: api-only or combined (default: combined)"
    )
    
    # WebSocket server options
    parser.add_argument("--ws-host", default="localhost", help="WebSocket server host")
    parser.add_argument("--ws-port", type=int, default=8765, help="WebSocket server port")
    
    # API server options
    parser.add_argument("--api-host", default="localhost", help="API server host")
    parser.add_argument("--api-port", type=int, default=8000, help="API server port")
    
    # General options
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--config", type=Path, help="Configuration file path")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload (API only mode)")
    
    return parser.parse_args()


async def main():
    """Main application entry point"""
    args = parse_arguments()
    
    # Load configuration
    if args.config:
        from chatgpt_controller.core.config import Config
        config = Config(args.config)
    else:
        config = get_config()
    
    # Override config with command line arguments
    if args.api_host:
        config.api.host = args.api_host
    if args.api_port:
        config.api.port = args.api_port
    if args.reload:
        config.api.reload = True
    
    # Setup logging
    log_level = "DEBUG" if args.debug else config.logging.level
    setup_logging(log_level)
    
    logger.info("ChatGPT Controller API Server")
    logger.info("=" * 40)
    logger.info(f"Mode: {args.mode}")
    
    try:
        if args.mode == "api-only":
            return await run_api_server_only(args)
        else:
            return await run_combined_servers(args)
            
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted by user")
        return 0
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
