# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Screenshots (if not needed in container)
screenshots/

# Development files
.pytest_cache/
.coverage
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Git
.git/
.gitignore

# Database files (if you want to exclude them)
# *.db
# *.sqlite
# *.sqlite3

# Config files that shouldn't be in container
# config.json
