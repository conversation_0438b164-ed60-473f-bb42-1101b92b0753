#!/bin/bash

# Icon Generator Script for ChatGPT Forward Plugin
# This script provides easy access to the Python icon generator

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Python is available
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        print_error "Python is not installed or not in PATH"
        exit 1
    fi
}

# Check if PIL/Pillow is available
check_pillow() {
    if ! $PYTHON_CMD -c "from PIL import Image" &> /dev/null; then
        print_warning "Pillow (PIL) is not installed"
        print_info "Installing Pillow..."
        
        if $PYTHON_CMD -m pip install Pillow; then
            print_success "Pillow installed successfully"
        else
            print_error "Failed to install Pillow"
            print_info "Please install manually: pip install Pillow"
            exit 1
        fi
    fi
}

# Main function
main() {
    print_info "ChatGPT Forward Plugin - Icon Generator"
    echo "========================================"
    
    # Check dependencies
    check_python
    check_pillow
    
    # Run the icon generator with passed arguments
    print_info "Running icon generator..."
    
    if [ $# -eq 0 ]; then
        # No arguments, generate all icons
        print_info "No arguments provided, generating all icons..."
        $PYTHON_CMD generate_icons.py
    else
        # Pass all arguments to the Python script
        $PYTHON_CMD generate_icons.py "$@"
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Icon generation completed successfully!"
        print_info "Icons are saved in public/icons/"
        
        # Rebuild the extension if npm is available
        if command -v npm &> /dev/null && [ -f "package.json" ]; then
            print_info "Rebuilding extension..."
            if npm run build; then
                print_success "Extension rebuilt successfully!"
                print_info "You can now load the extension from the dist/ folder"
            else
                print_warning "Extension rebuild failed, but icons were generated"
            fi
        else
            print_info "To use the new icons, rebuild the extension with: npm run build"
        fi
    else
        print_error "Icon generation failed"
        exit 1
    fi
}

# Help function
show_help() {
    echo "ChatGPT Forward Plugin - Icon Generator"
    echo "========================================"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h              Show this help message"
    echo "  --states STATE...       Generate specific states (connected, disconnected, etc.)"
    echo "  --sizes SIZE...         Generate specific sizes (16, 48, 128)"
    echo "  --text TEXT             Custom text to display (default: GPTF)"
    echo "  --output DIR            Output directory (default: public/icons)"
    echo "  --list-states           List available states"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Generate all icons"
    echo "  $0 --states connected error          # Generate only connected and error icons"
    echo "  $0 --text 'GPT' --sizes 16 48        # Custom text and sizes"
    echo "  $0 --list-states                     # Show available states"
    echo ""
}

# Check for help flag
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    show_help
    exit 0
fi

# Run main function
main "$@"
