# 网络数据转发插件

一个功能强大的Chrome扩展，专门用于拦截和转发ChatGPT网络请求数据，并支持远程元素查询和操作。

## 🚀 功能特性

- 🔗 **WebSocket连接管理**: 支持连接到自定义WebSocket服务器
- 📡 **增强网络数据拦截**: 多层次拦截ChatGPT的网络请求和响应，绕过防御措施
- 🔄 **实时数据转发**: 将拦截到的数据实时转发到WebSocket服务器
- 🎯 **智能过滤**: 只拦截ChatGPT相关的网络请求
- 📊 **状态监控**: 实时显示连接状态和数据传输情况
- 🛠️ **易于配置**: 简单的弹窗界面配置WebSocket服务器地址
- 🔍 **元素查询**: 支持WebSocket服务器下发指令获取页面元素信息
- 🎮 **元素操作**: 支持点击、输入、滚动等基本元素操作
- 🕵️ **隐蔽模式**: 自动检测并绕过网站防御措施
- 🌐 **多种选择器**: 支持XPath、CSS、ID等多种元素选择器
- 🚇 **代理隧道**: 支持通过代理和隧道技术进一步隐藏行为

## 项目结构

```
src/
├── assets/           # 静态资源
├── components/       # 共享组件
│   └── App.tsx       # 主应用组件
├── constants/        # 常量定义
├── hooks/            # 自定义React Hooks
├── pages/            # 页面组件
│   ├── Debug/        # 调试页面
│   ├── Options/      # 选项页面
│   └── Popup/        # 弹出页面
├── services/         # 服务
│   ├── chatgpt/      # ChatGPT相关服务
│   │   └── index.ts  # ChatGPT工具函数
│   ├── element-query/ # 元素查询服务
│   │   ├── index.ts  # 主服务入口
│   │   ├── selector-engine.ts # 选择器引擎
│   │   ├── element-extractor.ts # 元素信息提取器
│   │   └── element-actions.ts # 元素操作器
│   ├── message-handler/ # 消息处理服务
│   ├── simulation/   # 页面信息和元素定位服务
│   └── websocket/    # WebSocket相关服务
├── types/            # 类型定义
│   ├── chatgpt.ts    # ChatGPT相关类型
│   ├── chrome.d.ts   # Chrome API类型
│   ├── element-query.ts # 元素查询相关类型
│   └── websocket.ts  # WebSocket相关类型
└── utils/            # 工具函数
```

## 主要功能

### 🌐 网络拦截
- **Extension API拦截**: 使用Chrome Extension WebRequest API进行底层网络拦截
- **智能过滤**: 只拦截ChatGPT相关的网络请求
- **实时转发**: 将拦截到的数据实时转发到WebSocket服务器

### 🔍 元素查询系统
- **多种选择器**: 支持XPath、CSS、ID、类名、标签、文本、属性选择器
- **元素信息**: 获取位置、尺寸、样式、属性等详细信息
- **批量查询**: 支持一次查询多个元素
- **实时监听**: 监听元素事件变化

### 🎮 元素操作功能
- **基本操作**: 点击、输入、滚动、悬停、聚焦等
- **高级操作**: 拖拽、键盘模拟、属性获取等
- **智能等待**: 等待元素可见、状态改变等
- **操作验证**: 验证操作结果和状态

### 📡 数据转发
- **实时转发**: 将拦截到的数据实时转发到WebSocket服务器
- **格式化数据**: 结构化的JSON数据格式
- **状态监控**: 实时显示连接状态和数据传输情况
- **错误处理**: 完善的错误处理和重连机制

## 页面说明

- **弹出页面 (Popup)**: 提供快速连接和断开WebSocket服务器的界面
- **选项页面 (Options)**: 提供详细的配置选项
- **调试页面 (Debug)**: 提供调试工具和测试功能

## 服务说明

- **websocket**: 处理WebSocket连接和通信
- **chatgpt**: 提供增强的网络请求拦截功能
- **element-query**: 提供元素查询和操作功能
- **message-handler**: 处理数据转发消息

## 📋 使用指南

### 基本使用

1. **安装扩展**
   ```bash
   pnpm install
   pnpm build:extension
   ```

2. **加载到Chrome**
   - 打开Chrome扩展管理页面 (`chrome://extensions/`)
   - 启用开发者模式
   - 点击"加载已解压的扩展程序"
   - 选择项目的 `dist` 文件夹

3. **连接WebSocket服务器**
   - 点击扩展图标打开弹窗
   - 输入WebSocket服务器地址 (例如: `ws://localhost:8765`)
   - 点击"连接"按钮

4. **访问ChatGPT**
   - 打开 [ChatGPT网站](https://chatgpt.com)
   - 扩展会自动开始拦截网络请求
   - 查看WebSocket服务器接收到的数据

### 元素查询使用

#### 发送查询指令
```javascript
// 通过WebSocket发送元素查询指令
const command = {
  type: 'ELEMENT_QUERY',
  id: 'unique-id',
  timestamp: new Date().toISOString(),
  selector: {
    type: 'css',
    value: '#submit-button',
    options: { waitVisible: true, timeout: 5000 }
  },
  actions: [{
    type: 'click',
    params: { delay: 500 }
  }]
};

websocket.send(JSON.stringify(command));
```

#### 接收查询结果
```javascript
websocket.onmessage = function(event) {
  const result = JSON.parse(event.data);
  if (result.type === 'ELEMENT_QUERY_RESPONSE') {
    console.log('找到元素:', result.data.elements);
  }
};
```

### 测试功能

1. **网络拦截测试**
   - 打开 `test-enhanced-intercept.html`
   - 测试各种拦截功能和绕过技术

2. **元素查询测试**
   - 打开 `test-element-query.html`
   - 测试元素查询和操作功能

## 📚 详细文档

- [增强绕过功能指南](ENHANCED_BYPASS_GUIDE.md)
- [元素查询功能指南](ELEMENT_QUERY_GUIDE.md)

## 开发

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm dev
```

### 构建扩展

```bash
pnpm build:extension
```

## 使用方法

1. 安装扩展
2. 点击扩展图标，输入WebSocket服务器地址
3. 点击"连接"按钮
4. 打开目标网页
5. 插件将自动拦截并转发所有网络请求数据

## 核心功能

### 网络请求拦截
- 使用Chrome Extension WebRequest API拦截网络请求
- 自动检测ChatGPT相关请求
- 捕获请求和响应的完整数据

### 数据转发
- 将所有网络数据转发到WebSocket服务器
- 支持JSON数据的完整转发
- 包含请求时间、状态码等元信息

### 连接管理
- 简单的WebSocket连接管理
- 自动重连机制
- 连接状态实时显示
