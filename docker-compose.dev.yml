services:
  chatgpt-controller:
    build:
      context: ./controller
      dockerfile: Dockerfile
      target: backend-builder  # Use builder stage for development
    environment:
      - LOG_LEVEL=DEBUG
      - PYTHONUNBUFFERED=1
      - RELOAD=true
    volumes:
      # Mount source code for live development
      - ./controller:/app
      # Exclude virtual environment to avoid conflicts
      - /app/.venv
    command: ["python", "api_server.py", "--mode", "combined", "--ws-host", "0.0.0.0", "--api-host", "0.0.0.0", "--debug", "--reload"]
    ports:
      - "8000:8000"
      - "8765:8765"
    networks:
      - chatgpt-network

networks:
  chatgpt-network:
    driver: bridge
