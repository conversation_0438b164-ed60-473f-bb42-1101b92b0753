# ChatGPT Controller Environment Configuration
# Copy this file to .env and modify as needed

# Server Configuration
API_HOST=0.0.0.0
WS_HOST=0.0.0.0
API_PORT=8000
WS_PORT=8765

# Logging
LOG_LEVEL=INFO
# Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

# Development Settings
RELOAD=false
DEBUG=false

# Database (if using external database)
# DATABASE_URL=sqlite:///app/data/chatgpt_controller.db
# DATABASE_URL=postgresql://user:password@localhost:5432/chatgpt_controller

# Security (for production)
# SECRET_KEY=your-secret-key-here
# ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# WebSocket Configuration
WS_PING_INTERVAL=20
WS_PING_TIMEOUT=10
WS_MAX_CLIENTS=10
WS_MAX_MESSAGE_SIZE=1048576

# API Configuration
API_WORKERS=1
API_MAX_REQUESTS=1000

# SSL/TLS (for production)
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Monitoring
ENABLE_METRICS=false
METRICS_PORT=9090

# External Services
# REDIS_URL=redis://localhost:6379/0
# SENTRY_DSN=your-sentry-dsn-here
