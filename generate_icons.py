#!/usr/bin/env python3
"""
Plugin Icon Generator for ChatGPT Forward Plugin
Generates icons with "GPTF" text for different connection states.
"""

import os
from PIL import Image, ImageDraw, ImageFont
import argparse

# Icon configurations
ICON_SIZES = [16, 48, 128]
OUTPUT_DIR = "public/icons"

# Color schemes for different states
ICON_STATES = {
    "default": {
        "bg_color": "#2563eb",  # Blue
        "text_color": "#ffffff",  # White
        "border_color": "#1d4ed8",  # Darker blue
        "description": "Default state"
    },
    "connected": {
        "bg_color": "#16a34a",  # Green
        "text_color": "#ffffff",  # White
        "border_color": "#15803d",  # Darker green
        "description": "Connected to WebSocket"
    },
    "disconnected": {
        "bg_color": "#6b7280",  # Gray
        "text_color": "#ffffff",  # White
        "border_color": "#4b5563",  # Darker gray
        "description": "Disconnected from WebSocket"
    },
    "connecting": {
        "bg_color": "#f59e0b",  # Orange/Yellow
        "text_color": "#ffffff",  # White
        "border_color": "#d97706",  # Darker orange
        "description": "Connecting to WebSocket"
    },
    "error": {
        "bg_color": "#dc2626",  # Red
        "text_color": "#ffffff",  # White
        "border_color": "#b91c1c",  # Darker red
        "description": "Connection error"
    }
}

def create_icon(size, bg_color, text_color, border_color, text="GPTF"):
    """
    Create a single icon with the specified parameters.
    
    Args:
        size (int): Icon size in pixels (square)
        bg_color (str): Background color in hex format
        text_color (str): Text color in hex format
        border_color (str): Border color in hex format
        text (str): Text to display on the icon
    
    Returns:
        PIL.Image: Generated icon image
    """
    # Create a new image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Calculate dimensions
    border_width = max(1, size // 32)  # Responsive border width
    corner_radius = size // 8  # Rounded corners
    
    # Draw rounded rectangle background
    draw.rounded_rectangle(
        [border_width, border_width, size - border_width, size - border_width],
        radius=corner_radius,
        fill=bg_color,
        outline=border_color,
        width=border_width
    )
    
    # Calculate font size based on icon size
    font_size = size // 4
    
    # Try to load a system font, fallback to default
    try:
        # Try different font paths for different systems
        font_paths = [
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux
            "C:/Windows/Fonts/arial.ttf",  # Windows
            "/usr/share/fonts/TTF/arial.ttf",  # Some Linux distributions
        ]
        
        font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                font = ImageFont.truetype(font_path, font_size)
                break
        
        if font is None:
            # Fallback to default font
            font = ImageFont.load_default()
    except Exception:
        # Ultimate fallback
        font = ImageFont.load_default()
    
    # Get text bounding box
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Calculate text position (centered)
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2
    
    # Draw text with slight shadow for better visibility
    shadow_offset = max(1, size // 64)
    if shadow_offset > 0:
        # Draw shadow
        draw.text(
            (text_x + shadow_offset, text_y + shadow_offset),
            text,
            font=font,
            fill=(0, 0, 0, 128)  # Semi-transparent black shadow
        )
    
    # Draw main text
    draw.text((text_x, text_y), text, font=font, fill=text_color)
    
    return img

def generate_icons(output_dir=OUTPUT_DIR, states=None, icon_sizes=None):
    """
    Generate all icon variants for the plugin.

    Args:
        output_dir (str): Directory to save the generated icons
        states (list): List of states to generate. If None, generates all states.
        icon_sizes (list): List of icon sizes to generate. If None, uses default sizes.
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Use all states if none specified
    if states is None:
        states = list(ICON_STATES.keys())

    # Use default sizes if none specified
    if icon_sizes is None:
        icon_sizes = ICON_SIZES

    print(f"Generating icons in {output_dir}/")
    print(f"States: {', '.join(states)}")
    print(f"Sizes: {', '.join(map(str, icon_sizes))}")
    print("-" * 50)
    
    for state in states:
        if state not in ICON_STATES:
            print(f"Warning: Unknown state '{state}', skipping...")
            continue
            
        config = ICON_STATES[state]
        print(f"Generating {state} icons ({config['description']})...")
        
        for size in icon_sizes:
            # Create the icon
            icon = create_icon(
                size=size,
                bg_color=config["bg_color"],
                text_color=config["text_color"],
                border_color=config["border_color"]
            )
            
            # Determine filename
            if state == "default":
                filename = f"icon{size}.png"
            else:
                filename = f"icon{size}-{state}.png"
            
            filepath = os.path.join(output_dir, filename)
            
            # Save the icon
            icon.save(filepath, "PNG")
            print(f"  ✓ {filename} ({size}x{size})")
    
    print("-" * 50)
    print("Icon generation complete!")

def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Generate plugin icons for ChatGPT Forward Plugin"
    )
    parser.add_argument(
        "--output", "-o",
        default=OUTPUT_DIR,
        help=f"Output directory (default: {OUTPUT_DIR})"
    )
    parser.add_argument(
        "--states", "-s",
        nargs="+",
        choices=list(ICON_STATES.keys()),
        help="States to generate (default: all states)"
    )
    parser.add_argument(
        "--sizes",
        nargs="+",
        type=int,
        default=ICON_SIZES,
        help=f"Icon sizes to generate (default: {ICON_SIZES})"
    )
    parser.add_argument(
        "--text", "-t",
        default="GPTF",
        help="Text to display on icons (default: GPTF)"
    )
    parser.add_argument(
        "--list-states",
        action="store_true",
        help="List available states and exit"
    )
    
    args = parser.parse_args()
    
    if args.list_states:
        print("Available icon states:")
        for state, config in ICON_STATES.items():
            print(f"  {state:12} - {config['description']}")
        return
    
    # Update sizes if specified
    if args.sizes != ICON_SIZES:
        icon_sizes = args.sizes
    else:
        icon_sizes = ICON_SIZES
    
    # Generate icons
    generate_icons(output_dir=args.output, states=args.states, icon_sizes=icon_sizes)

if __name__ == "__main__":
    main()
