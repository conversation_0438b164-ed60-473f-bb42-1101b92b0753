#!/bin/bash

# Generate Self-Signed SSL Certificate for IP-based Server
# This script creates SSL certificates that work with IP addresses

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
SSL_DIR="nginx/ssl"
CERT_DAYS=365
SERVER_IP=""
FORCE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            SERVER_IP="$2"
            shift 2
            ;;
        --days)
            CERT_DAYS="$2"
            shift 2
            ;;
        --ssl-dir)
            SSL_DIR="$2"
            shift 2
            ;;
        --force)
            FORCE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --ip IP_ADDRESS         Server IP address (auto-detected if not provided)"
            echo "  --days DAYS            Certificate validity in days (default: 365)"
            echo "  --ssl-dir DIR          SSL directory path (default: nginx/ssl)"
            echo "  --force                Force regenerate even if certificates exist"
            echo "  -h, --help             Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                     # Auto-detect IP and generate certificate"
            echo "  $0 --ip *************  # Use specific IP address"
            echo "  $0 --force             # Force regenerate certificates"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_status "Generating self-signed SSL certificate for IP-based server..."

# Create SSL directory if it doesn't exist
mkdir -p "$SSL_DIR"

# Check if certificates already exist
if [ -f "$SSL_DIR/cert.pem" ] && [ -f "$SSL_DIR/key.pem" ] && [ "$FORCE" = false ]; then
    print_warning "SSL certificates already exist in $SSL_DIR"
    print_status "Use --force to regenerate or remove existing certificates"
    
    # Show certificate info
    if command -v openssl >/dev/null 2>&1; then
        print_status "Current certificate information:"
        openssl x509 -in "$SSL_DIR/cert.pem" -text -noout | grep -E "(Subject:|Not After|DNS:|IP Address:)" || true
    fi
    exit 0
fi

# Auto-detect server IP if not provided
if [ -z "$SERVER_IP" ]; then
    print_status "Auto-detecting server IP address..."
    
    # Try to get public IP first
    SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "")
    
    if [ -z "$SERVER_IP" ]; then
        # Fall back to local IP
        SERVER_IP=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' || echo "127.0.0.1")
    fi
    
    print_status "Detected IP address: $SERVER_IP"
fi

# Validate IP address format
if ! [[ $SERVER_IP =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
    print_error "Invalid IP address format: $SERVER_IP"
    exit 1
fi

# Check if openssl is available
if ! command -v openssl >/dev/null 2>&1; then
    print_error "OpenSSL is not installed. Please install it first."
    exit 1
fi

print_status "Generating SSL certificate for IP: $SERVER_IP"
print_status "Certificate validity: $CERT_DAYS days"
print_status "SSL directory: $SSL_DIR"

# Create OpenSSL configuration file for IP-based certificate
cat > "$SSL_DIR/openssl.conf" << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = State
L = City
O = ChatGPT Controller
OU = IT Department
CN = $SERVER_IP

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
IP.1 = $SERVER_IP
IP.2 = 127.0.0.1
IP.3 = ::1
DNS.1 = localhost
EOF

# Generate private key
print_status "Generating private key..."
openssl genrsa -out "$SSL_DIR/key.pem" 2048

# Generate certificate signing request
print_status "Generating certificate signing request..."
openssl req -new -key "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.csr" -config "$SSL_DIR/openssl.conf"

# Generate self-signed certificate
print_status "Generating self-signed certificate..."
openssl x509 -req -in "$SSL_DIR/cert.csr" -signkey "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" \
    -days "$CERT_DAYS" -extensions v3_req -extfile "$SSL_DIR/openssl.conf"

# Set proper permissions
chmod 600 "$SSL_DIR/key.pem"
chmod 644 "$SSL_DIR/cert.pem"

# Clean up temporary files
rm -f "$SSL_DIR/cert.csr" "$SSL_DIR/openssl.conf"

print_success "SSL certificate generated successfully!"
print_status "Certificate files:"
echo "  - Certificate: $SSL_DIR/cert.pem"
echo "  - Private Key: $SSL_DIR/key.pem"

# Show certificate information
print_status "Certificate information:"
openssl x509 -in "$SSL_DIR/cert.pem" -text -noout | grep -E "(Subject:|Not After|DNS:|IP Address:)"

print_warning "Note: This is a self-signed certificate."
print_warning "Browsers will show a security warning when accessing HTTPS."
print_warning "Users need to accept the certificate to proceed."

print_status "Access your server at:"
echo "  - HTTP:  http://$SERVER_IP"
echo "  - HTTPS: https://$SERVER_IP (accept certificate warning)"

if [ "$SERVER_IP" != "127.0.0.1" ]; then
    echo "  - Local HTTP:  http://127.0.0.1"
    echo "  - Local HTTPS: https://127.0.0.1 (accept certificate warning)"
fi
