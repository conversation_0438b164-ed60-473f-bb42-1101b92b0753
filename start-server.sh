#!/bin/bash

# ChatGPT Controller Server Startup Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Parse command line arguments
MODE="production"
PROFILE=""
BUILD=false
LOGS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dev|--development)
            MODE="development"
            shift
            ;;
        -p|--prod|--production)
            MODE="production"
            shift
            ;;
        --build)
            BUILD=true
            shift
            ;;
        --logs)
            LOGS=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -d, --dev, --development    Start in development mode"
            echo "  -p, --prod, --production    Start in production mode (default)"
            echo "  --build                     Force rebuild of containers"
            echo "  --logs                      Show logs after starting"
            echo "  -h, --help                  Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                          # Start in production mode"
            echo "  $0 --dev                    # Start in development mode"
            echo "  $0 --prod --build           # Production mode with rebuild"
            echo "  $0 --dev --logs             # Development mode and show logs"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_status "Starting ChatGPT Controller Server in $MODE mode..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose >/dev/null 2>&1; then
    print_error "docker-compose is not installed. Please install it and try again."
    exit 1
fi

# Check if required ports are available
if ! check_port 8000; then
    print_warning "Port 8000 is already in use. The HTTP API server may not start properly."
fi

if ! check_port 8765; then
    print_warning "Port 8765 is already in use. The WebSocket server may not start properly."
fi

# Set up compose files based on mode
COMPOSE_FILES="-f docker-compose.yml"

if [ "$MODE" = "development" ]; then
    COMPOSE_FILES="$COMPOSE_FILES -f docker-compose.dev.yml"
    print_status "Using development configuration"
elif [ "$MODE" = "production" ]; then
    PROFILE="--profile production"
    print_status "Using production configuration with nginx reverse proxy"
fi

# Build containers if requested
if [ "$BUILD" = true ]; then
    print_status "Building containers..."
    docker-compose $COMPOSE_FILES build
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p controller/data controller/logs nginx/ssl

# Generate SSL certificate if not exists (for HTTPS support)
if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/key.pem" ]; then
    print_status "SSL certificates not found. Generating self-signed certificate..."
    if [ -f "./generate-ssl-cert.sh" ]; then
        ./generate-ssl-cert.sh
    else
        print_warning "generate-ssl-cert.sh not found. Creating basic self-signed certificate..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=CN/ST=State/L=City/O=ChatGPT Controller/CN=localhost" \
            -addext "subjectAltName=IP:127.0.0.1,IP:::1,DNS:localhost" 2>/dev/null || \
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=CN/ST=State/L=City/O=ChatGPT Controller/CN=localhost"
        chmod 600 nginx/ssl/key.pem
        chmod 644 nginx/ssl/cert.pem
        print_success "Basic SSL certificate generated"
    fi
else
    print_status "SSL certificates found"
fi

# Start services
print_status "Starting services..."
if [ "$MODE" = "production" ]; then
    docker-compose $COMPOSE_FILES $PROFILE up -d
else
    docker-compose $COMPOSE_FILES up -d
fi

# Wait for services to be ready
print_status "Checking service health..."

# Wait for the main controller service
if wait_for_service "http://localhost:8000/health" "ChatGPT Controller API"; then
    print_success "HTTP API server is running on http://localhost:8000"
else
    print_error "Failed to start HTTP API server"
    docker-compose $COMPOSE_FILES logs chatgpt-controller
    exit 1
fi

# Test WebSocket connection (basic check)
if nc -z localhost 8765 2>/dev/null; then
    print_success "WebSocket server is running on ws://localhost:8765"
else
    print_warning "WebSocket server may not be ready yet"
fi

# Show service status
print_status "Service status:"
docker-compose $COMPOSE_FILES ps

# Show logs if requested
if [ "$LOGS" = true ]; then
    print_status "Showing logs (Ctrl+C to exit):"
    docker-compose $COMPOSE_FILES logs -f
fi

print_success "ChatGPT Controller Server started successfully!"
print_status "Access points:"
echo "  - HTTP API: http://localhost:8000"
echo "  - WebSocket: ws://localhost:8765"
echo "  - Health Check: http://localhost:8000/health"

if [ "$MODE" = "production" ]; then
    echo "  - Nginx Proxy: http://localhost:80 (HTTPS: 443 if SSL configured)"
fi

print_status "To view logs: docker-compose $COMPOSE_FILES logs -f"
print_status "To stop: docker-compose $COMPOSE_FILES down"
