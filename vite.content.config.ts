import { defineConfig } from 'vite'
import { resolve } from 'path'
import { fileURLToPath } from 'url'
const __dirname = fileURLToPath(new URL('.', import.meta.url))

// Separate config for content script to avoid module imports
export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    outDir: 'dist',
    emptyOutDir: false, // Don't empty since we're building separately
    // minify: false,
    rollupOptions: {
      input: resolve(__dirname, 'src/content.ts'),
      output: {
        dir: 'dist',
        entryFileNames: 'content.js',
        format: 'iife',
        name: 'ContentScript',
        inlineDynamicImports: true
      }
    }
  }
})
