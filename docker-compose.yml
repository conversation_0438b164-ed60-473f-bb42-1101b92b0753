services:
  chatgpt-controller:
    build:
      context: ./controller
      dockerfile: Dockerfile
    container_name: chatgpt-controller
    restart: unless-stopped
    ports:
      # HTTP API Server
      - "8000:8000"
      # WebSocket Server
      - "8765:8765"
    environment:
      # Server configuration
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
      # Optional: Override default hosts to bind to all interfaces
      - API_HOST=0.0.0.0
      - WS_HOST=0.0.0.0
      # Optional: Set log level
      - LOG_LEVEL=INFO
      - COLUMNS=128
    volumes:
      # Persist data directory
      - ./controller/data:/app/data
      # Persist logs
      - ./controller/logs:/app/logs
      # Optional: Mount config file for easy editing
      - ./controller/config.json:/app/config.json:ro
    networks:
      - chatgpt-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Nginx reverse proxy - supports both HTTP and HTTPS
  nginx:
    image: nginx:alpine
    container_name: chatgpt-nginx
    restart: unless-stopped
    ports:
      - "80:80"    # HTTP access
      - "443:443"  # HTTPS access (with self-signed certificate)
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - chatgpt-controller
    networks:
      - chatgpt-network
    profiles:
      - production
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  chatgpt-network:
    driver: bridge

volumes:
  controller-data:
  controller-logs:
