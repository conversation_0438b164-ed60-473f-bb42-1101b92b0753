/**
 * 插件API测试文件
 * 
 * 用于验证重构后的接口定义和功能
 */

import { 
  isBrowserControlCommand,
  isElementCommand,
  createResponse,
  generateId,
  MessageType
} from '@/types/plugin-api';

// 测试浏览器控制指令识别
function testBrowserControlCommand() {
  console.log('🧪 测试浏览器控制指令识别...');

  const openChatGPTCommand = {
    type: MessageType.OPEN_CHATGPT,
    id: generateId('test'),
    timestamp: new Date().toISOString(),
    data: { url: 'https://chatgpt.com' }
  };

  const setWindowSizeCommand = {
    type: MessageType.SET_WINDOW_SIZE,
    id: generateId('test'),
    timestamp: new Date().toISOString(),
    data: { width: 1920, height: 1080 }
  };

  const takeScreenshotCommand = {
    type: MessageType.TAKE_SCREENSHOT,
    id: generateId('test'),
    timestamp: new Date().toISOString(),
    data: { format: 'png' as const }
  };

  const elementQueryCommand = {
    type: MessageType.ELEMENT_QUERY,
    id: generateId('test'),
    timestamp: new Date().toISOString(),
    selector: {
      type: 'css' as const,
      value: '.test-selector'
    }
  };

  // 测试浏览器控制指令识别
  console.assert(isBrowserControlCommand(openChatGPTCommand), '应该识别为浏览器控制指令');
  console.assert(isBrowserControlCommand(setWindowSizeCommand), '应该识别为浏览器控制指令');
  console.assert(isBrowserControlCommand(takeScreenshotCommand), '应该识别为浏览器控制指令');
  console.assert(!isBrowserControlCommand(elementQueryCommand), '不应该识别为浏览器控制指令');

  // 测试元素查询指令识别
  console.assert(!isElementCommand(openChatGPTCommand), '不应该识别为元素查询指令');
  console.assert(isElementCommand(elementQueryCommand), '应该识别为元素查询指令');

  console.log('✅ 浏览器控制指令识别测试通过');
}

// 测试响应创建
function testResponseCreation() {
  console.log('🧪 测试响应创建...');

  const openChatGPTCommand = {
    type: MessageType.OPEN_CHATGPT,
    id: generateId('test'),
    timestamp: new Date().toISOString(),
    data: { url: 'https://chatgpt.com' }
  } as const;

  try {
    const response = createResponse(openChatGPTCommand as any, true, { tabId: 123 });
    console.assert(response.type === MessageType.BROWSER_CONTROL_RESPONSE, '响应类型应该正确');
    console.assert(response.id === openChatGPTCommand.id, '响应ID应该匹配');

    // 类型断言以访问BrowserControlResponse的属性
    const browserResponse = response as any;
    console.assert(browserResponse.success === true, '响应状态应该正确');
    console.assert(browserResponse.data?.tabId === 123, '响应数据应该正确');

    console.log('✅ 响应创建测试通过');
  } catch (error) {
    console.error('❌ 响应创建测试失败:', error);
  }
}

// 测试ID生成
function testIdGeneration() {
  console.log('🧪 测试ID生成...');

  const id1 = generateId();
  const id2 = generateId('custom');
  const id3 = generateId();

  console.assert(id1 !== id2, 'ID应该是唯一的');
  console.assert(id1 !== id3, 'ID应该是唯一的');
  console.assert(id2 !== id3, 'ID应该是唯一的');
  console.assert(id1.startsWith('msg-'), '默认前缀应该正确');
  console.assert(id2.startsWith('custom-'), '自定义前缀应该正确');

  console.log('✅ ID生成测试通过');
}

// 运行所有测试
export function runPluginApiTests() {
  console.log('🚀 开始运行插件API测试...');
  
  try {
    testBrowserControlCommand();
    testResponseCreation();
    testIdGeneration();
    
    console.log('🎉 所有插件API测试通过！');
  } catch (error) {
    console.error('❌ 插件API测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  runPluginApiTests();
}
