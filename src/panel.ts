/**
 * DevTools panel script for network request/response interception
 * This script runs in the DevTools panel and intercepts network requests
 */

console.log('ChatGPT Forward DevTools panel script loaded');

// Inline the necessary constants and functions to avoid module imports
const MONITORED_API_ENDPOINTS = {
  CHATGPT_CONVERSATION: {
    pattern: '/backend-api/conversation',
    description: 'ChatGPT conversation API'
  },
  CHATGPT_USER_INFO: {
    pattern: '/backend-api/me',
    description: 'ChatGPT user info API'
  },
  CHATGPT_REQUIREMENTS: {
    pattern: '/backend-api/chat_requirements',
    description: 'ChatGPT chat requirements API'
  }
};

const MonitoredAPIMessageType = {
  MONITORED_API_RESPONSE_DATA: 'MONITORED_API_RESPONSE_DATA'
};

// Inline utility functions
function isMonitoredAPIRequest(url: string): boolean {
  return Object.values(MONITORED_API_ENDPOINTS).some(endpoint =>
    url.includes(endpoint.pattern)
  );
}

function getMonitoredAPIType(url: string): string | null {
  for (const [key, endpoint] of Object.entries(MONITORED_API_ENDPOINTS)) {
    if (url.includes(endpoint.pattern)) {
      return key.toLowerCase().replace('chatgpt_', '');
    }
  }
  return null;
}

// Status element
let statusElement: HTMLElement | null = null;

// Initialize the panel
function initializePanel(): void {
  console.log('🔧 Initializing DevTools panel...');

  statusElement = document.getElementById('status');
  if (statusElement) {
    console.log('✅ Status element found');
    updateStatus('active', 'DevTools Network Interceptor - Active');
  } else {
    console.error('❌ Status element not found');
  }

  console.log('✅ DevTools panel initialized successfully');
}

// Update status display
function updateStatus(type: 'active' | 'inactive', message: string): void {
  if (statusElement) {
    statusElement.className = `status ${type}`;
    statusElement.textContent = message;
  }
}

// Create request metadata
function createRequestMetadata(_url: string): any {
  return {
    endpoint_description: 'DevTools intercepted request',
    captured_at: new Date().toISOString(),
    source: 'chrome_devtools_api'
  };
}

// Handle network request finished event
function handleRequestFinished(request: chrome.devtools.network.Request): void {
  // Only process monitored API requests
  if (!isMonitoredAPIRequest(request.request.url)) {
    return;
  }

  const apiType = getMonitoredAPIType(request.request.url);
  console.log(`🔍 [DevTools] Intercepted API request [${apiType}]:`, request.request.method, request.request.url);

  // Get the response content
  request.getContent((content: string, encoding: string) => {
    try {
      // Parse response content
      let responseData: any = null;
      if (content) {
        try {
          responseData = JSON.parse(content);
        } catch (e) {
          // If not JSON, store as raw text
          responseData = { raw_text: content, encoding };
        }
      }

      // Create the response data message
      const responseMessage = {
        type: MonitoredAPIMessageType.MONITORED_API_RESPONSE_DATA,
        id: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        data: {
          api_type: apiType,
          api_endpoint: request.request.url,
          request: {
            url: request.request.url,
            method: request.request.method,
            requestId: `devtools-${Date.now()}`,
            headers: request.request.headers
          },
          response: {
            url: request.request.url,
            statusCode: request.response.status,
            statusText: request.response.statusText,
            headers: request.response.headers,
            content: responseData,
            contentLength: content ? content.length : 0,
            mimeType: request.response.content?.mimeType || 'unknown',
            encoding: encoding || 'utf-8'
          },
          timing: {
            startTime: request.startedDateTime,
            endTime: new Date().toISOString(),
            duration: request.time
          },
          metadata: createRequestMetadata(request.request.url)
        }
      };

      // Send to background script for forwarding to WebSocket server
      chrome.runtime.sendMessage({
        type: 'DEVTOOLS_RESPONSE_DATA',
        data: responseMessage
      }).then((response) => {
        if (response?.success) {
          console.log('✅ [DevTools] Response data forwarded successfully');
        } else {
          console.error('❌ [DevTools] Failed to forward response data:', response?.error);
        }
      }).catch((error) => {
        console.error('❌ [DevTools] Error forwarding response data:', error);
      });

    } catch (error) {
      console.error('❌ [DevTools] Error processing response content:', error);
    }
  });
}

// Set up network request listener
function setupNetworkListener(): void {
  console.log('🔧 Setting up DevTools network listener...');

  try {
    // Listen for all network requests
    chrome.devtools.network.onRequestFinished.addListener(handleRequestFinished);
    console.log('✅ DevTools network listener active');
    updateStatus('active', 'DevTools Network Interceptor - Monitoring requests');
  } catch (error) {
    console.error('❌ Failed to setup network listener:', error);
    updateStatus('inactive', 'DevTools Network Interceptor - Error');
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    initializePanel();
    setupNetworkListener();
  });
} else {
  initializePanel();
  setupNetworkListener();
}

export {};
