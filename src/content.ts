// 内容脚本，注入到ChatGPT网页中
import { handleMessage } from '@/services/message-handler';
import { elementQueryService } from '@/services/element-query';
import { getPageInfo } from '@/services/chatgpt';
import { isElementCommand, isElementResponse } from '@/types/plugin-api';
import type { ElementCommand, ElementResult } from '@/types/element-query';

console.log('ChatGPT Forward Plugin content script loaded');

// 全局错误处理器
window.addEventListener('error', (event) => {
  console.error('❌ 全局错误捕获:', event.error);
  console.error('❌ 错误文件:', event.filename);
  console.error('❌ 错误行号:', event.lineno);
  console.error('❌ 错误列号:', event.colno);
  console.error('❌ 错误堆栈:', event.error?.stack);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ 未处理的Promise拒绝:', event.reason);
  console.error('❌ Promise:', event.promise);
  if (event.reason instanceof Error) {
    console.error('❌ 错误堆栈:', event.reason.stack);
  }
});

// 处理元素查询指令
async function handleElementCommand(command: ElementCommand): Promise<ElementResult> {
  const startTime = Date.now();

  try {
    console.log('🔍 开始处理元素查询指令:', command);
    console.log('🔍 指令详细信息:', JSON.stringify(command, null, 2));

    // 详细验证指令结构
    if (!command) {
      throw new Error('Element command is null or undefined');
    }

    if (typeof command !== 'object') {
      throw new Error(`Element command must be an object, got ${typeof command}`);
    }

    if (!command.type) {
      throw new Error('Element command missing required field: type');
    }

    if (!command.id) {
      throw new Error('Element command missing required field: id');
    }

    // 验证命令类型
    const validTypes = ['ELEMENT_QUERY', 'PAGE_INFO', 'ELEMENT_WATCH', 'BATCH_ELEMENT_QUERY', 'ELEMENT_SCREENSHOT'];
    if (!validTypes.includes(command.type)) {
      throw new Error(`Invalid element command type: ${command.type}. Valid types: ${validTypes.join(', ')}`);
    }

    // 特别检查元素查询指令的选择器
    if (command.type === 'ELEMENT_QUERY') {
      const elementQueryCmd = command as any; // 临时类型断言来处理类型问题
      const selector = elementQueryCmd.selector || elementQueryCmd.data?.selector;
      console.log('🔍 元素查询选择器详情:', JSON.stringify(selector, null, 2));

      if (!selector) {
        throw new Error('元素查询指令缺少selector字段');
      }

      if (!selector.type || !selector.value) {
        throw new Error('选择器格式无效：缺少type或value字段');
      }

      console.log(`🔍 选择器验证通过: ${selector.type}="${selector.value}"`);
    }

    console.log(`✅ 指令验证通过，类型: ${command.type}, ID: ${command.id}`);

    const result = await elementQueryService.handleCommand(command);
    console.log('✅ 元素查询服务返回结果:', JSON.stringify(result, null, 2));

    // 特别检查元素查询结果
    if (result.type === 'ELEMENT_QUERY_RESULT') {
      console.log(`🔍 查询结果详情: 成功=${result.success}, 元素数量=${result.elements?.length || 0}`);
      if (result.elements && result.elements.length > 0) {
        result.elements.forEach((element, index) => {
          console.log(`🔍 元素 ${index}:`, {
            selector: element.selector,
            position: element.position,
            isVisible: element.isVisible,
            tagName: element.attributes?.tagName
          });
        });
      }
      if (!result.success && result.error) {
        console.error(`❌ 查询失败: ${result.error}`);
      }
    }

    await sendElementQueryResponse(result);
    return result;
  } catch (error) {
    const executionTime = Date.now() - startTime;

    console.error('❌ 处理元素查询指令失败:', error);

    // 详细的错误信息和堆栈跟踪
    if (error instanceof Error) {
      console.error('❌ 错误名称:', error.name);
      console.error('❌ 错误消息:', error.message);
      console.error('❌ 错误堆栈:', error.stack);

      // 如果有cause属性，也打印出来
      if ('cause' in error && error.cause) {
        console.error('❌ 错误原因:', error.cause);
      }
    } else {
      console.error('❌ 非Error对象错误:', error);
      console.error('❌ 错误类型:', typeof error);
    }

    console.error('❌ 指令详情:', JSON.stringify(command, null, 2));
    console.error('❌ 执行时间:', executionTime, 'ms');

    // 创建错误响应
    const errorResult: ElementResult = {
      type: 'ELEMENT_QUERY_RESULT',
      id: command?.id || 'unknown',
      timestamp: new Date().toISOString(),
      success: false,
      elements: [],
      count: 0,
      error: error instanceof Error ? `${error.name}: ${error.message}` : String(error),
      executionTime
    };

    // 尝试发送错误响应
    try {
      await sendElementQueryResponse(errorResult);
      console.log('✅ 错误响应已发送');
    } catch (sendError) {
      console.error('❌ 发送错误响应失败:', sendError);
      console.error('❌ 发送错误堆栈:', sendError instanceof Error ? sendError.stack : 'No stack trace');
    }

    throw error;
  }
}

// 发送元素查询响应到WebSocket服务器
async function sendElementQueryResponse(result: ElementResult): Promise<void> {
  const responseMessage = {
    success: true,
    type: 'ELEMENT_QUERY_RESPONSE',
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: result
  };

  const success = await sendMessageToServer(responseMessage);
  if (success) {
    console.log('✅ 元素查询结果已发送到WebSocket服务器');
  } else {
    console.error('❌ 发送元素查询结果失败');
  }
}

// WebSocket连接状态
interface ConnectionState {
  connected: boolean;
  state: string;
  url: string;
}

let connectionState: ConnectionState = {
  connected: false,
  state: 'DISCONNECTED',
  url: 'ws://localhost:8765'
};

// 发送消息到WebSocket服务器
function sendMessageToServer(message: any): Promise<boolean> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({
      type: 'WS_SEND_MESSAGE',
      data: message
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('❌ Chrome runtime error:', chrome.runtime.lastError.message);
        resolve(false);
        return;
      }
      resolve(response?.success || false);
    });
  });
}

// 获取WebSocket连接状态
function getConnectionStatus(): Promise<ConnectionState> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({
      type: 'WS_CONNECTION_STATUS'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('❌ Chrome runtime error:', chrome.runtime.lastError.message);
        resolve({connected: false, state: 'DISCONNECTED', url: ''});
        return;
      }
      resolve(response || {connected: false, state: 'DISCONNECTED', url: ''});
    });
  });
}

// 设置网络数据拦截器
async function setupNetworkInterceptor(): Promise<void> {
  console.log('🔧 网络拦截现在由background script的WebRequest API处理');

  // 获取页面信息并发送到WebSocket服务器
  const pageInfo = getPageInfo();
  console.log('🌍 当前页面信息:', pageInfo);

  if (pageInfo.isChatGPT) {
    console.log('✅ 在ChatGPT页面，发送页面信息到WebSocket服务器');
    await sendPageInfo(pageInfo);
  } else {
    console.log('⚠️ 不在ChatGPT页面，网络拦截可能不会生效');
  }
}

// 发送页面信息到WebSocket服务器
async function sendPageInfo(pageInfo: any): Promise<void> {
  const message = {
    type: 'PAGE_INFO',
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: pageInfo
  };

  const success = await sendMessageToServer(message);
  if (success) {
    console.log('✅ 页面信息已发送到WebSocket服务器');
  } else {
    console.log('❌ 发送页面信息失败');
  }
}

// 设置元素事件监听器
function setupElementEventListeners(): void {
  console.log('🔧 设置元素事件监听器...');

  // 监听自定义元素事件
  window.addEventListener('elementEvent', handleElementEvent);
  console.log('✅ 元素事件监听器设置完成');
}

// 处理元素事件
async function handleElementEvent(event: Event): Promise<void> {
  const customEvent = event as CustomEvent;
  console.log('📦 收到元素事件:', customEvent.detail);

  // 检查WebSocket连接状态
  if (!connectionState.connected) {
    console.warn('⚠️ WebSocket未连接，跳过元素事件转发');
    return;
  }

  // 转发元素事件到WebSocket服务器
  const message = {
    type: 'ELEMENT_EVENT_MESSAGE',
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: customEvent.detail
  };

  try {
    const success = await sendMessageToServer(message);
    if (success) {
      console.log('✅ 元素事件转发成功');
    } else {
      console.log('❌ 元素事件转发失败');
    }
  } catch (error) {
    console.error('❌ 发送元素事件失败:', error);
  }
}

// 初始化content script
async function initializeContentScript(): Promise<void> {
  try {
    console.log('🚀 开始初始化content script...');

    // 检查是否在ChatGPT页面
    const isOnChatGPT = window.location.href.includes('chatgpt.com') || window.location.href.includes('chat.openai.com');
    console.log('📍 当前页面:', window.location.href);
    console.log('🎯 是否在ChatGPT页面:', isOnChatGPT);

    if (!isOnChatGPT) {
      console.log('⚠️ 不在ChatGPT页面，跳过初始化');
      return;
    }

    // 获取当前连接状态
    console.log('📡 获取WebSocket连接状态...');
    connectionState = await getConnectionStatus();
    console.log('✅ 当前WebSocket连接状态:', connectionState);

    // 设置网络数据拦截器和元素事件监听
    await setupNetworkInterceptor();
    setupElementEventListeners();

  } catch (error) {
    console.error('❌ 初始化content script失败:', error);
  }
}

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeContentScript);
} else {
  initializeContentScript();
}

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  console.log('收到来自background script的消息:', message);

  try {
    switch (message.type) {
      case 'PING':
        return handlePingMessage(sendResponse);

      case 'WS_STATE_CHANGE':
        return handleWebSocketStateChange(message, sendResponse);

      case 'WS_MESSAGE_FROM_SERVER':
        return handleWebSocketMessage(message, sendResponse);

      default:
        return handleExtensionMessage(message, sendResponse);
    }
  } catch (error) {
    console.error('处理消息失败:', error);
    sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
    return true;
  }
});

// 处理PING消息
function handlePingMessage(sendResponse: (response: any) => void): boolean {
  console.log('收到PING消息，content script已准备好');
  sendResponse({ success: true, ready: true });
  return true;
}

// 处理WebSocket状态变化
function handleWebSocketStateChange(message: any, sendResponse: (response: any) => void): boolean {
  console.log('WebSocket状态变化:', message.state, message.url);
  connectionState = {
    state: message.state,
    url: message.url || connectionState.url,
    connected: message.state === 'CONNECTED'
  };
  sendResponse({ success: true });
  return true;
}

// 处理来自WebSocket服务器的消息
function handleWebSocketMessage(message: any, sendResponse: (response: any) => void): boolean {
  console.log('收到来自WebSocket服务器的消息:', message);

  try {
    // 检查消息结构
    if (!message || typeof message !== 'object') {
      console.error('❌ 无效的消息对象:', message);
      sendResponse({ success: false, error: 'Invalid message object' });
      return true;
    }

    // 检查消息数据
    if (!message.data) {
      console.error('❌ 消息缺少data字段:', message);
      sendResponse({ success: false, error: 'Message missing data field' });
      return true;
    }

    let parsedMessage;
    try {
      // 安全的JSON解析
      if (typeof message.data === 'string') {
        if (message.data.trim() === '') {
          throw new Error('Empty message data');
        }
        parsedMessage = JSON.parse(message.data);
      } else if (message.data !== null && message.data !== undefined) {
        parsedMessage = message.data;
      } else {
        throw new Error('Message data is null or undefined');
      }
    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError);
      console.error('❌ 原始数据类型:', typeof message.data);
      console.error('❌ 原始数据内容:', message.data);
      console.error('❌ 解析错误堆栈:', parseError instanceof Error ? parseError.stack : 'No stack trace');

      sendResponse({
        success: false,
        error: `Failed to parse message data as JSON: ${parseError instanceof Error ? parseError.message : String(parseError)}`
      });
      return true;
    }

    console.log('解析的WebSocket消息:', parsedMessage);

    // 详细的消息结构验证
    if (parsedMessage === null || parsedMessage === undefined) {
      console.error('❌ 解析后的消息为null或undefined');
      sendResponse({ success: false, error: 'Parsed message is null or undefined' });
      return true;
    }

    if (typeof parsedMessage !== 'object') {
      console.error('❌ 解析后的消息不是对象:', typeof parsedMessage, parsedMessage);
      sendResponse({ success: false, error: `Parsed message is not an object, got ${typeof parsedMessage}` });
      return true;
    }

    // 检查消息类型
    if (!('type' in parsedMessage) || !parsedMessage.type) {
      console.error('❌ 消息缺少type字段:', parsedMessage);
      console.error('❌ 消息的所有属性:', Object.keys(parsedMessage));
      sendResponse({ success: false, error: 'Message missing type field' });
      return true;
    }

    console.log('📨 消息类型:', parsedMessage.type);
    console.log('📨 消息ID:', parsedMessage.id || 'No ID');

    try {
      // 安全的类型检查
      let isElementCmd = false;
      let isElementResp = false;

      try {
        isElementCmd = isElementCommand(parsedMessage);
      } catch (typeCheckError) {
        console.error('❌ 元素命令类型检查失败:', typeCheckError);
        console.error('❌ 类型检查错误堆栈:', typeCheckError instanceof Error ? typeCheckError.stack : 'No stack trace');
        console.error('❌ 检查的消息:', parsedMessage);
      }

      try {
        isElementResp = isElementResponse(parsedMessage);
      } catch (typeCheckError) {
        console.error('❌ 元素响应类型检查失败:', typeCheckError);
        console.error('❌ 类型检查错误堆栈:', typeCheckError instanceof Error ? typeCheckError.stack : 'No stack trace');
        console.error('❌ 检查的消息:', parsedMessage);
      }

      // 检查是否是元素查询指令
      if (isElementCmd) {
        console.log('🔍 处理元素查询指令:', parsedMessage);

        // 异步处理元素查询
        handleElementCommand(parsedMessage as ElementCommand)
          .then(result => {
            console.log('✅ 元素查询完成:', result);
          })
          .catch(error => {
            console.error('❌ 元素查询失败:', error);
            console.error('❌ 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
            console.error('❌ 错误详情:', {
              name: error instanceof Error ? error.name : 'Unknown',
              message: error instanceof Error ? error.message : String(error),
              command: parsedMessage
            });
          });
      } else if (isElementResp) {
        console.log('📤 收到元素查询响应消息:', parsedMessage.type);
        // 响应消息通常不需要在content script中处理，只是记录
        console.log('ℹ️ 响应消息详情:', parsedMessage);
      } else {
        console.log('ℹ️ 其他消息类型:', parsedMessage.type);
        // 可以在这里添加对其他消息类型的处理
        switch (parsedMessage.type) {
          case 'PING':
            console.log('🏓 收到PING消息');
            break;
          case 'PONG':
            console.log('🏓 收到PONG消息');
            break;
          default:
            console.log('❓ 未知消息类型:', parsedMessage.type);
        }
      }
    } catch (processingError) {
      console.error('❌ 消息处理过程中发生错误:', processingError);
      console.error('❌ 处理错误堆栈:', processingError instanceof Error ? processingError.stack : 'No stack trace');
      console.error('❌ 问题消息:', parsedMessage);

      sendResponse({
        success: false,
        error: `Message processing error: ${processingError instanceof Error ? processingError.message : String(processingError)}`
      });
      return true;
    }

    sendResponse({ success: true });
  } catch (error) {
    console.error('❌ 处理WebSocket消息时发生未预期错误:', error);
    console.error('❌ 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('❌ 原始消息:', message);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      details: {
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        originalMessage: message
      }
    });
  }
  return true;
}

// 处理其他扩展消息
function handleExtensionMessage(message: any, sendResponse: (response: any) => void): boolean {
  const result = handleMessage(message, sendResponse);
  return result instanceof Promise ? true : result;
}

// 通知后台脚本内容脚本已加载
chrome.runtime.sendMessage({
  type: 'CONTENT_SCRIPT_LOADED',
  url: window.location.href
}, (response) => {
  // 检查chrome.runtime.lastError
  if (chrome.runtime.lastError) {
    console.error('❌ 通知背景脚本失败:', chrome.runtime.lastError.message);
    return;
  }

  // 更新本地状态
  if (response) {
    console.log('从背景脚本接收到响应:', response);
    if (response.wsServerUrl) {
      connectionState.url = response.wsServerUrl;
    }
    if (response.connectionState) {
      connectionState.state = response.connectionState;
    }
    if (typeof response.connected === 'boolean') {
      connectionState.connected = response.connected;
    }

    console.log('更新后的连接状态:', connectionState);
  }
});

export {};