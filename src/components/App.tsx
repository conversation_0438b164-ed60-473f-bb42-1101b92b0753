import { useState } from 'react'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="max-w-md mx-auto p-6 text-center">
      <h1 className="text-4xl font-bold leading-tight mt-4">Vite + React</h1>
      <div className="p-8">
        <button 
          onClick={() => setCount((count) => count + 1)}
          className="rounded-lg border border-transparent px-5 py-2.5 bg-gray-800 text-white font-medium hover:border-blue-400 transition-colors"
        >
          count is {count}
        </button>
        <p className="mt-4">
          Edit <code className="font-mono bg-gray-100 p-1 rounded">src/components/App.tsx</code> and save to test HMR
        </p>
      </div>
      <p className="text-gray-500 text-sm">
        Click on the Vite and React logos to learn more
      </p>
    </div>
  )
}

export default App 