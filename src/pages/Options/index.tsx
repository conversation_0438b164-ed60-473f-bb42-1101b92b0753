import { useState, useEffect } from 'react';
import '@/index.css';

function Options() {
  const [serverUrl, setServerUrl] = useState<string>('');
  const [autoConnect, setAutoConnect] = useState<boolean>(false);
  const [saveStatus, setSaveStatus] = useState<string>('');

  useEffect(() => {
    // 从存储中加载设置
    chrome.storage.sync.get(['serverUrl', 'autoConnect'], (result) => {
      if (result.serverUrl) {
        setServerUrl(result.serverUrl);
      }
      
      if (result.autoConnect !== undefined) {
        setAutoConnect(result.autoConnect);
      }
    });
  }, []);

  // 保存设置
  const saveSettings = () => {
    chrome.storage.sync.set({
      serverUrl,
      autoConnect
    }, () => {
      setSaveStatus('设置已保存');
      
      // 3秒后清除状态消息
      setTimeout(() => {
        setSaveStatus('');
      }, 3000);
    });
  };

  // 重置设置
  const resetSettings = () => {
    setServerUrl('');
    setAutoConnect(false);
    
    chrome.storage.sync.remove(['serverUrl', 'autoConnect'], () => {
      setSaveStatus('设置已重置');
      
      // 3秒后清除状态消息
      setTimeout(() => {
        setSaveStatus('');
      }, 3000);
    });
  };

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">ChatGPT Forward 设置</h1>
      
      <div className="bg-white shadow-md rounded p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">连接设置</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">服务器URL</label>
          <input 
            type="text" 
            value={serverUrl} 
            onChange={(e) => setServerUrl(e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="ws://localhost:8765"
          />
          <p className="text-xs text-gray-500 mt-1">WebSocket服务器地址，例如：ws://localhost:8765</p>
        </div>
        
        <div className="mb-4">
          <label className="flex items-center">
            <input 
              type="checkbox" 
              checked={autoConnect} 
              onChange={(e) => setAutoConnect(e.target.checked)}
              className="mr-2"
            />
            <span>启动时自动连接</span>
          </label>
          <p className="text-xs text-gray-500 mt-1 ml-6">扩展启动时自动连接到服务器</p>
        </div>
      </div>
      
      <div className="flex justify-between">
        <button
          onClick={resetSettings}
          className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded"
        >
          重置设置
        </button>
        
        <div className="flex items-center">
          {saveStatus && (
            <span className="text-green-500 mr-4">{saveStatus}</span>
          )}
          
          <button
            onClick={saveSettings}
            className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
          >
            保存设置
          </button>
        </div>
      </div>
    </div>
  );
}

export default Options; 