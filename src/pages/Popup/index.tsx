import { useState, useEffect, useCallback } from 'react';
import '@/index.css';

function Popup() {
  const [serverUrl, setServerUrl] = useState<string>('');
  const [apiKey, setApiKey] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<string>('未连接');
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [lastError, setLastError] = useState<string>('');

  // Auto-save settings when inputs change (using local storage to avoid quota)
  const saveSettings = useCallback((urlToSave: string, keyToSave: string) => {
    console.log('💾 Auto-saving settings to local storage:', {
      wsServerUrl: urlToSave,
      apiKey: keyToSave ? '***存在***' : '未设置'
    });

    const dataToSave = {
      wsServerUrl: urlToSave,
      apiKey: keyToSave
    };

    // Use local storage to avoid sync quota limits
    chrome.storage.local.set(dataToSave, () => {
      if (chrome.runtime.lastError) {
        console.error('❌ Local storage save failed:', chrome.runtime.lastError);
      } else {
        console.log('✅ Settings saved to local storage');
      }
    });
  }, []);

  useEffect(() => {
    // 测试Chrome storage是否可用
    console.log('🔍 Popup检查Chrome storage API:', !!chrome.storage);
    console.log('🔍 Popup检查Chrome storage.sync:', !!chrome.storage?.sync);

    // 立即测试存储功能
    const testKey = 'popup_test_' + Date.now();
    chrome.storage.sync.set({ [testKey]: 'test_value' }, () => {
      if (chrome.runtime.lastError) {
        console.error('❌ Popup初始存储测试失败:', chrome.runtime.lastError);
      } else {
        console.log('✅ Popup初始存储测试成功');
        chrome.storage.sync.get([testKey], (result) => {
          console.log('📖 Popup初始存储读取测试:', result);
          // 清理测试数据
          chrome.storage.sync.remove([testKey]);
        });
      }
    });

    // 从存储中获取服务器URL、API密钥和连接状态 (优先使用local storage)
    chrome.storage.local.get(['wsServerUrl', 'apiKey', 'connectionStatus', 'lastError'], (localResult) => {
      if (chrome.runtime.lastError) {
        console.warn('⚠️ Popup local storage get error, trying sync:', chrome.runtime.lastError);
        // Fallback to sync storage
        chrome.storage.sync.get(['wsServerUrl', 'apiKey', 'connectionStatus', 'lastError'], (syncResult) => {
          if (chrome.runtime.lastError) {
            console.error('❌ Popup both local and sync storage get failed:', chrome.runtime.lastError);
            return;
          }
          console.log('📖 Popup从同步存储加载数据:', syncResult);
          loadDataFromResult(syncResult);
        });
      } else {
        console.log('📖 Popup从本地存储加载数据:', localResult);
        loadDataFromResult(localResult);
      }
    });

    // Helper function to load data from storage result
    function loadDataFromResult(result: any) {
      if (result.wsServerUrl) {
        console.log('🌐 Popup设置服务器URL:', result.wsServerUrl);
        setServerUrl(result.wsServerUrl);
      }

      if (result.apiKey) {
        console.log('🔑 Popup设置API密钥: ***存在***');
        setApiKey(result.apiKey);
      }

      if (result.connectionStatus) {
        console.log('📊 Popup设置连接状态:', result.connectionStatus);
        updateConnectionStatus(result.connectionStatus);
      }

      if (result.lastError) {
        console.log('❌ Popup设置错误信息:', result.lastError);
        setLastError(result.lastError);
      }
    }

    // 主动查询当前WebSocket连接状态
    chrome.runtime.sendMessage({
      type: 'WS_CONNECTION_STATUS'
    }, (response) => {
      if (response) {
        console.log('当前WebSocket连接状态:', response);
        if (response.connected) {
          updateConnectionStatus('已连接');
        } else {
          const stateText = getStatusTextFromState(response.state);
          updateConnectionStatus(stateText);
        }
        if (response.url) {
          setServerUrl(response.url);
        }
      }
    });

    // 监听连接状态变化
    const messageListener = (message: any) => {
      if (message.type === 'connectionStatus') {
        updateConnectionStatus(message.status);
        if (message.error) {
          // 处理认证错误的特殊显示
          if (message.error.includes('认证失败') || message.error.includes('Authentication failed')) {
            setLastError('认证失败 - 请检查API密钥是否正确');
          } else {
            setLastError(message.error);
          }
        } else {
          setLastError('');
        }
      }
      return true;
    };

    chrome.runtime.onMessage.addListener(messageListener);

    // 清理函数 - Chrome扩展API不支持removeListener，所以我们不需要清理
  }, []);

  // 将连接状态转换为文本
  const getStatusTextFromState = (state: string): string => {
    switch (state) {
      case 'DISCONNECTED':
        return '未连接';
      case 'CONNECTING':
        return '连接中';
      case 'CONNECTED':
        return '已连接';
      case 'RECONNECTING':
        return '重连中';
      case 'ERROR':
        return '连接失败';
      default:
        return '未知状态';
    }
  };

  // 更新连接状态
  const updateConnectionStatus = (status: string) => {
    setConnectionStatus(status);
    // 如果状态不是连接中或断开中，停止loading状态
    if (status !== '连接中' && status !== '断开中') {
      setIsConnecting(false);
    }
  };

  // Handle server URL change with auto-save
  const handleServerUrlChange = (value: string) => {
    setServerUrl(value);
    saveSettings(value, apiKey); // Use current apiKey state
  };

  // Handle API key change with auto-save
  const handleApiKeyChange = (value: string) => {
    setApiKey(value);
    saveSettings(serverUrl, value); // Use current serverUrl state
  };

  // 连接到服务器
  const connectToServer = () => {
    if (!serverUrl.trim()) {
      alert('请输入服务器URL');
      return;
    }

    if (!apiKey.trim()) {
      alert('请输入API密钥');
      return;
    }

    // 验证URL格式
    try {
      const url = new URL(serverUrl);
      if (!url.protocol.startsWith('ws')) {
        alert('请输入有效的WebSocket URL (ws:// 或 wss://)');
        return;
      }
    } catch (error) {
      alert('请输入有效的URL格式');
      return;
    }

    setIsConnecting(true);
    setLastError('');
    updateConnectionStatus('连接中');

    // 发送连接消息到后台脚本（设置已通过输入自动保存）
    console.log('📡 Popup发送连接消息到后台脚本');
    chrome.runtime.sendMessage({
      type: 'SET_WS_SERVER',
      url: serverUrl
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Chrome runtime error:', chrome.runtime.lastError);
        setIsConnecting(false);
        updateConnectionStatus('连接失败');
        setLastError('无法与后台脚本通信');
        return;
      }

      if (response && response.success) {
        // 连接成功，状态会通过消息监听器更新
        console.log('连接请求已发送');
      } else {
        setIsConnecting(false);
        updateConnectionStatus('连接失败');
        const errorMsg = response?.error || '未知错误';
        setLastError(errorMsg);
        console.error('连接失败:', errorMsg);
      }
    });
  };

  // 断开连接
  const disconnectFromServer = () => {
    setLastError('');
    updateConnectionStatus('断开中');

    chrome.runtime.sendMessage({ type: 'DISCONNECT_WS_SERVER' }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Chrome runtime error:', chrome.runtime.lastError);
        updateConnectionStatus('断开失败');
        setLastError('无法与后台脚本通信');
        setTimeout(() => updateConnectionStatus('已连接'), 2000);
        return;
      }

      if (response && response.success) {
        updateConnectionStatus('未连接');
        // 清除存储的连接状态和错误
        chrome.storage.sync.set({
          connectionStatus: '未连接',
          lastError: null
        });
      } else {
        updateConnectionStatus('断开失败');
        setLastError(response?.error || '断开连接失败');
        setTimeout(() => updateConnectionStatus('已连接'), 2000);
      }
    });
  };



  // 获取状态指示器的样式
  const getStatusIndicatorClass = () => {
    if (connectionStatus === '已连接') return 'status-connected';
    if (connectionStatus === '连接中' || connectionStatus === '断开中' || connectionStatus === '重连中') return 'status-connecting';
    return 'status-disconnected';
  };







  return (
    <div className="popup-container">
      <div className="popup-header">
        <h1 className="popup-title">ChatGPT Forward</h1>
      </div>

      <div className="popup-content">
        <div className="input-group">
          <label className="input-label">服务器URL</label>
          <input
            type="text"
            value={serverUrl}
            onChange={(e) => handleServerUrlChange(e.target.value)}
            className="input-field"
            placeholder="ws://localhost:8765"
          />
        </div>

        <div className="input-group">
          <label className="input-label">API密钥</label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => handleApiKeyChange(e.target.value)}
            className="input-field"
            placeholder="输入API密钥"
          />
        </div>

        <div className="status-container">
          <p className="status-text">
            连接状态
            <span className={`status-indicator ${getStatusIndicatorClass()}`}>
              {connectionStatus}
            </span>
          </p>
          {lastError && (
            <p className="error-text text-red-600 text-sm mt-1">
              错误: {lastError}
            </p>
          )}
        </div>
      </div>

      <div className="popup-footer">
        <div className="button-group">
          <button
            onClick={connectToServer}
            disabled={isConnecting || connectionStatus === '连接中' || connectionStatus === '断开中' || connectionStatus === '重连中'}
            className="btn-primary"
          >
            {(isConnecting || connectionStatus === '连接中' || connectionStatus === '重连中') && <span className="loading-spinner"></span>}
            {connectionStatus === '连接中' ? '连接中...' :
             connectionStatus === '重连中' ? '重连中...' :
             connectionStatus === '已连接' ? '重新连接' : '连接'}
          </button>

          <button
            onClick={disconnectFromServer}
            disabled={connectionStatus === '未连接' || connectionStatus === '断开中' || connectionStatus === '连接中' || connectionStatus === '连接失败'}
            className="btn-danger"
          >
            {connectionStatus === '断开中' ? '断开中...' : '断开'}
          </button>
        </div>
      </div>
    </div>
  );
}

export default Popup; 