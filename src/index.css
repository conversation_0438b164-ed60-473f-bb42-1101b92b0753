@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

@layer components {
  .popup-container {
    @apply bg-white rounded-xl shadow-2xl;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 350px;
    height: 450px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .popup-header {
    @apply px-6 pt-6 pb-4;
    flex-shrink: 0;
  }

  .popup-content {
    @apply px-6;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }

  .popup-footer {
    @apply px-6 pb-6 pt-4;
    flex-shrink: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(248, 250, 252, 0.5);
  }

  .popup-title {
    @apply text-2xl font-bold mb-0 text-center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .input-group {
    @apply mb-6;
  }

  .input-label {
    @apply block text-sm font-semibold mb-2 text-gray-700;
  }

  .input-field {
    @apply w-full p-3 border-2 border-gray-200 rounded-lg transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 focus:outline-none;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  }

  .status-container {
    @apply mb-6 p-3 rounded-lg;
    background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
  }

  .status-text {
    @apply text-sm font-medium text-gray-700;
  }

  .status-indicator {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ml-2;
  }

  .status-connected {
    @apply bg-green-100 text-green-800;
  }

  .status-connecting {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-disconnected {
    @apply bg-red-100 text-red-800;
  }

  .btn-primary {
    @apply flex-1 py-3 px-4 rounded-lg font-semibold text-white transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    border: none;
  }

  .btn-primary:hover {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  .btn-primary:disabled {
    @apply opacity-50 cursor-not-allowed transform-none;
    box-shadow: none;
  }

  .btn-danger {
    @apply flex-1 py-3 px-4 rounded-lg font-semibold text-white transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    border: none;
  }

  .btn-danger:hover {
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  }

  .btn-secondary {
    @apply w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
    background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #475569;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
  }

  .btn-secondary:hover {
    background: linear-gradient(145deg, #e2e8f0 0%, #cbd5e1 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  }

  .button-group {
    @apply flex space-x-3;
  }

  .loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2;
  }

  .debug-panel {
    @apply space-y-4;
  }

  .result-panel {
    @apply mt-4 p-3 border rounded-lg;
    background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #e2e8f0;
  }
}
