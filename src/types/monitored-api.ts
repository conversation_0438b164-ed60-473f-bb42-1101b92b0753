/**
 * 监控API请求相关的类型定义
 * 与WebSocket服务器的消息类型保持一致
 */

/**
 * 监控API请求的消息类型枚举
 */
export enum MonitoredAPIMessageType {
  // API请求相关
  MONITORED_API_REQUEST = 'MONITORED_API_REQUEST',
  MONITORED_API_HEADERS = 'MONITORED_API_HEADERS', 
  MONITORED_API_RESPONSE_HEADERS = 'MONITORED_API_RESPONSE_HEADERS',
  MONITORED_API_COMPLETED = 'MONITORED_API_COMPLETED',
  
  // API响应数据
  MONITORED_API_RESPONSE_DATA = 'MONITORED_API_RESPONSE_DATA'
}

/**
 * API类型枚举
 */
export enum APIType {
  USER_INFO = 'user_info',
  CHAT_REQUIREMENTS = 'chat_requirements', 
  CONVERSATION = 'conversation'
}

/**
 * 请求体数据接口
 */
export interface RequestBodyData {
  raw: any; // 解析后的JSON数据或原始文本
  formData: Record<string, string[]> | null; // 表单数据
  hasData: boolean; // 是否包含数据
}

/**
 * 请求元数据接口
 */
export interface RequestMetadata {
  endpoint_description: string; // 端点描述
  captured_at: string; // 捕获时间
  source: string; // 数据源
}

/**
 * 监控API请求消息接口
 */
export interface MonitoredAPIRequestMessage {
  type: MonitoredAPIMessageType.MONITORED_API_REQUEST;
  id: string;
  timestamp: string;
  data: {
    api_type: string | null;
    api_endpoint: string;
    request: {
      url: string;
      method: string;
      requestId: string;
      tabId: number;
      frameId: number;
      requestBody: RequestBodyData;
    };
    metadata: RequestMetadata;
  };
}

/**
 * 监控API请求头消息接口
 */
export interface MonitoredAPIHeadersMessage {
  type: MonitoredAPIMessageType.MONITORED_API_HEADERS;
  id: string;
  timestamp: string;
  data: {
    api_type: string | null;
    api_endpoint: string;
    request: {
      url: string;
      method: string;
      requestId: string;
      requestHeaders: chrome.webRequest.HttpHeader[] | undefined;
    };
    metadata: RequestMetadata;
  };
}

/**
 * 监控API响应头消息接口
 */
export interface MonitoredAPIResponseHeadersMessage {
  type: MonitoredAPIMessageType.MONITORED_API_RESPONSE_HEADERS;
  id: string;
  timestamp: string;
  data: {
    api_type: string | null;
    api_endpoint: string;
    response: {
      url: string;
      statusCode: number;
      statusLine: string;
      requestId: string;
      responseHeaders: chrome.webRequest.HttpHeader[] | undefined;
    };
    metadata: RequestMetadata;
  };
}

/**
 * 监控API完成消息接口
 */
export interface MonitoredAPICompletedMessage {
  type: MonitoredAPIMessageType.MONITORED_API_COMPLETED;
  id: string;
  timestamp: string;
  data: {
    api_type: string | null;
    api_endpoint: string;
    response: {
      url: string;
      statusCode: number;
      statusLine: string;
      requestId: string;
      responseHeaders: chrome.webRequest.HttpHeader[] | undefined;
    };
    timing: {
      timeStamp: number;
      completed_at: string;
    };
    metadata: RequestMetadata;
  };
}

/**
 * 响应数据接口 - 用于DevTools拦截的响应内容
 */
export interface ResponseData {
  url: string;
  statusCode: number;
  statusText: string;
  headers: Record<string, string> | chrome.webRequest.HttpHeader[];
  content: any; // 解析后的响应内容（JSON或原始文本）
  contentLength: number;
  mimeType: string;
  encoding: string;
}

/**
 * 请求数据接口 - 用于DevTools拦截的请求信息
 */
export interface DevToolsRequestData {
  url: string;
  method: string;
  requestId: string;
  headers: Record<string, string> | chrome.webRequest.HttpHeader[];
}

/**
 * 时间信息接口
 */
export interface TimingData {
  startTime: string;
  endTime: string;
  duration?: number;
}

/**
 * 监控API响应数据消息接口
 */
export interface MonitoredAPIResponseDataMessage {
  type: MonitoredAPIMessageType.MONITORED_API_RESPONSE_DATA;
  id: string;
  timestamp: string;
  data: {
    api_type: string | null;
    api_endpoint: string;
    request: DevToolsRequestData;
    response: ResponseData;
    timing: TimingData;
    metadata: RequestMetadata;
  };
}

/**
 * 监控API消息联合类型
 */
export type MonitoredAPIMessage = 
  | MonitoredAPIRequestMessage
  | MonitoredAPIHeadersMessage
  | MonitoredAPIResponseHeadersMessage
  | MonitoredAPICompletedMessage
  | MonitoredAPIResponseDataMessage;

/**
 * 工具函数：创建监控API请求消息
 */
export function createMonitoredAPIRequestMessage(
  apiType: string | null,
  apiEndpoint: string,
  request: MonitoredAPIRequestMessage['data']['request'],
  metadata: RequestMetadata
): MonitoredAPIRequestMessage {
  return {
    type: MonitoredAPIMessageType.MONITORED_API_REQUEST,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: {
      api_type: apiType,
      api_endpoint: apiEndpoint,
      request,
      metadata
    }
  };
}

/**
 * 工具函数：检查是否为监控API消息
 */
export function isMonitoredAPIMessage(message: any): message is MonitoredAPIMessage {
  return message && 
         typeof message.type === 'string' && 
         Object.values(MonitoredAPIMessageType).includes(message.type as MonitoredAPIMessageType);
}
