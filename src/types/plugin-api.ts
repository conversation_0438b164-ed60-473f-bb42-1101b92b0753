/**
 * ChatGPT Forward Plugin API 接口定义
 * 
 * 这个文件定义了插件支持的所有接口和消息类型
 */

import { MessageType } from './websocket';
import type { ElementCommand, ElementResult } from './element-query';

// 重新导出MessageType以便其他模块使用
export { MessageType };

// ============================================================================
// 浏览器控制接口
// ============================================================================

// 打开ChatGPT页面指令
export interface OpenChatGPTCommand {
  type: MessageType.OPEN_CHATGPT;
  id: string;
  timestamp: string;
  data?: {
    url?: string; // 可选的特定URL，默认为chatgpt.com
    newTab?: boolean; // 是否在新标签页打开，默认true
    focus?: boolean; // 是否聚焦到新标签页，默认true
  };
}

// 设置窗口大小指令
export interface SetWindowSizeCommand {
  type: MessageType.SET_WINDOW_SIZE;
  id: string;
  timestamp: string;
  data: {
    width: number;
    height: number;
    windowId?: number; // 可选的窗口ID，默认为当前窗口
  };
}

// 页面截图指令
export interface TakeScreenshotCommand {
  type: MessageType.TAKE_SCREENSHOT;
  id: string;
  timestamp: string;
  data?: {
    format?: 'png' | 'jpeg'; // 图片格式，默认png
    quality?: number; // 图片质量 0-100，仅对jpeg有效
    fullPage?: boolean; // 是否截取整个页面，默认false（仅可见区域）
    tabId?: number; // 可选的标签页ID，默认为当前活动标签页
  };
}

// 浏览器控制响应
export interface BrowserControlResponse {
  type: MessageType.BROWSER_CONTROL_RESPONSE;
  id: string; // 对应指令的ID
  timestamp: string;
  success: boolean;
  data?: any; // 响应数据，如截图的base64数据
  error?: string; // 错误信息
}

// ============================================================================
// 消息类型联合
// ============================================================================

// 所有浏览器控制指令
export type BrowserControlCommand = 
  | OpenChatGPTCommand
  | SetWindowSizeCommand
  | TakeScreenshotCommand;

// 所有插件支持的指令
export type PluginCommand = 
  | BrowserControlCommand
  | ElementCommand;

// 所有插件响应
export type PluginResponse = 
  | BrowserControlResponse
  | ElementResult;

// ============================================================================
// 插件状态和配置
// ============================================================================

// 插件状态
export interface PluginState {
  connected: boolean;
  wsServerUrl: string;
  connectionState: 'DISCONNECTED' | 'CONNECTING' | 'CONNECTED' | 'ERROR';
  lastActivity?: string;
  activeTabId?: number;
  currentUrl?: string;
}

// 插件配置
export interface PluginConfig {
  wsServerUrl: string;
  autoConnect: boolean;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  enableNetworkInterception: boolean;
  enableElementQuery: boolean;
  debugMode: boolean;
}

// ============================================================================
// 内部消息类型（插件组件间通信）
// ============================================================================

// Content Script 消息类型
export enum ContentScriptMessageType {
  // 生命周期
  CONTENT_SCRIPT_LOADED = 'CONTENT_SCRIPT_LOADED',
  PING = 'PING',
  
  // WebSocket 相关
  WS_SEND_MESSAGE = 'WS_SEND_MESSAGE',
  WS_CONNECTION_STATUS = 'WS_CONNECTION_STATUS',
  WS_STATE_CHANGE = 'WS_STATE_CHANGE',
  WS_MESSAGE_FROM_SERVER = 'WS_MESSAGE_FROM_SERVER',
  
  // 浏览器控制
  BROWSER_CONTROL_COMMAND = 'BROWSER_CONTROL_COMMAND',
}

// Content Script 消息接口
export interface ContentScriptMessage {
  type: ContentScriptMessageType;
  data?: any;
  tabId?: number;
  url?: string;
}

// ============================================================================
// 工具函数
// ============================================================================

// 检查是否为浏览器控制指令
export function isBrowserControlCommand(message: any): message is BrowserControlCommand {
  const browserControlTypes = [
    MessageType.OPEN_CHATGPT,
    MessageType.SET_WINDOW_SIZE,
    MessageType.TAKE_SCREENSHOT
  ];
  return browserControlTypes.includes(message.type);
}

// 检查是否为元素查询指令
export function isElementCommand(message: any): message is ElementCommand {
  try {
    // 基础验证
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (!message.type || typeof message.type !== 'string') {
      return false;
    }

    const elementCommandTypes = [
      MessageType.ELEMENT_QUERY,
      MessageType.PAGE_INFO,
      MessageType.ELEMENT_WATCH,
      MessageType.BATCH_ELEMENT_QUERY,
      MessageType.ELEMENT_SCREENSHOT
    ];

    return elementCommandTypes.includes(message.type);
  } catch (error) {
    console.error('Error in isElementCommand:', error);
    return false;
  }
}

// 检查是否为元素查询响应
export function isElementResponse(message: any): boolean {
  try {
    // 基础验证
    if (!message || typeof message !== 'object') {
      return false;
    }

    if (!message.type || typeof message.type !== 'string') {
      return false;
    }

    const elementResponseTypes = [
      MessageType.ELEMENT_QUERY_RESULT,
      MessageType.ELEMENT_ACTION_RESULT,
      MessageType.PAGE_INFO_RESULT,
      MessageType.ELEMENT_WATCH_RESULT,
      MessageType.BATCH_ELEMENT_QUERY_RESULT,
      MessageType.ELEMENT_SCREENSHOT_RESULT,
      MessageType.ELEMENT_QUERY_RESPONSE,
      MessageType.ELEMENT_EVENT_MESSAGE
    ];

    return elementResponseTypes.includes(message.type);
  } catch (error) {
    console.error('Error in isElementResponse:', error);
    return false;
  }
}

// 创建响应消息
export function createResponse(
  originalCommand: PluginCommand,
  success: boolean,
  data?: any,
  error?: string
): PluginResponse {
  const baseResponse = {
    id: originalCommand.id,
    timestamp: new Date().toISOString(),
    success,
    data,
    error
  };

  if (isBrowserControlCommand(originalCommand)) {
    return {
      type: MessageType.BROWSER_CONTROL_RESPONSE,
      ...baseResponse
    } as BrowserControlResponse;
  } else {
    // 对于元素查询，返回类型由具体的处理器决定
    throw new Error('Element command responses should be handled by element query service');
  }
}

// 生成唯一ID
export function generateId(prefix: string = 'msg'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
}
