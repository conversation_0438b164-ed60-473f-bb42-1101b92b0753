/**
 * WebSocket Message Types and Interfaces
 *
 * 简化的消息类型，专注于数据转发功能
 */

// 简化的消息类型枚举 - 与plugin-api.ts保持一致
export enum MessageType {
  // 连接管理
  CONNECT = "CONNECT",
  DISCONNECT = "DISCONNECT",
  PING = "PING",
  PONG = "PONG",
  ERROR = "ERROR",

  // 浏览器控制指令
  OPEN_CHATGPT = "OPEN_CHATGPT",
  SET_WINDOW_SIZE = "SET_WINDOW_SIZE",
  TAKE_SCREENSHOT = "TAKE_SCREENSHOT",

  // 元素查询指令
  ELEMENT_QUERY = "ELEMENT_QUERY",
  PAGE_INFO = "PAGE_INFO",
  ELEMENT_WATCH = "ELEMENT_WATCH",
  BATCH_ELEMENT_QUERY = "BATCH_ELEMENT_QUERY",
  ELEMENT_SCREENSHOT = "ELEMENT_SCREENSHOT",

  // 响应消息
  ELEMENT_QUERY_RESPONSE = "ELEMENT_QUERY_RESPONSE",
  ELEMENT_QUERY_RESULT = "ELEMENT_QUERY_RESULT",
  ELEMENT_ACTION_RESULT = "ELEMENT_ACTION_RESULT",
  PAGE_INFO_RESULT = "PAGE_INFO_RESULT",
  ELEMENT_WATCH_RESULT = "ELEMENT_WATCH_RESULT",
  BATCH_ELEMENT_QUERY_RESULT = "BATCH_ELEMENT_QUERY_RESULT",
  ELEMENT_SCREENSHOT_RESULT = "ELEMENT_SCREENSHOT_RESULT",
  ELEMENT_EVENT_MESSAGE = "ELEMENT_EVENT_MESSAGE",
  BROWSER_CONTROL_RESPONSE = "BROWSER_CONTROL_RESPONSE",
}

// 连接状态枚举
export enum ConnectionStatus {
  DISCONNECTED = "DISCONNECTED",
  CONNECTING = "CONNECTING",
  CONNECTED = "CONNECTED",
  ERROR = "ERROR"
}

// 基础消息接口
export interface BaseMessage {
  type: MessageType | string;
  id: string;
  timestamp?: Date;
  data?: any; // 简化为any类型，支持任意JSON数据
}

// 注意：数据转发现在通过监控API消息类型处理，不再需要专门的FORWARD_DATA类型

// 连接消息接口
export interface ConnectMessage extends BaseMessage {
  type: MessageType.CONNECT;
  data?: {
    url?: string;
  };
}

// 断开连接消息接口
export interface DisconnectMessage extends BaseMessage {
  type: MessageType.DISCONNECT;
  data?: Record<string, unknown>;
}

// 错误消息接口
export interface ErrorMessage extends BaseMessage {
  type: MessageType.ERROR;
  data: {
    message: string;
    code?: string;
  };
}

// Ping/Pong消息接口
export interface PingMessage extends BaseMessage {
  type: MessageType.PING;
  data?: Record<string, unknown>;
}

export interface PongMessage extends BaseMessage {
  type: MessageType.PONG;
  data?: Record<string, unknown>;
}

// 联合类型，包含所有可能的消息
export type WebSocketMessage =
  | ConnectMessage
  | DisconnectMessage
  | ErrorMessage
  | PingMessage
  | PongMessage
  | BaseMessage;

// 连接信息接口
export interface ConnectionInfo {
  clientId: string;
  remoteAddress: string;
  connectedAt: Date;
  lastActivity: Date;
}

// WebSocket配置接口
export interface WebSocketConfig {
  url: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  pingInterval?: number;
  timeout?: number;
}

// 工具函数：检查是否为有效的消息类型
export function isMessageType(type: string): type is MessageType {
  return Object.values(MessageType).includes(type as MessageType);
}

// 工具函数：创建基础消息
export function createBaseMessage(type: MessageType | string, data?: any): BaseMessage {
  return {
    type,
    id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    timestamp: new Date(),
    data
  };
}

// 注意：数据转发现在通过监控API消息类型处理，不再需要专门的转发消息创建函数

// 工具函数：创建错误消息
export function createErrorMessage(message: string, code?: string): ErrorMessage {
  return {
    type: MessageType.ERROR,
    id: `error-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    timestamp: new Date(),
    data: { message, code }
  };
}

export function createForwardDataMessage(data: any): BaseMessage {
  return {
    type: 'FORWARD_DATA',
    id: `forward-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
    timestamp: new Date(),
    data
  };
}
