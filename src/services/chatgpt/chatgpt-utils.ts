/**
 * ChatGPT相关工具函数
 * 提供简单的URL检测和工具函数
 *
 * 注意：网络拦截现在由background script的WebRequest API处理
 */

/**
 * API端点配置接口
 */
export interface APIEndpointConfig {
  pattern: string;
  description: string;
  isDynamic?: boolean; // 表示URL包含动态参数
}

/**
 * 需要监控的API端点配置
 * 集中管理所有需要过滤的API请求
 */
export const MONITORED_API_ENDPOINTS: Record<string, APIEndpointConfig> = {
  // 用户信息相关
  USER_INFO: {
    pattern: '/backend-api/me',
    description: '获取用户信息',
    isDynamic: false
  },

  // 聊天需求相关
  CHAT_REQUIREMENTS: {
    pattern: '/backend-api/sentinel/chat-requirements',
    description: '获取聊天需求和设置',
    isDynamic: false
  },

  // 对话相关 (支持动态ID)
  CONVERSATION: {
    pattern: '/backend-api/conversation/',
    description: '对话相关操作',
    isDynamic: true // 表示URL包含动态参数
  }
};

/**
 * 检查URL是否为需要监控的API请求
 */
export function isMonitoredAPIRequest(url: string): boolean {
  // 首先检查是否是ChatGPT域名
  if (!isChatGPTRequest(url)) {
    return false;
  }

  // 检查是否匹配监控的API端点
  return Object.values(MONITORED_API_ENDPOINTS).some(endpoint => {
    if (endpoint.isDynamic) {
      return url.includes(endpoint.pattern);
    } else {
      return url.includes(endpoint.pattern);
    }
  });
}

/**
 * 获取API请求的类型
 */
export function getMonitoredAPIType(url: string): string | null {
  for (const [key, endpoint] of Object.entries(MONITORED_API_ENDPOINTS)) {
    if (endpoint.isDynamic) {
      if (url.includes(endpoint.pattern)) {
        return key.toLowerCase();
      }
    } else {
      if (url.includes(endpoint.pattern)) {
        return key.toLowerCase();
      }
    }
  }
  return null;
}

/**
 * 检查URL是否为ChatGPT相关请求
 */
export function isChatGPTRequest(url: string): boolean {
  const patterns = [
    'chatgpt.com',
    'chat.openai.com',
    'backend-api',
    'openai.com/backend-api',
    'api.openai.com',
    'platform.openai.com'
  ];

  return patterns.some(pattern => url.includes(pattern));
}

/**
 * 获取ChatGPT请求的类型
 */
export function getChatGPTRequestType(url: string): string {
  if (url.includes('/backend-api/conversation')) {
    return 'conversation';
  }
  if (url.includes('/backend-api/chat')) {
    return 'chat';
  }
  if (url.includes('/backend-api/models')) {
    return 'models';
  }
  if (url.includes('/backend-api/accounts')) {
    return 'accounts';
  }
  if (url.includes('/backend-api/')) {
    return 'backend-api';
  }
  if (url.includes('api.openai.com')) {
    return 'openai-api';
  }
  return 'unknown';
}

/**
 * 格式化网络请求数据
 */
export function formatNetworkData(data: any): any {
  return {
    type: 'CHATGPT_NETWORK_DATA',
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: data
  };
}

/**
 * 检查是否在ChatGPT页面
 */
export function isOnChatGPTPage(): boolean {
  const url = window.location.href;
  return url.includes('chatgpt.com') || url.includes('chat.openai.com');
}

/**
 * 获取当前页面信息
 */
export function getPageInfo(): {
  url: string;
  title: string;
  isChatGPT: boolean;
  timestamp: string;
} {
  return {
    url: window.location.href,
    title: document.title,
    isChatGPT: isOnChatGPTPage(),
    timestamp: new Date().toISOString()
  };
}
