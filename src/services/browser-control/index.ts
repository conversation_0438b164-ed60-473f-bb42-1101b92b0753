/**
 * 浏览器控制服务
 * 
 * 处理浏览器相关的操作：打开页面、设置窗口大小、截图等
 */

import type {
  BrowserControlCommand,
  OpenChatGPTCommand,
  SetWindowSizeCommand,
  TakeScreenshotCommand,
  BrowserControlResponse
} from '@/types/plugin-api';
import { MessageType } from '@/types/websocket';

export class BrowserControlService {
  /**
   * 处理浏览器控制指令
   */
  async handleCommand(command: BrowserControlCommand): Promise<BrowserControlResponse> {
    try {
      switch (command.type) {
        case MessageType.OPEN_CHATGPT:
          return await this.handleOpenChatGPT(command);
        case MessageType.SET_WINDOW_SIZE:
          return await this.handleSetWindowSize(command);
        case MessageType.TAKE_SCREENSHOT:
          return await this.handleTakeScreenshot(command);
        default:
          throw new Error(`Unsupported browser control command: ${(command as any).type}`);
      }
    } catch (error) {
      return this.createErrorResponse(command, error);
    }
  }

  /**
   * 打开ChatGPT页面
   */
  private async handleOpenChatGPT(command: OpenChatGPTCommand): Promise<BrowserControlResponse> {
    console.log('handleOpenChatGPT received command:', JSON.stringify(command, null, 2));
    const { url = 'https://chatgpt.com', focus = true, newTab = true } = command.data || {};

    try {
      if (newTab) {
        const tab = await chrome.tabs.create({
          url,
          active: focus,
        });
        return {
          type: MessageType.BROWSER_CONTROL_RESPONSE,
          id: command.id,
          timestamp: new Date().toISOString(),
          success: true,
          data: {
            tabId: tab.id,
            url: tab.url,
            windowId: tab.windowId
          }
        };
      } else {
        const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
        // Redirect the active tab to the ChatGPT URL
        if (activeTab.id === undefined) {
          throw new Error('Failed to get active tab ID');
        } else {
          await chrome.tabs.update(activeTab.id, { url });
        }
        return {
          type: MessageType.BROWSER_CONTROL_RESPONSE,
          id: command.id,
          timestamp: new Date().toISOString(),
          success: true,
          data: {
            tabId: activeTab.id,
            url: url,
            windowId: activeTab.windowId
          }
        };
      }
    } catch (error) {
      throw new Error(`Failed to open ChatGPT page: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置窗口大小
   */
  private async handleSetWindowSize(command: SetWindowSizeCommand): Promise<BrowserControlResponse> {
    const { width, height, windowId } = command.data;

    try {
      // 如果没有指定窗口ID，获取当前窗口
      const targetWindowId = windowId || (await chrome.windows.getCurrent()).id;

      if (!targetWindowId) {
        throw new Error('No window ID available');
      }

      const updatedWindow = await chrome.windows.update(targetWindowId, {
        width,
        height
      });

      return {
        type: MessageType.BROWSER_CONTROL_RESPONSE,
        id: command.id,
        timestamp: new Date().toISOString(),
        success: true,
        data: {
          windowId: updatedWindow.id,
          width: updatedWindow.width,
          height: updatedWindow.height,
          left: updatedWindow.left,
          top: updatedWindow.top
        }
      };
    } catch (error) {
      throw new Error(`Failed to set window size: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 页面截图
   */
  private async handleTakeScreenshot(command: TakeScreenshotCommand): Promise<BrowserControlResponse> {
    const { format = 'png', quality = 90, fullPage = false, tabId } = command.data || {};

    try {
      // 如果没有指定标签页ID，获取当前活动标签页
      let targetTabId = tabId;
      if (!targetTabId) {
        const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
        targetTabId = activeTab?.id;
      }

      if (!targetTabId) {
        throw new Error('No tab ID available');
      }

      // 截图选项
      const captureOptions: chrome.tabs.CaptureVisibleTabOptions = {
        format: format as 'png' | 'jpeg',
        ...(format === 'jpeg' && { quality: quality / 100 }) // Chrome API expects 0-1
      };

      let dataUrl: string;

      if (fullPage) {
        // 全页面截图需要特殊处理
        dataUrl = await this.captureFullPage(targetTabId, captureOptions);
      } else {
        // 可见区域截图
        dataUrl = await chrome.tabs.captureVisibleTab(captureOptions);
      }

      return {
        type: MessageType.BROWSER_CONTROL_RESPONSE,
        id: command.id,
        timestamp: new Date().toISOString(),
        success: true,
        data: {
          dataUrl,
          format,
          quality,
          fullPage,
          tabId: targetTabId
        }
      };
    } catch (error) {
      throw new Error(`Failed to take screenshot: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 全页面截图（需要滚动页面）
   */
  private async captureFullPage(
    tabId: number,
    captureOptions: chrome.tabs.CaptureVisibleTabOptions
  ): Promise<string> {
    // 注入脚本获取页面尺寸
    const [result] = await chrome.scripting.executeScript({
      target: { tabId },
      func: () => {
        return {
          scrollHeight: document.documentElement.scrollHeight,
          scrollWidth: document.documentElement.scrollWidth,
          clientHeight: document.documentElement.clientHeight,
          clientWidth: document.documentElement.clientWidth,
          scrollTop: document.documentElement.scrollTop,
          scrollLeft: document.documentElement.scrollLeft
        };
      }
    });

    const pageInfo = result.result;
    if (!pageInfo) {
      throw new Error('Failed to get page dimensions');
    }

    // 如果页面不需要滚动，直接截图
    if (pageInfo.scrollHeight <= pageInfo.clientHeight && pageInfo.scrollWidth <= pageInfo.clientWidth) {
      return await chrome.tabs.captureVisibleTab(captureOptions);
    }

    // TODO: 实现完整的全页面截图逻辑
    // 这需要滚动页面并拼接多个截图
    // 目前先返回可见区域截图
    console.warn('Full page screenshot not fully implemented, returning visible area');
    return await chrome.tabs.captureVisibleTab(captureOptions);
  }

  /**
   * 创建错误响应
   */
  private createErrorResponse(command: BrowserControlCommand, error: unknown): BrowserControlResponse {
    return {
      type: MessageType.BROWSER_CONTROL_RESPONSE,
      id: command.id,
      timestamp: new Date().toISOString(),
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

// 导出单例实例
export const browserControlService = new BrowserControlService();
