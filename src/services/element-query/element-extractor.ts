/**
 * 元素信息提取器
 * 提取元素的详细信息，包括位置、样式、属性等
 */

import type { 
  ElementInfo, 
  ElementPosition, 
  ElementStyles, 
  ElementAttributes,
  SelectorType 
} from '@/types/element-query';
import { SelectorEngine } from './selector-engine';

/**
 * 元素信息提取器类
 */
export class ElementExtractor {
  private selectorEngine: SelectorEngine;

  constructor() {
    this.selectorEngine = SelectorEngine.getInstance();
  }

  /**
   * 验证元素是否有效
   */
  private validateElement(element: any, methodName: string): Element {
    console.log(`🔍 ${methodName}: Validating element:`, element);

    if (!element) {
      const error = new Error(`${methodName}: element is null or undefined`);
      console.error(`❌ ${methodName}: Element validation failed - null or undefined`);
      throw error;
    }

    if (!(element instanceof Element)) {
      const error = new Error(`${methodName}: provided element is not a valid DOM Element (type: ${typeof element})`);
      console.error(`❌ ${methodName}: Element validation failed - not a DOM Element:`, element);
      throw error;
    }

    console.log(`✅ ${methodName}: Element validation passed`);
    return element;
  }

  /**
   * 提取元素的完整信息
   */
  extractElementInfo(
    element: Element,
    selector: string,
    selectorType: SelectorType
  ): ElementInfo {
    // 验证元素
    this.validateElement(element, 'extractElementInfo');

    return {
      selector,
      selectorType,
      position: this.extractPosition(element),
      styles: this.extractStyles(element),
      attributes: this.extractAttributes(element),
      isVisible: this.isElementVisible(element),
      isInViewport: this.isElementInViewport(element),
      hasChildren: element.children.length > 0,
      childrenCount: element.children.length,
      parentSelector: this.getParentSelector(element),
      xpath: this.selectorEngine.generateXPath(element),
      cssPath: this.selectorEngine.generateCSSPath(element)
    };
  }

  /**
   * 提取元素位置信息
   */
  private extractPosition(element: Element): ElementPosition {
    // 验证元素
    this.validateElement(element, 'extractPosition');

    try {
      const rect = element.getBoundingClientRect();
      const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
      const scrollY = window.pageYOffset || document.documentElement.scrollTop;

      return {
        x: rect.left,
        y: rect.top,
        pageX: rect.left + scrollX,
        pageY: rect.top + scrollY,
        width: rect.width,
        height: rect.height,
        top: rect.top,
        left: rect.left,
        right: rect.right,
        bottom: rect.bottom
      };
    } catch (error) {
      console.error('Error extracting element position:', error);
      throw new Error(`Failed to extract element position: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 提取元素样式信息
   */
  private extractStyles(element: Element): ElementStyles {
    const computedStyle = window.getComputedStyle(element);

    return {
      display: computedStyle.display,
      visibility: computedStyle.visibility,
      opacity: computedStyle.opacity,
      zIndex: computedStyle.zIndex,
      position: computedStyle.position,
      backgroundColor: computedStyle.backgroundColor,
      color: computedStyle.color,
      fontSize: computedStyle.fontSize,
      fontFamily: computedStyle.fontFamily,
      border: computedStyle.border,
      margin: computedStyle.margin,
      padding: computedStyle.padding
    };
  }

  /**
   * 提取元素属性信息
   */
  private extractAttributes(element: Element): ElementAttributes {
    const attributes: ElementAttributes = {
      tagName: element.tagName.toLowerCase()
    };

    // 基本属性
    if (element.id) attributes.id = element.id;
    if (element.className) attributes.className = element.className;
    if (element.textContent) attributes.textContent = element.textContent.trim();
    
    // innerHTML (限制长度避免过大)
    const innerHTML = element.innerHTML;
    if (innerHTML && innerHTML.length < 10000) {
      attributes.innerHTML = innerHTML;
    }

    // 表单元素特有属性
    if (element instanceof HTMLInputElement) {
      attributes.value = element.value;
      attributes.placeholder = element.placeholder;
      attributes.disabled = element.disabled;
      attributes.checked = element.checked;
    }

    if (element instanceof HTMLSelectElement) {
      attributes.value = element.value;
      attributes.disabled = element.disabled;
      attributes.selected = element.selectedIndex >= 0;
    }

    if (element instanceof HTMLTextAreaElement) {
      attributes.value = element.value;
      attributes.placeholder = element.placeholder;
      attributes.disabled = element.disabled;
    }

    // 链接元素
    if (element instanceof HTMLAnchorElement) {
      attributes.href = element.href;
    }

    // 图片元素
    if (element instanceof HTMLImageElement) {
      attributes.src = element.src;
      attributes.alt = element.alt;
    }

    // 通用属性
    if (element.getAttribute('title')) {
      attributes.title = element.getAttribute('title')!;
    }

    // 获取所有自定义属性
    for (let i = 0; i < element.attributes.length; i++) {
      const attr = element.attributes[i];
      if (attr.name.startsWith('data-') || attr.name.startsWith('aria-')) {
        attributes[attr.name] = attr.value;
      }
    }

    return attributes;
  }

  /**
   * 检查元素是否可见
   */
  private isElementVisible(element: Element): boolean {
    // 验证元素，如果无效则返回false
    try {
      this.validateElement(element, 'isElementVisible');
    } catch {
      return false;
    }

    try {
      const rect = element.getBoundingClientRect();
      const style = window.getComputedStyle(element);

      return (
        rect.width > 0 &&
        rect.height > 0 &&
        style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        parseFloat(style.opacity) > 0
      );
    } catch (error) {
      console.error('Error checking element visibility:', error);
      return false;
    }
  }

  /**
   * 检查元素是否在视口内
   */
  private isElementInViewport(element: Element): boolean {
    // 验证元素，如果无效则返回false
    try {
      this.validateElement(element, 'isElementInViewport');
    } catch {
      return false;
    }

    try {
      const rect = element.getBoundingClientRect();
      const windowHeight = window.innerHeight || document.documentElement.clientHeight;
      const windowWidth = window.innerWidth || document.documentElement.clientWidth;

      return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= windowHeight &&
        rect.right <= windowWidth
      );
    } catch (error) {
      console.error('Error checking element viewport status:', error);
      return false;
    }
  }

  /**
   * 获取父元素选择器
   */
  private getParentSelector(element: Element): string | undefined {
    const parent = element.parentElement;
    if (!parent || parent === document.body) {
      return undefined;
    }

    // 优先使用ID
    if (parent.id) {
      return `#${parent.id}`;
    }

    // 使用类名
    if (parent.className) {
      const classes = parent.className.trim().split(/\s+/);
      return `.${classes[0]}`;
    }

    // 使用标签名
    return parent.tagName.toLowerCase();
  }

  /**
   * 获取元素的中心点坐标
   */
  getElementCenter(element: Element): { x: number; y: number } {
    // 验证元素
    this.validateElement(element, 'getElementCenter');

    try {
      const rect = element.getBoundingClientRect();
      return {
        x: rect.left + rect.width / 2,
        y: rect.top + rect.height / 2
      };
    } catch (error) {
      console.error('Error getting element center:', error);
      throw new Error(`Failed to get element center: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 检查元素是否可点击
   */
  isElementClickable(element: Element): boolean {
    const style = window.getComputedStyle(element);
    const tagName = element.tagName.toLowerCase();
    
    // 检查是否是可点击的元素类型
    const clickableTags = ['a', 'button', 'input', 'select', 'textarea', 'label'];
    const isClickableTag = clickableTags.includes(tagName);
    
    // 检查是否有点击事件监听器
    const hasClickHandler = (element as any).onclick !== null ||
                           element.getAttribute('onclick') !== null;
    
    // 检查CSS样式
    const hasPointerCursor = style.cursor === 'pointer';
    
    return (
      this.isElementVisible(element) &&
      style.pointerEvents !== 'none' &&
      (isClickableTag || hasClickHandler || hasPointerCursor)
    );
  }

  /**
   * 获取元素的所有子元素信息
   */
  getChildrenInfo(element: Element): ElementInfo[] {
    const children: ElementInfo[] = [];
    
    for (let i = 0; i < element.children.length; i++) {
      const child = element.children[i];
      const selector = this.selectorEngine.generateCSSPath(child);
      const info = this.extractElementInfo(child, selector, 'css');
      children.push(info);
    }
    
    return children;
  }

  /**
   * 获取页面信息
   */
  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      status: document.readyState,
      size: {
        width: document.documentElement.scrollWidth,
        height: document.documentElement.scrollHeight,
        scrollWidth: document.documentElement.scrollWidth,
        scrollHeight: document.documentElement.scrollHeight
      },
      scroll: {
        x: window.pageXOffset || document.documentElement.scrollLeft,
        y: window.pageYOffset || document.documentElement.scrollTop
      },
      viewport: {
        width: window.innerWidth || document.documentElement.clientWidth,
        height: window.innerHeight || document.documentElement.clientHeight
      }
    };
  }

  /**
   * 获取元素截图（转换为base64）
   */
  async captureElementScreenshot(
    element: Element,
    options: { format?: string; quality?: number; padding?: number } = {}
  ): Promise<string> {
    const { format = 'png', quality = 0.9, padding = 0 } = options;

    try {
      // 验证元素
      this.validateElement(element, 'captureElementScreenshot');

      // 获取元素位置和尺寸
      const rect = element.getBoundingClientRect();

      if (rect.width === 0 || rect.height === 0) {
        throw new Error('元素尺寸为0，无法截图');
      }

      // 确保元素在视口内可见
      if (!this.isElementVisible(element)) {
        console.warn('元素不可见，尝试滚动到视图中');
        element.scrollIntoView({ behavior: 'instant', block: 'center' });
        // 等待滚动完成
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 使用canvas绘制方案（更可靠）
      return await this.captureWithCanvas(element, rect, padding, format, quality);
    } catch (error) {
      console.error('元素截图失败:', error);
      throw new Error(`元素截图失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }



  /**
   * 使用Canvas绘制元素内容
   */
  private async captureWithCanvas(
    element: Element,
    rect: DOMRect,
    padding: number,
    format: string,
    quality: number
  ): Promise<string> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('无法创建canvas上下文');
    }

    canvas.width = rect.width + padding * 2;
    canvas.height = rect.height + padding * 2;

    // 设置背景色为白色
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    try {
      // 方案1: 尝试使用html2canvas风格的DOM到Canvas转换
      const success = await this.drawElementToCanvas(ctx, element, rect, padding);

      if (success) {
        return canvas.toDataURL(`image/${format}`, quality);
      } else {
        // 方案2: 使用SVG foreignObject
        const svgData = await this.elementToSVG(element, rect, padding);
        const img = new Image();

        return new Promise((resolve) => {
          img.onload = () => {
            // 清除canvas并重新绘制
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
            const dataUrl = canvas.toDataURL(`image/${format}`, quality);
            resolve(dataUrl);
          };

          img.onerror = () => {
            // 方案3: 绘制占位图
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            this.drawElementPlaceholder(ctx, element, rect, padding);
            const dataUrl = canvas.toDataURL(`image/${format}`, quality);
            resolve(dataUrl);
          };

          img.src = svgData;
        });
      }
    } catch (error) {
      // 最后的降级方案：绘制占位图
      console.warn('所有截图方案都失败，使用占位图:', error);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      this.drawElementPlaceholder(ctx, element, rect, padding);
      return canvas.toDataURL(`image/${format}`, quality);
    }
  }

  /**
   * 直接将元素绘制到Canvas上
   */
  private async drawElementToCanvas(
    ctx: CanvasRenderingContext2D,
    element: Element,
    rect: DOMRect,
    padding: number
  ): Promise<boolean> {
    try {
      const computedStyle = window.getComputedStyle(element);

      // 绘制背景
      this.drawElementBackground(ctx, element, computedStyle, rect, padding);

      // 绘制边框
      this.drawElementBorder(ctx, element, computedStyle, rect, padding);

      // 绘制文本内容
      this.drawElementText(ctx, element, computedStyle, rect, padding);

      // 绘制子元素（简化处理）
      this.drawChildElements(ctx, element, rect, padding);

      return true;
    } catch (error) {
      console.warn('直接Canvas绘制失败:', error);
      return false;
    }
  }

  /**
   * 将元素转换为SVG数据URL
   */
  private async elementToSVG(element: Element, rect: DOMRect, padding: number): Promise<string> {
    const computedStyle = window.getComputedStyle(element);

    // 克隆元素以避免影响原始DOM
    const clonedElement = element.cloneNode(true) as Element;

    // 应用计算样式到克隆元素
    this.applyComputedStyles(clonedElement, computedStyle);

    const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="${rect.width + padding * 2}" height="${rect.height + padding * 2}">
        <foreignObject x="${padding}" y="${padding}" width="${rect.width}" height="${rect.height}">
          <div xmlns="http://www.w3.org/1999/xhtml">
            ${clonedElement.outerHTML}
          </div>
        </foreignObject>
      </svg>
    `;

    return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
  }

  /**
   * 应用计算样式到元素
   */
  private applyComputedStyles(element: Element, computedStyle: CSSStyleDeclaration): void {
    if (!(element instanceof HTMLElement)) return;

    // 应用关键样式
    const importantStyles = [
      'width', 'height', 'padding', 'margin', 'border',
      'background', 'color', 'font-family', 'font-size', 'font-weight',
      'text-align', 'line-height', 'display', 'position'
    ];

    importantStyles.forEach(prop => {
      const value = computedStyle.getPropertyValue(prop);
      if (value) {
        element.style.setProperty(prop, value, 'important');
      }
    });

    // 递归应用到子元素
    Array.from(element.children).forEach(child => {
      if (child instanceof HTMLElement) {
        const childStyle = window.getComputedStyle(child);
        this.applyComputedStyles(child, childStyle);
      }
    });
  }

  /**
   * 绘制元素占位图
   */
  private drawElementPlaceholder(
    ctx: CanvasRenderingContext2D,
    element: Element,
    rect: DOMRect,
    padding: number
  ): void {

    // 绘制边框
    ctx.strokeStyle = '#cccccc';
    ctx.lineWidth = 2;
    ctx.strokeRect(padding, padding, rect.width, rect.height);

    // 绘制对角线
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding + rect.width, padding + rect.height);
    ctx.moveTo(padding + rect.width, padding);
    ctx.lineTo(padding, padding + rect.height);
    ctx.stroke();

    // 绘制元素信息
    ctx.fillStyle = '#666666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    const tagName = element.tagName.toLowerCase();
    const className = element.className ? `.${element.className.split(' ')[0]}` : '';
    const id = element.id ? `#${element.id}` : '';

    const text = `${tagName}${id}${className}`;
    const textX = padding + rect.width / 2;
    const textY = padding + rect.height / 2;

    ctx.fillText(text, textX, textY);
    ctx.fillText(`${Math.round(rect.width)}×${Math.round(rect.height)}`, textX, textY + 16);
  }

  /**
   * 计算元素之间的距离
   */
  calculateDistance(element1: Element, element2: Element): number {
    const center1 = this.getElementCenter(element1);
    const center2 = this.getElementCenter(element2);
    
    const dx = center2.x - center1.x;
    const dy = center2.y - center1.y;
    
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * 查找最近的可点击元素
   */
  findNearestClickableElement(targetElement: Element): Element | null {
    const allElements = Array.from(document.querySelectorAll('*'));
    const clickableElements = allElements.filter(el => this.isElementClickable(el));

    if (clickableElements.length === 0) {
      return null;
    }

    let nearestElement = clickableElements[0];
    let minDistance = this.calculateDistance(targetElement, nearestElement);

    for (const element of clickableElements) {
      const distance = this.calculateDistance(targetElement, element);
      if (distance < minDistance) {
        minDistance = distance;
        nearestElement = element;
      }
    }

    return nearestElement;
  }

  /**
   * 绘制元素背景
   */
  private drawElementBackground(
    ctx: CanvasRenderingContext2D,
    _element: Element,
    computedStyle: CSSStyleDeclaration,
    rect: DOMRect,
    padding: number
  ): void {
    const x = padding;
    const y = padding;
    const width = rect.width;
    const height = rect.height;

    // 绘制背景色
    const backgroundColor = computedStyle.backgroundColor;
    if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(x, y, width, height);
    }

    // 简化的渐变背景处理
    const backgroundImage = computedStyle.backgroundImage;
    if (backgroundImage && backgroundImage !== 'none') {
      // 这里可以添加更复杂的背景图片处理
      // 目前只处理简单的线性渐变
      if (backgroundImage.includes('linear-gradient')) {
        this.drawLinearGradient(ctx, backgroundImage, x, y, width, height);
      }
    }
  }

  /**
   * 绘制线性渐变背景
   */
  private drawLinearGradient(
    ctx: CanvasRenderingContext2D,
    _gradientStr: string,
    x: number,
    y: number,
    width: number,
    height: number
  ): void {
    try {
      // 简化的渐变解析（仅处理基本情况）
      const gradient = ctx.createLinearGradient(x, y, x + width, y + height);

      // 默认渐变色
      gradient.addColorStop(0, '#ff6b6b');
      gradient.addColorStop(1, '#4ecdc4');

      ctx.fillStyle = gradient;
      ctx.fillRect(x, y, width, height);
    } catch (error) {
      console.warn('渐变绘制失败:', error);
    }
  }

  /**
   * 绘制元素边框
   */
  private drawElementBorder(
    ctx: CanvasRenderingContext2D,
    _element: Element,
    computedStyle: CSSStyleDeclaration,
    rect: DOMRect,
    padding: number
  ): void {
    const x = padding;
    const y = padding;
    const width = rect.width;
    const height = rect.height;

    const borderWidth = parseFloat(computedStyle.borderWidth) || 0;
    const borderColor = computedStyle.borderColor || '#000000';
    const borderStyle = computedStyle.borderStyle;

    if (borderWidth > 0 && borderStyle !== 'none') {
      ctx.strokeStyle = borderColor;
      ctx.lineWidth = borderWidth;

      if (borderStyle === 'dashed') {
        ctx.setLineDash([5, 5]);
      } else if (borderStyle === 'dotted') {
        ctx.setLineDash([2, 2]);
      } else {
        ctx.setLineDash([]);
      }

      ctx.strokeRect(x, y, width, height);
    }
  }

  /**
   * 绘制元素文本内容
   */
  private drawElementText(
    ctx: CanvasRenderingContext2D,
    element: Element,
    computedStyle: CSSStyleDeclaration,
    rect: DOMRect,
    padding: number
  ): void {
    const textContent = element.textContent?.trim();
    if (!textContent) return;

    const x = padding;
    const y = padding;
    const width = rect.width;

    // 设置字体样式
    const fontSize = computedStyle.fontSize || '14px';
    const fontFamily = computedStyle.fontFamily || 'Arial';
    const fontWeight = computedStyle.fontWeight || 'normal';
    const color = computedStyle.color || '#000000';
    const textAlign = computedStyle.textAlign || 'left';

    ctx.fillStyle = color;
    ctx.font = `${fontWeight} ${fontSize} ${fontFamily}`;
    ctx.textAlign = textAlign as CanvasTextAlign;
    ctx.textBaseline = 'top';

    // 简化的文本绘制（不处理换行）
    const maxWidth = width - 20; // 留一些边距
    let textX = x + 10;

    if (textAlign === 'center') {
      textX = x + width / 2;
    } else if (textAlign === 'right') {
      textX = x + width - 10;
    }

    const textY = y + 10;

    // 截断过长的文本
    let displayText = textContent;
    if (ctx.measureText(displayText).width > maxWidth) {
      while (ctx.measureText(displayText + '...').width > maxWidth && displayText.length > 0) {
        displayText = displayText.slice(0, -1);
      }
      displayText += '...';
    }

    ctx.fillText(displayText, textX, textY, maxWidth);
  }

  /**
   * 绘制子元素（简化处理）
   */
  private drawChildElements(
    ctx: CanvasRenderingContext2D,
    element: Element,
    rect: DOMRect,
    padding: number
  ): void {
    // 简化处理：只绘制直接子元素的轮廓
    const children = Array.from(element.children);

    children.forEach(child => {
      try {
        const childRect = child.getBoundingClientRect();
        const parentRect = element.getBoundingClientRect();

        // 计算相对位置
        const relativeX = childRect.left - parentRect.left + padding;
        const relativeY = childRect.top - parentRect.top + padding;

        if (relativeX >= 0 && relativeY >= 0 &&
            relativeX < rect.width && relativeY < rect.height) {

          // 绘制子元素轮廓
          ctx.strokeStyle = '#cccccc';
          ctx.lineWidth = 1;
          ctx.setLineDash([2, 2]);
          ctx.strokeRect(relativeX, relativeY, childRect.width, childRect.height);
        }
      } catch (error) {
        // 忽略子元素绘制错误
      }
    });
  }
}
