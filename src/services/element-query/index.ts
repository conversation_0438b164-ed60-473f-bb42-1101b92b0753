/**
 * 元素查询服务主入口
 * 整合选择器引擎、信息提取器和操作器
 */

import type { 
  ElementCommand,
  ElementResult,
  ElementQueryCommand,
  ElementQueryResult,
  ElementActionResult,
  PageInfoCommand,
  PageInfoResult,
  ElementWatchCommand,
  ElementEventResult,
  BatchElementQueryCommand,
  BatchElementQueryResult,
  ElementScreenshotCommand,
  ElementScreenshotResult
} from '@/types/element-query';

import { SelectorEngine } from './selector-engine';
import { ElementExtractor } from './element-extractor';
import { ElementActions } from './element-actions';

/**
 * 元素查询服务类
 */
export class ElementQueryService {
  private static instance: ElementQueryService;
  private selectorEngine: SelectorEngine;
  private extractor: ElementExtractor;
  private actions: ElementActions;
  private watchers: Map<string, { elements: Element[]; events: string[]; callback: Function }> = new Map();

  private constructor() {
    this.selectorEngine = SelectorEngine.getInstance();
    this.extractor = new ElementExtractor();
    this.actions = new ElementActions();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ElementQueryService {
    if (!ElementQueryService.instance) {
      ElementQueryService.instance = new ElementQueryService();
    }
    return ElementQueryService.instance;
  }

  /**
   * 处理元素查询指令
   */
  async handleCommand(command: ElementCommand): Promise<ElementResult> {
    const startTime = Date.now();

    try {
      // 详细的命令验证
      if (!command) {
        throw new Error('Command is null or undefined');
      }

      if (typeof command !== 'object') {
        throw new Error(`Command must be an object, got ${typeof command}`);
      }

      if (!command.type) {
        throw new Error('Command missing required field: type');
      }

      if (!command.id) {
        throw new Error('Command missing required field: id');
      }

      console.log(`🔧 ElementQueryService: 处理命令 ${command.type} (ID: ${command.id})`);

      switch (command.type) {
        case 'ELEMENT_QUERY':
          return await this.handleElementQuery(command, startTime);
        case 'PAGE_INFO':
          return await this.handlePageInfo(command, startTime);
        case 'ELEMENT_WATCH':
          return await this.handleElementWatch(command, startTime);
        case 'BATCH_ELEMENT_QUERY':
          return await this.handleBatchElementQuery(command, startTime);
        case 'ELEMENT_SCREENSHOT':
          return await this.handleElementScreenshot(command, startTime);
        default:
          throw new Error(`不支持的指令类型: ${(command as any).type}`);
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;

      console.error('❌ ElementQueryService: 处理命令失败:', error);
      console.error('❌ 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
      console.error('❌ 命令详情:', JSON.stringify(command, null, 2));
      console.error('❌ 执行时间:', executionTime, 'ms');

      return {
        type: 'ELEMENT_QUERY_RESULT',
        id: command?.id || 'unknown',
        timestamp: new Date().toISOString(),
        success: false,
        elements: [],
        count: 0,
        error: error instanceof Error ? `${error.name}: ${error.message}` : String(error),
        executionTime
      } as ElementQueryResult;
    }
  }

  /**
   * 处理元素查询
   */
  private async handleElementQuery(command: ElementQueryCommand, startTime: number): Promise<ElementQueryResult> {
    // Debug logging
    console.log('🔍 handleElementQuery received command:', JSON.stringify(command, null, 2));

    // Extract selector and actions from command data
    const selector = (command as any).data?.selector || command.selector;
    const actions = (command as any).data?.actions || command.actions;

    console.log('🔍 Extracted selector:', selector);
    console.log('🔍 Extracted actions:', actions);

    // Validate selector
    if (!selector) {
      throw new Error('Selector is missing from command');
    }

    if (!selector.type) {
      throw new Error('Selector type is missing');
    }

    if (!selector.value) {
      throw new Error('Selector value is missing');
    }

    // 查找元素
    const elements = await this.selectorEngine.findElements(
      selector.type,
      selector.value,
      selector.options
    );

    console.log(`🔍 Found ${elements.length} elements`);
    console.log('🔍 Elements array:', elements);

    // 提取元素信息，添加错误处理
    const elementInfos = elements.map((element, index) => {
      try {
        console.log(`🔍 Processing element at index ${index}:`, element);

        // 验证元素是否有效
        if (!element) {
          console.warn(`❌ Element at index ${index} is null or undefined`);
          return null;
        }

        // 验证元素是否是有效的DOM元素
        if (!(element instanceof Element)) {
          console.warn(`❌ Element at index ${index} is not a valid DOM Element:`, typeof element, element);
          return null;
        }

        console.log(`✅ Element at index ${index} is valid, extracting info...`);
        const info = this.extractor.extractElementInfo(element, selector.value, selector.type);
        console.log(`✅ Successfully extracted info for element at index ${index}`);
        return info;
      } catch (error) {
        console.error(`❌ Error extracting info for element at index ${index}:`, error);
        console.error(`❌ Element details:`, element);
        console.error(`❌ Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
        return null;
      }
    }).filter(info => info !== null); // 过滤掉null值

    console.log(`🔍 Successfully processed ${elementInfos.length} out of ${elements.length} elements`);

    // 执行操作（如果有）
    const actionResults: ElementActionResult[] = [];
    if (actions && actions.length > 0 && elements.length > 0) {
      console.log(`🎯 Executing ${actions.length} actions on ${elements.length} elements`);

      for (const action of actions) {
        console.log(`🎯 Executing action: ${action.type}`);

        for (let i = 0; i < elements.length; i++) {
          const element = elements[i];

          try {
            console.log(`🎯 Executing action ${action.type} on element ${i}:`, element);

            // 验证元素在执行操作前是否仍然有效
            if (!element) {
              throw new Error(`Element at index ${i} is null or undefined`);
            }

            if (!(element instanceof Element)) {
              throw new Error(`Element at index ${i} is not a valid DOM Element`);
            }

            const result = await this.actions.executeAction(element, action);
            console.log(`✅ Action ${action.type} executed successfully on element ${i}`);

            actionResults.push({
              type: 'ELEMENT_ACTION_RESULT',
              id: command.id,
              timestamp: new Date().toISOString(),
              success: true,
              actionType: action.type,
              result,
              executionTime: Date.now() - startTime
            });
          } catch (error) {
            console.error(`❌ Action ${action.type} failed on element ${i}:`, error);

            actionResults.push({
              type: 'ELEMENT_ACTION_RESULT',
              id: command.id,
              timestamp: new Date().toISOString(),
              success: false,
              actionType: action.type,
              error: error instanceof Error ? error.message : String(error),
              executionTime: Date.now() - startTime
            });
          }
        }
      }
    }

    const executionTime = Date.now() - startTime;

    return {
      type: 'ELEMENT_QUERY_RESULT',
      id: command.id,
      timestamp: new Date().toISOString(),
      success: true,
      elements: elementInfos,
      count: elementInfos.length,
      executionTime
    };
  }

  /**
   * 处理页面信息查询
   */
  private async handlePageInfo(command: PageInfoCommand, _startTime: number): Promise<PageInfoResult> {
    const pageInfo = this.extractor.getPageInfo();

    return {
      type: 'PAGE_INFO_RESULT',
      success: true,
      id: command.id,
      timestamp: new Date().toISOString(),
      ...pageInfo
    };
  }

  /**
   * 处理元素监听
   */
  private async handleElementWatch(command: ElementWatchCommand, _startTime: number): Promise<ElementEventResult> {
    // Extract data from command
    const selector = (command as any).data?.selector || command.selector;
    const events = (command as any).data?.events || command.events;
    const options = (command as any).data?.options || command.options || {};
    
    // 查找元素
    const elements = await this.selectorEngine.findElements(
      selector.type,
      selector.value,
      { multiple: true }
    );

    if (elements.length === 0) {
      throw new Error('未找到要监听的元素');
    }

    // 设置事件监听
    const watchId = command.id;
    const eventHandler = (event: Event) => {
      const element = event.target as Element;
      const elementInfo = this.extractor.extractElementInfo(element, selector.value, selector.type);
      
      // 发送事件结果（这里需要通过回调或事件系统发送）
      const result: ElementEventResult = {
        type: 'ELEMENT_EVENT',
        id: watchId,
        timestamp: new Date().toISOString(),
        eventType: event.type,
        element: elementInfo,
        eventData: this.extractEventData(event)
      };

      // 触发回调或发送到WebSocket
      this.notifyElementEvent(result);

      // 如果是一次性监听，移除监听器
      if (options.once) {
        this.removeElementWatch(watchId);
      }
    };

    // 为每个元素添加事件监听
    elements.forEach(element => {
      events.forEach((eventType: string) => {
        element.addEventListener(eventType, eventHandler);
      });
    });

    // 保存监听器信息
    this.watchers.set(watchId, {
      elements,
      events,
      callback: eventHandler
    });

    // 设置超时
    if (options.timeout) {
      setTimeout(() => {
        this.removeElementWatch(watchId);
      }, options.timeout);
    }

    // 返回初始结果
    return {
      type: 'ELEMENT_EVENT',
      id: command.id,
      timestamp: new Date().toISOString(),
      eventType: 'watch_started',
      element: this.extractor.extractElementInfo(elements[0], selector.value, selector.type)
    };
  }

  /**
   * 处理批量元素查询
   */
  private async handleBatchElementQuery(command: BatchElementQueryCommand, startTime: number): Promise<BatchElementQueryResult> {
    const results = [];

    // Extract queries from command data
    const queries = (command as any).data?.queries || command.queries;

    for (const query of queries) {
      try {
        const elements = await this.selectorEngine.findElements(
          query.selector.type,
          query.selector.value,
          query.selector.options
        );

        console.log(`🔍 Batch query ${query.id}: Found ${elements.length} elements`);

        const elementInfos = elements.map((element, index) => {
          try {
            console.log(`🔍 Batch query ${query.id}: Processing element at index ${index}:`, element);

            // 验证元素是否有效
            if (!element) {
              console.warn(`❌ Batch query ${query.id}: Element at index ${index} is null or undefined`);
              return null;
            }

            // 验证元素是否是有效的DOM元素
            if (!(element instanceof Element)) {
              console.warn(`❌ Batch query ${query.id}: Element at index ${index} is not a valid DOM Element:`, typeof element, element);
              return null;
            }

            console.log(`✅ Batch query ${query.id}: Element at index ${index} is valid, extracting info...`);
            const info = this.extractor.extractElementInfo(element, query.selector.value, query.selector.type);
            console.log(`✅ Batch query ${query.id}: Successfully extracted info for element at index ${index}`);
            return info;
          } catch (error) {
            console.error(`❌ Batch query ${query.id}: Error extracting info for element at index ${index}:`, error);
            console.error(`❌ Batch query ${query.id}: Element details:`, element);
            console.error(`❌ Batch query ${query.id}: Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
            return null;
          }
        }).filter(info => info !== null); // 过滤掉null值

        console.log(`🔍 Batch query ${query.id}: Successfully processed ${elementInfos.length} out of ${elements.length} elements`);

        // 执行操作
        if (query.actions && query.actions.length > 0 && elements.length > 0) {
          for (const action of query.actions) {
            for (const element of elements) {
              await this.actions.executeAction(element, action);
            }
          }
        }

        results.push({
          id: query.id,
          success: true,
          elements: elementInfos
        });
      } catch (error) {
        results.push({
          id: query.id,
          success: false,
          elements: [],
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    const totalExecutionTime = Date.now() - startTime;

    return {
      type: 'BATCH_ELEMENT_QUERY_RESULT',
      id: command.id,
      timestamp: new Date().toISOString(),
      results,
      totalExecutionTime
    };
  }

  /**
   * 处理元素截图
   */
  private async handleElementScreenshot(command: ElementScreenshotCommand, _startTime: number): Promise<ElementScreenshotResult> {
    // Extract data from command
    const selector = (command as any).data?.selector || command.selector;
    const options = (command as any).data?.options || command.options || {};
    
    const elements = await this.selectorEngine.findElements(
      selector.type,
      selector.value,
      { multiple: false }
    );

    if (elements.length === 0) {
      throw new Error('未找到要截图的元素');
    }

    const element = elements[0];
    const elementInfo = this.extractor.extractElementInfo(element, selector.value, selector.type);

    try {
      const dataUrl = await this.extractor.captureElementScreenshot(element, options);
      
      return {
        type: 'ELEMENT_SCREENSHOT_RESULT',
        id: command.id,
        timestamp: new Date().toISOString(),
        success: true,
        dataUrl,
        element: elementInfo
      };
    } catch (error) {
      return {
        type: 'ELEMENT_SCREENSHOT_RESULT',
        id: command.id,
        timestamp: new Date().toISOString(),
        success: false,
        error: error instanceof Error ? error.message : String(error),
        element: elementInfo
      };
    }
  }

  /**
   * 移除元素监听
   */
  private removeElementWatch(watchId: string): void {
    const watcher = this.watchers.get(watchId);
    if (watcher) {
      watcher.elements.forEach(element => {
        watcher.events.forEach(eventType => {
          element.removeEventListener(eventType, watcher.callback as EventListener);
        });
      });
      this.watchers.delete(watchId);
    }
  }

  /**
   * 提取事件数据
   */
  private extractEventData(event: Event): any {
    const data: any = {
      type: event.type,
      bubbles: event.bubbles,
      cancelable: event.cancelable,
      timestamp: Date.now()
    };

    // 鼠标事件
    if (event instanceof MouseEvent) {
      data.clientX = event.clientX;
      data.clientY = event.clientY;
      data.button = event.button;
      data.buttons = event.buttons;
    }

    // 键盘事件
    if (event instanceof KeyboardEvent) {
      data.key = event.key;
      data.code = event.code;
      data.ctrlKey = event.ctrlKey;
      data.shiftKey = event.shiftKey;
      data.altKey = event.altKey;
    }

    // 输入事件
    if (event instanceof InputEvent) {
      data.inputType = event.inputType;
      data.data = event.data;
    }

    return data;
  }

  /**
   * 通知元素事件（需要与WebSocket集成）
   */
  private notifyElementEvent(result: ElementEventResult): void {
    // 这里需要与WebSocket服务集成
    console.log('元素事件:', result);
    
    // 可以通过自定义事件发送
    window.dispatchEvent(new CustomEvent('elementEvent', { detail: result }));
  }

  /**
   * 清理所有监听器
   */
  cleanup(): void {
    this.watchers.forEach((_, watchId) => {
      this.removeElementWatch(watchId);
    });
    this.watchers.clear();
  }
}

// 导出单例实例
export const elementQueryService = ElementQueryService.getInstance();

// 导出类型
export * from '@/types/element-query';
