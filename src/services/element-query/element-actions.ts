/**
 * 元素操作器
 * 执行各种元素操作，如点击、输入、滚动等
 */

import type { ElementAction } from '@/types/element-query';
import { ElementExtractor } from './element-extractor';

/**
 * 元素操作器类
 */
export class ElementActions {
  private extractor: ElementExtractor;

  constructor() {
    this.extractor = new ElementExtractor();
  }

  /**
   * 执行元素操作
   */
  async executeAction(element: Element, action: ElementAction): Promise<any> {
    const { type, params = {} } = action;
    const { delay = 0 } = params;

    // 执行前延迟
    if (delay > 0) {
      await this.sleep(delay);
    }

    switch (type) {
      case 'click':
        return this.clickElement(element, params.x, params.y);
      case 'input':
        return this.inputText(element, params.text || '');
      case 'scroll':
        return this.scrollElement(element, params.scrollX, params.scrollY);
      case 'hover':
        return this.hoverElement(element);
      case 'focus':
        return this.focusElement(element);
      case 'blur':
        return this.blurElement(element);
      case 'getAttribute':
        return this.getAttribute(element, params.attribute || '');
      case 'setText':
        return this.setText(element, params.text || '');
      case 'simulateKeyboard':
        return this.simulateKeyboard(element, params.key || '', params.options || {});
      default:
        throw new Error(`不支持的操作类型: ${type}`);
    }
  }

  /**
   * 点击元素
   */
  private async clickElement(element: Element, x?: number, y?: number): Promise<boolean> {
    try {
      // 添加空值检查
      if (!element) {
        console.error('Cannot click: element is null or undefined');
        return false;
      }

      // 确保元素可见
      this.scrollIntoView(element);
      await this.sleep(100);

      let clickX: number, clickY: number;

      if (x !== undefined && y !== undefined) {
        // 使用指定坐标（相对于元素）
        const rect = element.getBoundingClientRect();
        clickX = rect.left + x;
        clickY = rect.top + y;
      } else {
        // 使用元素中心点
        const center = this.extractor.getElementCenter(element);
        clickX = center.x;
        clickY = center.y;
      }

      // 创建点击事件
      const clickEvent = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: clickX,
        clientY: clickY
      });

      // 触发事件
      element.dispatchEvent(clickEvent);

      // 如果是可点击元素，也调用原生点击
      if (element instanceof HTMLElement) {
        element.click();
      }

      return true;
    } catch (error) {
      console.error('点击元素失败:', error);
      return false;
    }
  }

  /**
   * 输入文本
   */
  private async inputText(element: Element, text: string): Promise<boolean> {
    try {
      // 检查元素是否支持文本输入
      if (!this.isElementEditable(element)) {
        throw new Error('元素不支持文本输入');
      }

      // 聚焦元素
      if (element instanceof HTMLElement) {
        element.focus();
        await this.sleep(50);
      }

      // 处理不同类型的可编辑元素
      if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        // 处理 input 和 textarea 元素
        return this.inputToFormElement(element, text);
      } else if (this.isContentEditable(element)) {
        // 处理 contenteditable 元素
        return this.inputToContentEditable(element as HTMLElement, text);
      }

      return false;
    } catch (error) {
      console.error('输入文本失败:', error);
      return false;
    }
  }

  /**
   * 检查元素是否可编辑
   */
  private isElementEditable(element: Element): boolean {
    // 检查是否是表单输入元素
    if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
      return !element.disabled && !element.readOnly;
    }

    // 检查是否是 contenteditable 元素
    if (this.isContentEditable(element)) {
      return true;
    }

    return false;
  }

  /**
   * 检查元素是否是 contenteditable
   */
  private isContentEditable(element: Element): boolean {
    if (!(element instanceof HTMLElement)) {
      return false;
    }

    // 检查 contenteditable 属性
    const contentEditable = element.getAttribute('contenteditable');
    if (contentEditable === 'true' || contentEditable === '') {
      return true;
    }

    // 检查继承的 contenteditable
    return element.isContentEditable;
  }

  /**
   * 向表单元素输入文本
   */
  private async inputToFormElement(element: HTMLInputElement | HTMLTextAreaElement, text: string): Promise<boolean> {
    try {
      // 清空现有内容
      element.select();

      // 设置值
      element.value = text;

      // 触发输入事件
      const inputEvent = new Event('input', { bubbles: true });
      element.dispatchEvent(inputEvent);

      const changeEvent = new Event('change', { bubbles: true });
      element.dispatchEvent(changeEvent);

      return true;
    } catch (error) {
      console.error('向表单元素输入文本失败:', error);
      return false;
    }
  }

  /**
   * 向 contenteditable 元素输入文本
   */
  private async inputToContentEditable(element: HTMLElement, text: string): Promise<boolean> {
    try {
      // 选择所有内容
      const selection = window.getSelection();
      if (selection) {
        const range = document.createRange();
        range.selectNodeContents(element);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // 清空现有内容
      element.innerHTML = '';

      // 设置新文本
      element.textContent = text;

      // 触发输入事件
      const inputEvent = new Event('input', { bubbles: true });
      element.dispatchEvent(inputEvent);

      const changeEvent = new Event('change', { bubbles: true });
      element.dispatchEvent(changeEvent);

      // 触发 beforeinput 事件（某些应用可能需要）
      const beforeInputEvent = new InputEvent('beforeinput', {
        bubbles: true,
        cancelable: true,
        inputType: 'insertText',
        data: text
      });
      element.dispatchEvent(beforeInputEvent);

      return true;
    } catch (error) {
      console.error('向contenteditable元素输入文本失败:', error);
      return false;
    }
  }

  /**
   * 滚动元素
   */
  private async scrollElement(element: Element, scrollX?: number, scrollY?: number): Promise<boolean> {
    try {
      if (scrollX !== undefined || scrollY !== undefined) {
        // 滚动到指定位置
        element.scrollTo({
          left: scrollX || 0,
          top: scrollY || 0,
          behavior: 'smooth'
        });
      } else {
        // 滚动到元素可见
        this.scrollIntoView(element);
      }

      return true;
    } catch (error) {
      console.error('滚动元素失败:', error);
      return false;
    }
  }

  /**
   * 悬停元素
   */
  private async hoverElement(element: Element): Promise<boolean> {
    try {
      // 添加空值检查
      if (!element) {
        console.error('Cannot hover: element is null or undefined');
        return false;
      }

      const center = this.extractor.getElementCenter(element);

      const mouseEnterEvent = new MouseEvent('mouseenter', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: center.x,
        clientY: center.y
      });

      const mouseOverEvent = new MouseEvent('mouseover', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: center.x,
        clientY: center.y
      });

      element.dispatchEvent(mouseEnterEvent);
      element.dispatchEvent(mouseOverEvent);

      return true;
    } catch (error) {
      console.error('悬停元素失败:', error);
      return false;
    }
  }

  /**
   * 聚焦元素
   */
  private async focusElement(element: Element): Promise<boolean> {
    try {
      if (element instanceof HTMLElement) {
        element.focus();
        
        const focusEvent = new FocusEvent('focus', { bubbles: true });
        element.dispatchEvent(focusEvent);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('聚焦元素失败:', error);
      return false;
    }
  }

  /**
   * 失焦元素
   */
  private async blurElement(element: Element): Promise<boolean> {
    try {
      if (element instanceof HTMLElement) {
        element.blur();
        
        const blurEvent = new FocusEvent('blur', { bubbles: true });
        element.dispatchEvent(blurEvent);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('失焦元素失败:', error);
      return false;
    }
  }

  /**
   * 获取元素属性
   */
  private async getAttribute(element: Element, attributeName: string): Promise<string | null> {
    try {
      return element.getAttribute(attributeName);
    } catch (error) {
      console.error('获取属性失败:', error);
      return null;
    }
  }

  /**
   * 设置元素文本
   */
  private async setText(element: Element, text: string): Promise<boolean> {
    try {
      if (element instanceof HTMLElement) {
        element.textContent = text;
        
        // 触发变化事件
        const changeEvent = new Event('change', { bubbles: true });
        element.dispatchEvent(changeEvent);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('设置文本失败:', error);
      return false;
    }
  }

  /**
   * 滚动元素到可见区域
   */
  private scrollIntoView(element: Element): void {
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center'
    });
  }

  /**
   * 等待指定时间
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 模拟键盘输入
   */
  async simulateKeyboard(element: Element, key: string, options: KeyboardEventInit = {}): Promise<boolean> {
    try {
      if (!(element instanceof HTMLElement)) {
        console.error('Cannot simulate keyboard: element is not an HTMLElement');
        return false;
      }

      // 确保元素可见并聚焦
      this.scrollIntoView(element);
      await this.sleep(50);
      element.focus();
      await this.sleep(50);

      // 解析键值和修饰键
      const keyInfo = this.parseKeyInput(key, options);

      // 创建键盘事件的通用配置
      const eventConfig = {
        key: keyInfo.key,
        code: keyInfo.code,
        keyCode: keyInfo.keyCode,
        which: keyInfo.keyCode,
        bubbles: true,
        cancelable: true,
        ctrlKey: keyInfo.ctrlKey,
        altKey: keyInfo.altKey,
        shiftKey: keyInfo.shiftKey,
        metaKey: keyInfo.metaKey,
        ...options
      };
      
      console.log('eventConfig:', eventConfig);

      // 触发 keydown 事件
      const keydownEvent = new KeyboardEvent('keydown', eventConfig);
      element.dispatchEvent(keydownEvent);

      // 对于可打印字符，触发 keypress 事件（某些应用需要）
      if (keyInfo.isPrintable && !keydownEvent.defaultPrevented) {
        const keypressEvent = new KeyboardEvent('keypress', {
          ...eventConfig,
          charCode: keyInfo.charCode
        });
        element.dispatchEvent(keypressEvent);
      }

      // 短暂延迟模拟真实按键
      await this.sleep(50);

      // 触发 keyup 事件
      const keyupEvent = new KeyboardEvent('keyup', eventConfig);
      element.dispatchEvent(keyupEvent);

      // 对于特殊键，可能需要额外处理
      if (keyInfo.needsSpecialHandling) {
        await this.handleSpecialKey(element, keyInfo);
      }

      return true;
    } catch (error) {
      console.error('模拟键盘输入失败:', error);
      return false;
    }
  }

  /**
   * 解析键输入和修饰键
   */
  private parseKeyInput(key: string, options: KeyboardEventInit = {}) {
    // 键码映射表
    const keyCodeMap: { [key: string]: number } = {
      'Enter': 13, 'Return': 13,
      'Tab': 9,
      'Escape': 27, 'Esc': 27,
      'Space': 32, ' ': 32,
      'Backspace': 8,
      'Delete': 46,
      'ArrowUp': 38, 'Up': 38,
      'ArrowDown': 40, 'Down': 40,
      'ArrowLeft': 37, 'Left': 37,
      'ArrowRight': 39, 'Right': 39,
      'Home': 36,
      'End': 35,
      'PageUp': 33,
      'PageDown': 34,
      'F1': 112, 'F2': 113, 'F3': 114, 'F4': 115,
      'F5': 116, 'F6': 117, 'F7': 118, 'F8': 119,
      'F9': 120, 'F10': 121, 'F11': 122, 'F12': 123
    };

    // 代码映射表
    const codeMap: { [key: string]: string } = {
      'Enter': 'Enter',
      'Tab': 'Tab',
      'Escape': 'Escape',
      'Space': 'Space',
      'Backspace': 'Backspace',
      'Delete': 'Delete',
      'ArrowUp': 'ArrowUp',
      'ArrowDown': 'ArrowDown',
      'ArrowLeft': 'ArrowLeft',
      'ArrowRight': 'ArrowRight',
      'Home': 'Home',
      'End': 'End',
      'PageUp': 'PageUp',
      'PageDown': 'PageDown'
    };

    const normalizedKey = key.length === 1 ? key : key;
    const keyCode = keyCodeMap[normalizedKey] || (normalizedKey.length === 1 ? normalizedKey.charCodeAt(0) : 0);
    const code = codeMap[normalizedKey] || (normalizedKey.length === 1 ? `Key${normalizedKey.toUpperCase()}` : normalizedKey);

    // 判断是否为可打印字符
    const isPrintable = normalizedKey.length === 1 && keyCode >= 32 && keyCode <= 126;

    // 判断是否需要特殊处理
    const needsSpecialHandling = ['Enter', 'Tab', 'Escape'].includes(normalizedKey);

    return {
      key: normalizedKey,
      code,
      keyCode,
      charCode: isPrintable ? keyCode : 0,
      isPrintable,
      needsSpecialHandling,
      ctrlKey: options.ctrlKey || false,
      altKey: options.altKey || false,
      shiftKey: options.shiftKey || false,
      metaKey: options.metaKey || false
    };
  }

  /**
   * 处理特殊键的额外逻辑
   */
  private async handleSpecialKey(element: HTMLElement, keyInfo: any): Promise<void> {
    switch (keyInfo.key) {
      case 'Enter':
        // 对于表单元素，可能需要触发提交
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
          const form = element.closest('form');
          if (form) {
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(submitEvent);
          }
        }
        break;
      case 'Tab':
        // Tab键可能需要移动焦点
        if (!keyInfo.shiftKey) {
          // 向前移动焦点
          const nextElement = this.getNextFocusableElement(element);
          if (nextElement) {
            nextElement.focus();
          }
        } else {
          // 向后移动焦点
          const prevElement = this.getPreviousFocusableElement(element);
          if (prevElement) {
            prevElement.focus();
          }
        }
        break;
      case 'Escape':
        // Escape键可能需要关闭模态框或取消操作
        element.blur();
        break;
    }
  }

  /**
   * 获取下一个可聚焦元素
   */
  private getNextFocusableElement(element: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements();
    const currentIndex = focusableElements.indexOf(element);
    return currentIndex >= 0 && currentIndex < focusableElements.length - 1
      ? focusableElements[currentIndex + 1]
      : null;
  }

  /**
   * 获取上一个可聚焦元素
   */
  private getPreviousFocusableElement(element: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements();
    const currentIndex = focusableElements.indexOf(element);
    return currentIndex > 0
      ? focusableElements[currentIndex - 1]
      : null;
  }

  /**
   * 获取页面中所有可聚焦元素
   */
  private getFocusableElements(): HTMLElement[] {
    const selector = 'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])';
    return Array.from(document.querySelectorAll(selector))
      .filter(el => {
        const element = el as HTMLElement;
        const isDisabled = 'disabled' in element ? (element as any).disabled : false;
        return element.offsetWidth > 0 &&
               element.offsetHeight > 0 &&
               !isDisabled &&
               getComputedStyle(element).visibility !== 'hidden';
      }) as HTMLElement[];
  }

  /**
   * 拖拽元素
   */
  async dragElement(
    sourceElement: Element, 
    targetElement: Element, 
    options: { duration?: number } = {}
  ): Promise<boolean> {
    try {
      const { duration = 1000 } = options;
      
      const sourceCenter = this.extractor.getElementCenter(sourceElement);
      const targetCenter = this.extractor.getElementCenter(targetElement);

      // 开始拖拽
      const dragStartEvent = new DragEvent('dragstart', {
        bubbles: true,
        cancelable: true,
        clientX: sourceCenter.x,
        clientY: sourceCenter.y
      });

      sourceElement.dispatchEvent(dragStartEvent);

      // 模拟拖拽过程
      await this.sleep(duration / 2);

      // 结束拖拽
      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        cancelable: true,
        clientX: targetCenter.x,
        clientY: targetCenter.y
      });

      targetElement.dispatchEvent(dropEvent);

      const dragEndEvent = new DragEvent('dragend', {
        bubbles: true,
        cancelable: true,
        clientX: targetCenter.x,
        clientY: targetCenter.y
      });

      sourceElement.dispatchEvent(dragEndEvent);

      return true;
    } catch (error) {
      console.error('拖拽元素失败:', error);
      return false;
    }
  }

  /**
   * 等待元素状态改变
   */
  async waitForElementState(
    element: Element, 
    condition: (el: Element) => boolean, 
    timeout: number = 5000
  ): Promise<boolean> {
    const startTime = Date.now();

    return new Promise((resolve) => {
      const checkCondition = () => {
        if (condition(element)) {
          resolve(true);
        } else if (Date.now() - startTime >= timeout) {
          resolve(false);
        } else {
          setTimeout(checkCondition, 100);
        }
      };

      checkCondition();
    });
  }

  /**
   * 获取元素的所有事件监听器（调试用）
   */
  getEventListeners(element: Element): string[] {
    const events: string[] = [];
    
    // 常见事件类型
    const eventTypes = [
      'click', 'dblclick', 'mousedown', 'mouseup', 'mouseover', 'mouseout',
      'keydown', 'keyup', 'keypress', 'focus', 'blur', 'change', 'input',
      'submit', 'load', 'resize', 'scroll', 'touchstart', 'touchend'
    ];

    eventTypes.forEach(eventType => {
      // 检查是否有对应的属性
      const handlerProperty = `on${eventType}` as keyof Element;
      if ((element as any)[handlerProperty]) {
        events.push(eventType);
      }
    });

    return events;
  }
}
