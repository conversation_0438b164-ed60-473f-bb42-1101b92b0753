/**
 * 元素选择器引擎
 * 支持多种选择器类型的元素查找
 */

import type { 
  SelectorType, 
  ElementFindOptions, 
  SelectorValidator 
} from '@/types/element-query';

/**
 * 选择器引擎类
 */
export class SelectorEngine {
  private static instance: SelectorEngine;

  /**
   * 获取单例实例
   */
  static getInstance(): SelectorEngine {
    if (!SelectorEngine.instance) {
      SelectorEngine.instance = new SelectorEngine();
    }
    return SelectorEngine.instance;
  }

  /**
   * 根据选择器类型查找元素
   */
  findElements(
    selectorType: SelectorType,
    selectorValue: string,
    options: ElementFindOptions = {}
  ): Promise<Element[]> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const { timeout = 5000, waitVisible = false, multiple = false, index } = options;

      // 输入验证
      if (!selectorType || typeof selectorType !== 'string') {
        reject(new Error(`Invalid selector type: ${selectorType}`));
        return;
      }

      if (!selectorValue || typeof selectorValue !== 'string') {
        reject(new Error(`Invalid selector value: ${selectorValue}`));
        return;
      }

      console.log(`🔍 SelectorEngine: 查找元素 ${selectorType}="${selectorValue}"`);

      const findAttempt = (): Element[] => {
        let elements: Element[] = [];

        try {
          switch (selectorType) {
            case 'xpath':
              elements = this.findByXPath(selectorValue);
              break;
            case 'css':
              elements = this.findByCSS(selectorValue);
              break;
            case 'id':
              elements = this.findById(selectorValue);
              break;
            case 'class':
              elements = this.findByClass(selectorValue);
              break;
            case 'tag':
              elements = this.findByTag(selectorValue);
              break;
            case 'text':
              elements = this.findByText(selectorValue);
              break;
            case 'attribute':
              elements = this.findByAttribute(selectorValue);
              break;
            default:
              throw new Error(`不支持的选择器类型: ${selectorType}`);
          }

          console.log(`🔍 Found ${elements.length} raw elements before filtering`);

          // 过滤掉无效的元素
          elements = elements.filter((el, index) => {
            if (!el) {
              console.warn(`⚠️ Element at index ${index} is null or undefined, filtering out`);
              return false;
            }
            if (!(el instanceof Element)) {
              console.warn(`⚠️ Element at index ${index} is not a valid DOM Element (type: ${typeof el}), filtering out:`, el);
              return false;
            }
            console.log(`✅ Element at index ${index} is valid:`, el.tagName, el.id || '(no id)', el.className || '(no class)');
            return true;
          });

          console.log(`🔍 After filtering: ${elements.length} valid elements`);

          // 过滤可见元素
          if (waitVisible) {
            const visibleElements = elements.filter(el => this.isElementVisible(el));
            console.log(`🔍 过滤可见元素: ${elements.length} -> ${visibleElements.length}`);
            elements = visibleElements;
          }

          // 处理索引
          if (!multiple && elements.length > 0) {
            const targetIndex = (index !== undefined && index !== null) ? index : 0;
            console.log(`🔍 Processing index: ${targetIndex}, elements.length: ${elements.length}`);

            if (targetIndex >= 0 && targetIndex < elements.length) {
              const selectedElement = elements[targetIndex];
              console.log(`🔍 Selected element at index ${targetIndex}:`, selectedElement);

              // 验证选中的元素
              if (!selectedElement) {
                console.warn(`⚠️ Element at index ${targetIndex} is null or undefined`);
                elements = [];
              } else if (!(selectedElement instanceof Element)) {
                console.warn(`⚠️ Element at index ${targetIndex} is not a valid DOM Element:`, selectedElement);
                elements = [];
              } else {
                elements = [selectedElement];
              }
            } else {
              console.warn(`🔍 索引 ${targetIndex} 超出范围 [0, ${elements.length - 1}]`);
              elements = [];
            }
          }

          return elements;
        } catch (error) {
          console.error(`❌ 选择器查找失败:`, error);
          console.error(`❌ 选择器类型: ${selectorType}, 值: ${selectorValue}`);
          console.error(`❌ 错误堆栈:`, error instanceof Error ? error.stack : 'No stack trace');
          throw error; // 重新抛出错误以便上层处理
        }
      };

      const checkAndResolve = () => {
        try {
          const elements = findAttempt();
          const elapsed = Date.now() - startTime;

          if (elements.length > 0) {
            console.log(`✅ 找到 ${elements.length} 个元素，耗时 ${elapsed}ms`);
            resolve(elements);
          } else if (elapsed >= timeout) {
            console.log(`⏰ 查找超时 (${elapsed}ms)，未找到元素`);
            resolve([]);
          } else {
            // 继续重试
            setTimeout(checkAndResolve, 100);
          }
        } catch (error) {
          console.error(`❌ 元素查找过程中发生错误:`, error);
          reject(error);
        }
      };

      checkAndResolve();
    });
  }

  /**
   * 通过XPath查找元素
   */
  private findByXPath(xpath: string): Element[] {
    const elements: Element[] = [];

    try {
      const result = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null
      );

      for (let i = 0; i < result.snapshotLength; i++) {
        const node = result.snapshotItem(i);
        if (node && node.nodeType === Node.ELEMENT_NODE && node instanceof Element) {
          elements.push(node as Element);
        }
      }
    } catch (error) {
      console.error('XPath查找失败:', error);
      console.error('XPath:', xpath);
    }

    return elements;
  }

  /**
   * 通过CSS选择器查找元素
   */
  private findByCSS(selector: string): Element[] {
    try {
      return Array.from(document.querySelectorAll(selector));
    } catch (error) {
      console.error('CSS选择器查找失败:', error);
      return [];
    }
  }

  /**
   * 通过ID查找元素
   */
  private findById(id: string): Element[] {
    const element = document.getElementById(id);
    return element ? [element] : [];
  }

  /**
   * 通过类名查找元素
   */
  private findByClass(className: string): Element[] {
    try {
      return Array.from(document.getElementsByClassName(className));
    } catch (error) {
      console.error('类名查找失败:', error);
      return [];
    }
  }

  /**
   * 通过标签名查找元素
   */
  private findByTag(tagName: string): Element[] {
    try {
      return Array.from(document.getElementsByTagName(tagName));
    } catch (error) {
      console.error('标签名查找失败:', error);
      return [];
    }
  }

  /**
   * 通过文本内容查找元素
   */
  private findByText(text: string): Element[] {
    const elements: Element[] = [];
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_ELEMENT,
      null
    );

    let node: Node | null;
    while (node = walker.nextNode()) {
      const element = node as Element;
      if (element.textContent && element.textContent.includes(text)) {
        elements.push(element);
      }
    }

    return elements;
  }

  /**
   * 通过属性查找元素
   */
  private findByAttribute(attributeSelector: string): Element[] {
    try {
      // 支持格式: "attr=value" 或 "attr" 或 "[attr=value]"
      let selector = attributeSelector;
      
      if (!selector.startsWith('[')) {
        if (selector.includes('=')) {
          selector = `[${selector}]`;
        } else {
          selector = `[${selector}]`;
        }
      }

      return Array.from(document.querySelectorAll(selector));
    } catch (error) {
      console.error('属性选择器查找失败:', error);
      return [];
    }
  }

  /**
   * 检查元素是否可见
   */
  private isElementVisible(element: Element): boolean {
    // 添加空值检查
    if (!element) {
      return false;
    }

    try {
      const rect = element.getBoundingClientRect();
      const style = window.getComputedStyle(element);

      return (
        rect.width > 0 &&
        rect.height > 0 &&
        style.display !== 'none' &&
        style.visibility !== 'hidden' &&
        style.opacity !== '0'
      );
    } catch (error) {
      console.error('Error checking element visibility in selector engine:', error);
      return false;
    }
  }

  /**
   * 生成元素的XPath
   */
  generateXPath(element: Element): string {
    if (element.id) {
      return `//*[@id="${element.id}"]`;
    }

    const parts: string[] = [];
    let current: Element | null = element;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
      let index = 1;
      let sibling = current.previousElementSibling;

      while (sibling) {
        if (sibling.tagName === current.tagName) {
          index++;
        }
        sibling = sibling.previousElementSibling;
      }

      const tagName = current.tagName.toLowerCase();
      const part = index > 1 ? `${tagName}[${index}]` : tagName;
      parts.unshift(part);

      current = current.parentElement;
    }

    return '/' + parts.join('/');
  }

  /**
   * 生成元素的CSS路径
   */
  generateCSSPath(element: Element): string {
    if (element.id) {
      return `#${element.id}`;
    }

    const parts: string[] = [];
    let current: Element | null = element;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
      let selector = current.tagName.toLowerCase();

      if (current.className) {
        const classes = current.className.trim().split(/\s+/);
        if (classes.length > 0) {
          selector += '.' + classes.join('.');
        }
      }

      // 如果有兄弟元素，添加nth-child
      const siblings = Array.from(current.parentElement?.children || []);
      const sameTagSiblings = siblings.filter(s => s.tagName === current!.tagName);
      
      if (sameTagSiblings.length > 1) {
        const index = sameTagSiblings.indexOf(current) + 1;
        selector += `:nth-child(${index})`;
      }

      parts.unshift(selector);

      // 如果当前元素有唯一的类或ID，可以停止
      if (current.id || (current.className && this.isUniqueSelector(parts.join(' > ')))) {
        break;
      }

      current = current.parentElement;
    }

    return parts.join(' > ');
  }

  /**
   * 检查选择器是否唯一
   */
  private isUniqueSelector(selector: string): boolean {
    try {
      return document.querySelectorAll(selector).length === 1;
    } catch {
      return false;
    }
  }

  /**
   * 验证选择器语法
   */
  validateSelector(selectorType: SelectorType, selectorValue: string): boolean {
    const validators: Record<SelectorType, SelectorValidator> = {
      xpath: this.validateXPath,
      css: this.validateCSS,
      id: this.validateId,
      class: this.validateClass,
      tag: this.validateTag,
      text: this.validateText,
      attribute: this.validateAttribute
    };

    const validator = validators[selectorType];
    return validator ? validator(selectorValue) : false;
  }

  /**
   * 验证XPath语法
   */
  private validateXPath(xpath: string): boolean {
    try {
      document.evaluate(xpath, document, null, XPathResult.ANY_TYPE, null);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证CSS选择器语法
   */
  private validateCSS(selector: string): boolean {
    try {
      document.querySelector(selector);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证ID选择器
   */
  private validateId(id: string): boolean {
    return /^[a-zA-Z][\w-]*$/.test(id);
  }

  /**
   * 验证类名选择器
   */
  private validateClass(className: string): boolean {
    return /^[a-zA-Z][\w-]*$/.test(className);
  }

  /**
   * 验证标签名选择器
   */
  private validateTag(tagName: string): boolean {
    return /^[a-zA-Z][a-zA-Z0-9]*$/.test(tagName);
  }

  /**
   * 验证文本选择器
   */
  private validateText(text: string): boolean {
    return text.length > 0;
  }

  /**
   * 验证属性选择器
   */
  private validateAttribute(attribute: string): boolean {
    // 支持 "attr", "attr=value", "[attr]", "[attr=value]" 格式
    return /^(\[?[\w-]+(\s*=\s*[^[\]]*)?]?|\[[\w-]+\])$/.test(attribute);
  }
}
