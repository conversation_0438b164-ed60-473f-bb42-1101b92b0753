/**
 * 简化的消息处理器
 * 专注于数据转发功能
 */

import {
  createForwardDataMessage
} from '@/types/websocket';
import type {
  BaseMessage
} from '@/types/websocket';

/**
 * 处理来自页面的消息，将所有数据转发给WebSocket服务器
 */
export async function handleMessage(
  message: BaseMessage,
  sendResponse: (response: any) => void
): Promise<boolean> {
  try {
    // 简单的消息验证
    if (!message || !message.type) {
      sendResponse({
        success: false,
        error: 'Invalid message format',
        id: message?.id
      });
      return true;
    }

    // 对于所有消息，都转发给WebSocket服务器
    const forwardMessage = createForwardDataMessage({
      originalType: message.type,
      originalId: message.id,
      originalData: message.data,
      timestamp: new Date().toISOString(),
      source: 'content-script'
    });

    // 这里应该发送到WebSocket服务器
    // 由于这是content script，我们通过background script转发
    chrome.runtime.sendMessage({
      type: 'FORWARD_TO_WEBSOCKET',
      data: forwardMessage
    });

    // 返回成功响应
    sendResponse({
      success: true,
      id: message.id,
      forwarded: true
    });

    return true;
  } catch (error) {
    console.error('Message handling error:', error);
    sendResponse({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      id: message?.id
    });
    return true;
  }
}

/**
 * 处理来自WebSocket服务器的消息
 */
export function handleWebSocketMessage(message: any): void {
  try {
    // 简单地将WebSocket消息转发到页面
    console.log('Received WebSocket message:', message);

    // 可以在这里添加特定的处理逻辑
    // 比如显示通知、更新UI等

  } catch (error) {
    console.error('WebSocket message handling error:', error);
  }
}

/**
 * 初始化消息处理器
 */
export function initializeMessageHandler(): void {
  console.log('Message handler initialized for data forwarding');
}