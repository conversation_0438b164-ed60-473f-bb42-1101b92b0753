/**
 * DevTools extension entry point
 * Creates a custom panel in Chrome DevTools for network interception
 */

console.log('ChatGPT Forward DevTools extension loaded');
console.log('DevTools inspected window URL:', chrome.devtools.inspectedWindow.tabId);

// Create a custom panel in the DevTools
chrome.devtools.panels.create(
  "ChatGPT Forward",
  "", // No icon
  'panel.html',
  (panel) => {
    console.log('ChatGPT Forward DevTools panel created successfully');

    // Panel created successfully
    if (panel) {
      console.log('DevTools panel is ready and accessible');

      // Add some debugging info
      panel.onShown.addListener((_window) => {
        console.log('ChatGPT Forward panel is now visible');
      });

      panel.onHidden.addListener(() => {
        console.log('ChatGPT Forward panel is now hidden');
      });
    } else {
      console.error('Failed to create DevTools panel');
    }
  }
);

export {};
