{"manifest_version": 3, "name": "ChatGPT Forward", "version": "1.0.0", "description": "Intercept and forward all network request data to a WebSocket control server.", "action": {"default_popup": "index.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "devtools_page": "devtools.html", "permissions": ["storage", "tabs", "contextMenus", "webRequest", "declarativeNetRequest", "declarativeNetRequestWithHostAccess", "activeTab", "alarms", "background"], "host_permissions": ["https://chatgpt.com/*", "https://chat.openai.com/*", "<all_urls>"], "background": {"service_worker": "background.js", "type": "module", "persistent": false}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"]}], "options_ui": {"page": "options.html", "open_in_tab": true}, "web_accessible_resources": [{"resources": ["debug.html"], "matches": ["<all_urls>"]}]}