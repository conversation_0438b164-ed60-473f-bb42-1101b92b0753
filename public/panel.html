<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>ChatGPT Forward Panel</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 16px;
            background: #f5f5f5;
            color: #333;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        .status.active {
            background: #e8f5e8;
            color: #2d5a2d;
            border: 1px solid #c3e6c3;
        }
        .status.inactive {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div id="status" class="status inactive">
        DevTools Network Interceptor - Initializing...
    </div>
    <div class="info">
        <p>This panel intercepts network requests and responses from ChatGPT and forwards them to the WebSocket server.</p>
        <p><strong>Note:</strong> Keep DevTools open for the extension to capture response bodies.</p>
        <p>Monitored endpoints:</p>
        <ul>
            <li>chatgpt.com API calls</li>
            <li>chat.openai.com API calls</li>
            <li>api.openai.com API calls</li>
        </ul>
    </div>
    <script src="panel.js"></script>
</body>
</html>
