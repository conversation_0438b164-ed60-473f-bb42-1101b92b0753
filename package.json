{"name": "chatgpt-forward-plugin", "private": true, "version": "1.0.0", "description": "A simplified Chrome extension for intercepting and forwarding all network request data to a WebSocket control server.", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:content": "vite build --config vite.content.config.ts", "build:extension": "tsc -b && vite build && npm run build:content && cp -r public/manifest.json public/icons public/devtools.html public/panel.html dist/", "watch": "vite build --watch", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/chrome": "^0.0.260", "@types/node": "^20.11.24", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}