# ChatGPT Controller API 基本调用流程示例

本文档基于 `client/basic.py` 的实际运行结果，展示了 ChatGPT Controller API 的基本调用流程和响应数据。

## 概述

ChatGPT Controller API 提供了一套完整的接口来管理 ChatGPT 对话，包括创建对话、发送消息、获取对话详情等功能。本示例展示了一次完整的交互流程。

## 环境配置

### 启动客户端
```bash
python basic.py --interactive
```

### 配置信息
- **配置文件**: `config.yaml`
- **认证方式**: Bearer Token
- **API 基础地址**: `http://localhost:8000/api/v1`
- **请求超时**: 30秒

## 完整调用流程

### 1. 创建新对话

**操作**: 选择功能 3 - 创建新对话

**输入参数**:
- 初始提示词: `hello, chatgpt`
- 对话标题: (留空)
- 对话模式: (留空，默认为 None)

**API 请求**:
```http
POST /api/v1/conversations/start
Content-Type: application/json

{
  "init_prompt": "hello, chatgpt"
}
```

**响应结果**:
```json
{
  "success": true,
  "message": "Conversation started successfully",
  "timestamp": "2025-07-29T15:39:20.777651",
  "conversation_id": "68887a9b-6ce0-8012-91fe-3baf1e705ffb",
  "url": "https://chatgpt.com/c/68887a9b-6ce0-8012-91fe-3baf1e705ffb",
  "redirect_url": "https://chatgpt.com/c/68887a9b-6ce0-8012-91fe-3baf1e705ffb"
}
```

**关键信息**:
- ✅ 对话创建成功
- 🆔 对话ID: `68887a9b-6ce0-8012-91fe-3baf1e705ffb`
- 🔗 ChatGPT 链接已生成

### 2. 获取对话详情

**操作**: 选择功能 5 - 获取对话详情

**输入参数**:
- 对话ID: `68887a9b-6ce0-8012-91fe-3baf1e705ffb`

**API 请求**:
```http
GET /api/v1/conversations/68887a9b-6ce0-8012-91fe-3baf1e705ffb
```

**响应结果**:
```json
{
  "success": true,
  "message": null,
  "timestamp": "2025-07-29T15:39:53.811417",
  "conversation": {
    "id": "68887a9b-6ce0-8012-91fe-3baf1e705ffb",
    "title": null,
    "mode": null,
    "init_prompt": "hello, chatgpt",
    "status": "active",
    "is_cached": false,
    "created_at": "2025-07-29T15:39:20.724592",
    "updated_at": "2025-07-29T15:39:20.724609",
    "last_accessed": "2025-07-29T15:39:53.791684",
    "url": "https://chatgpt.com/?q=hello%2C%20chatgpt",
    "redirect_url": "https://chatgpt.com/",
    "message_count": 0
  },
  "messages": [],
  "cached": false,
  "cache_timestamp": null
}
```

**状态说明**:
- 📊 对话状态: `active`
- 💬 消息数量: 0 (初始状态)
- 🗂️ 缓存状态: 未缓存

### 3. 等待 ChatGPT 响应

再次获取对话详情，发现 ChatGPT 已经响应：

**响应结果**:
```json
{
  "conversation": {
    "title": null,
    "message_count": 2
  },
  "messages": [
    {
      "id": 77,
      "role": "user",
      "content": "hello, chatgpt",
      "message_id": "103daa94-b8fb-44df-a55b-905f1b1f6a47",
      "created_at": "2025-07-29T15:39:06.020000",
      "metadata": {
        "status": "finished_successfully",
        "message_source": "instant-query"
      }
    },
    {
      "id": 78,
      "role": "assistant",
      "content": "Hello! 😊  \nHow can I help you today?",
      "message_id": "e0a8c366-c2af-4d56-bfc1-f47cc190ddb3",
      "model": "gpt-4o",
      "created_at": "2025-07-29T15:39:08.069409",
      "metadata": {
        "status": "finished_successfully",
        "end_turn": true,
        "model_slug": "gpt-4o"
      }
    }
  ]
}
```

**消息分析**:
- 👤 用户消息: "hello, chatgpt"
- 🤖 AI 响应: "Hello! 😊 How can I help you today?"
- 🧠 使用模型: GPT-4o
- ⏱️ 响应时间: ~2秒

### 4. 发送新消息

**操作**: 选择功能 4 - 发送消息

**输入参数**:
- 对话ID: `68887a9b-6ce0-8012-91fe-3baf1e705ffb`
- 消息内容: `nice to see you`

**API 请求**:
```http
POST /api/v1/conversations/68887a9b-6ce0-8012-91fe-3baf1e705ffb/messages
Content-Type: application/json

{
  "message": "nice to see you"
}
```

**响应结果**:
```json
{
  "success": true,
  "message": "Message sent successfully",
  "timestamp": "2025-07-29T15:41:04.992405",
  "message_id": "81",
  "sent_at": "2025-07-29T15:41:04.932097"
}
```

**发送状态**:
- ✅ 消息发送成功
- 🆔 消息ID: 81
- ⏰ 发送时间: 2025-07-29T15:41:04.932097

### 5. 查看更新后的对话

再次获取对话详情，查看新消息：

**响应结果**:
```json
{
  "conversation": {
    "title": "Greeting conversation",
    "message_count": 3,
    "is_cached": true,
    "updated_at": "2025-07-29T15:41:04.932212"
  },
  "messages": [
    {
      "id": 79,
      "role": "user",
      "content": "hello, chatgpt"
    },
    {
      "id": 80,
      "role": "assistant", 
      "content": "Hello! 😊  \nHow can I help you today?",
      "model": "gpt-4o"
    },
    {
      "id": 81,
      "role": "user",
      "content": "nice to see you",
      "created_at": "2025-07-29T07:41:04",
      "sent_at": "2025-07-29T15:41:04.932097"
    }
  ]
}
```

**更新状态**:
- 📝 对话标题: 自动生成为 "Greeting conversation"
- 💬 消息总数: 3条
- 🗂️ 缓存状态: 已缓存
- 📅 最后更新: 2025-07-29T15:41:04.932212

## 错误处理示例

在测试过程中遇到的错误：

### 1. 参数错误
```json
{
  "detail": [
    {
      "type": "missing",
      "loc": ["body", "message"],
      "msg": "Field required",
      "input": {"content": "nice to see you"}
    }
  ]
}
```

**原因**: 发送消息时使用了错误的字段名 `content`，应该使用 `message`

### 2. 资源不存在
```json
{
  "detail": "Conversation content not found not found"
}
```

**原因**: 使用了无效的对话ID

## 总结

### 成功的调用流程
1. ✅ **创建对话** → 获得对话ID和ChatGPT链接
2. ✅ **查看详情** → 确认对话状态和消息
3. ✅ **发送消息** → 成功发送用户消息
4. ✅ **获取更新** → 查看完整的对话历史

### 关键特性
- 🔄 **自动同步**: API会自动同步ChatGPT的响应
- 🏷️ **智能标题**: 系统会根据对话内容自动生成标题
- 💾 **缓存机制**: 对话数据会被缓存以提高性能
- 🔗 **直接链接**: 提供直接访问ChatGPT对话的链接

### 最佳实践
- 📋 使用正确的字段名发送请求
- 🔍 定期检查对话状态获取最新消息
- ⚠️ 妥善处理API错误响应
- 🆔 保存对话ID以便后续操作
