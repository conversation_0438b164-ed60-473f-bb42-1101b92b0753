# ChatGPT Controller Docker Setup

This directory contains Docker configuration for running the ChatGPT Controller as a server with both HTTP API (port 8000) and WebSocket (port 8765) services.

## Quick Start

### 1. Using the Startup Script (Recommended)

```bash
# Development mode
./start-server.sh --dev

# Production mode
./start-server.sh --prod

# With logs
./start-server.sh --dev --logs

# Force rebuild
./start-server.sh --build
```

### 2. Using Docker Compose Directly

```bash
# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Production
docker-compose --profile production up -d

# View logs
docker-compose logs -f chatgpt-controller
```

## Files Overview

- **`Dockerfile`** - Multi-stage Docker build for the controller
- **`docker-compose.yml`** - Main compose configuration
- **`docker-compose.dev.yml`** - Development overrides
- **`nginx/nginx.conf`** - Nginx reverse proxy configuration
- **`start-server.sh`** - Convenient startup script
- **`.env.example`** - Environment variables template

## Services

### ChatGPT Controller
- **HTTP API**: `http://localhost:8000`
- **WebSocket**: `ws://localhost:8765`
- **Health Check**: `http://localhost:8000/health`

### Nginx (Production)
- **HTTP**: `http://localhost:80`
- **HTTPS**: `https://localhost:443` (with SSL configured)

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and modify:

```bash
cp .env.example .env
# Edit .env with your settings
```

### SSL/TLS Setup

For production with HTTPS:

```bash
# Create SSL directory
mkdir -p nginx/ssl

# Option 1: Self-signed (development)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem

# Option 2: Let's Encrypt (production)
# See DEPLOYMENT_TUTORIAL.md for detailed instructions
```

## Development

### Live Code Reloading

Development mode mounts your source code and enables auto-reload:

```bash
./start-server.sh --dev
```

### Debugging

Enable debug mode:

```bash
# Set in .env file
DEBUG=true
LOG_LEVEL=DEBUG

# Or pass environment variables
docker-compose up -e DEBUG=true -e LOG_LEVEL=DEBUG
```

## Production Deployment

### 1. Configure Domain

Edit `nginx/nginx.conf` and replace `your-domain.com` with your domain.

### 2. Set up SSL

Follow the SSL setup instructions in `DEPLOYMENT_TUTORIAL.md`.

### 3. Start Production Services

```bash
./start-server.sh --prod
```

### 4. Configure Firewall

```bash
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```

## Monitoring

### Health Checks

```bash
# Check service health
curl http://localhost:8000/health

# Check container status
docker-compose ps
```

### Logs

```bash
# View all logs
docker-compose logs

# Follow logs
docker-compose logs -f chatgpt-controller

# View specific service logs
docker-compose logs nginx
```

## Maintenance

### Updates

```bash
# Pull latest code
git pull

# Rebuild and restart
docker-compose down
docker-compose up --build -d
```

### Backup

```bash
# Backup data
tar -czf backup-$(date +%Y%m%d).tar.gz controller/data/

# Backup configuration
cp .env .env.backup
cp docker-compose.yml docker-compose.yml.backup
```

### Cleanup

```bash
# Stop services
docker-compose down

# Remove volumes (WARNING: This deletes data)
docker-compose down -v

# Clean up unused images
docker system prune -a
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Check if ports 8000/8765 are in use
2. **Permission issues**: Ensure proper file permissions
3. **SSL errors**: Verify certificate paths and validity
4. **WebSocket issues**: Check nginx proxy configuration

### Debug Commands

```bash
# Check port usage
sudo netstat -tulpn | grep :8000
sudo netstat -tulpn | grep :8765

# Test WebSocket connection
wscat -c ws://localhost:8765

# Check container logs
docker-compose logs chatgpt-controller

# Execute commands in container
docker-compose exec chatgpt-controller bash
```

## Security Notes

- The container runs as a non-root user
- Use HTTPS in production
- Configure proper firewall rules
- Regularly update dependencies
- Monitor access logs for suspicious activity

## Support

For detailed deployment instructions, see `DEPLOYMENT_TUTORIAL.md`.

For issues:
1. Check the logs: `docker-compose logs`
2. Verify configuration: `docker-compose config`
3. Test endpoints with curl/wscat
