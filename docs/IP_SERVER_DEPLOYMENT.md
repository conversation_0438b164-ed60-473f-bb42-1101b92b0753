# IP服务器部署指南 - 同时支持HTTP/HTTPS访问

本指南专门针对使用纯IP地址（无域名）的服务器，同时支持HTTP和HTTPS访问。

## 🚀 快速部署

### 1. 一键启动（推荐）

```bash
# 生产模式启动，自动生成SSL证书
./start-server.sh --prod

# 或者先生成SSL证书，再启动
./generate-ssl-cert.sh --ip YOUR_SERVER_IP
./start-server.sh --prod
```

### 2. 手动部署

```bash
# 1. 生成SSL证书
./generate-ssl-cert.sh --ip *************  # 替换为你的服务器IP

# 2. 启动服务
docker-compose --profile production up -d

# 3. 检查状态
docker-compose ps
curl http://localhost/health
```

## 🌐 访问方式

部署完成后，可以通过以下方式访问：

### HTTP访问（无需证书）
```
http://YOUR_SERVER_IP/api/          # API接口
http://YOUR_SERVER_IP/health        # 健康检查
ws://YOUR_SERVER_IP/ws              # WebSocket连接
```

### HTTPS访问（自签名证书）
```
https://YOUR_SERVER_IP/api/         # API接口（需要接受证书警告）
https://YOUR_SERVER_IP/health       # 健康检查
wss://YOUR_SERVER_IP/ws             # 安全WebSocket连接
```

### 直接访问（绕过nginx）
```
http://YOUR_SERVER_IP:8000          # 直接访问API服务
ws://YOUR_SERVER_IP:8765            # 直接访问WebSocket服务
```

## 🔧 SSL证书管理

### 自动生成证书

```bash
# 自动检测IP并生成证书
./generate-ssl-cert.sh

# 指定IP地址生成证书
./generate-ssl-cert.sh --ip *************

# 强制重新生成证书
./generate-ssl-cert.sh --force

# 生成长期有效证书（3年）
./generate-ssl-cert.sh --days 1095
```

### 手动生成证书

```bash
# 创建SSL目录
mkdir -p nginx/ssl

# 生成支持IP地址的自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem \
    -subj "/C=CN/ST=State/L=City/O=ChatGPT Controller/CN=*************" \
    -addext "subjectAltName=IP:*************,IP:127.0.0.1,DNS:localhost"

# 设置权限
chmod 600 nginx/ssl/key.pem
chmod 644 nginx/ssl/cert.pem
```

## 🔒 浏览器证书警告处理

由于使用自签名证书，浏览器会显示安全警告：

### Chrome/Edge
1. 访问 `https://YOUR_SERVER_IP/api/`
2. 点击"高级"
3. 点击"继续前往 YOUR_SERVER_IP（不安全）"

### Firefox
1. 访问 `https://YOUR_SERVER_IP/api/`
2. 点击"高级"
3. 点击"接受风险并继续"

### 永久信任证书（可选）
```bash
# 下载证书文件
curl -k https://YOUR_SERVER_IP/api/ --cert-status -v 2>&1 | grep "Server certificate" -A 20

# 或者直接下载
openssl s_client -connect YOUR_SERVER_IP:443 -servername YOUR_SERVER_IP < /dev/null 2>/dev/null | openssl x509 -outform PEM > server.crt

# 将证书添加到系统信任存储（根据操作系统不同）
```

## 🛠️ 配置说明

### Nginx配置特点

- **支持IP访问**: `server_name _` 接受任何服务器名称
- **HTTP/HTTPS并存**: 80和443端口同时提供服务
- **CORS支持**: 允许跨域请求
- **WebSocket代理**: 支持HTTP和HTTPS的WebSocket连接
- **无HSTS**: 避免自签名证书的浏览器问题

### 端口映射

```yaml
ports:
  - "80:80"      # HTTP访问
  - "443:443"    # HTTPS访问
  - "8000:8000"  # 直接API访问
  - "8765:8765"  # 直接WebSocket访问
```

## 🔥 防火墙配置

### Ubuntu/Debian
```bash
# 允许HTTP和HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 可选：允许直接访问
sudo ufw allow 8000/tcp
sudo ufw allow 8765/tcp

# 检查状态
sudo ufw status
```

### CentOS/RHEL
```bash
# 允许HTTP和HTTPS
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp

# 可选：允许直接访问
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --permanent --add-port=8765/tcp

# 重新加载
sudo firewall-cmd --reload
```

## 📊 监控和维护

### 健康检查

```bash
# HTTP健康检查
curl http://YOUR_SERVER_IP/health

# HTTPS健康检查（忽略证书）
curl -k https://YOUR_SERVER_IP/health

# WebSocket连接测试
wscat -c ws://YOUR_SERVER_IP/ws
wscat -c wss://YOUR_SERVER_IP/ws --no-check
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看nginx日志
docker-compose logs nginx

# 查看控制器日志
docker-compose logs chatgpt-controller

# 实时日志
docker-compose logs -f
```

### 证书信息查看

```bash
# 查看证书详情
openssl x509 -in nginx/ssl/cert.pem -text -noout

# 查看证书有效期
openssl x509 -in nginx/ssl/cert.pem -noout -dates

# 检查证书是否匹配私钥
openssl x509 -noout -modulus -in nginx/ssl/cert.pem | openssl md5
openssl rsa -noout -modulus -in nginx/ssl/key.pem | openssl md5
```

## 🚨 故障排除

### 常见问题

#### 1. HTTPS无法访问
```bash
# 检查证书文件是否存在
ls -la nginx/ssl/

# 检查nginx容器状态
docker-compose logs nginx

# 重新生成证书
./generate-ssl-cert.sh --force
docker-compose restart nginx
```

#### 2. 证书警告无法绕过
```bash
# 确保证书包含正确的IP地址
openssl x509 -in nginx/ssl/cert.pem -text -noout | grep -A 5 "Subject Alternative Name"

# 重新生成包含IP的证书
./generate-ssl-cert.sh --ip YOUR_SERVER_IP --force
```

#### 3. WebSocket连接失败
```bash
# 检查WebSocket代理配置
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: test" http://YOUR_SERVER_IP/ws

# 检查直接连接
wscat -c ws://YOUR_SERVER_IP:8765
```

#### 4. CORS错误
nginx配置已包含CORS头，如果仍有问题：
```bash
# 检查OPTIONS请求
curl -X OPTIONS http://YOUR_SERVER_IP/api/ -H "Origin: http://example.com" -v
```

## 🔄 更新和维护

### 更新服务
```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose down
docker-compose --profile production up --build -d
```

### 证书续期
```bash
# 重新生成证书（默认1年有效期）
./generate-ssl-cert.sh --force

# 重启nginx以加载新证书
docker-compose restart nginx
```

### 备份重要文件
```bash
# 备份数据和配置
tar -czf backup-$(date +%Y%m%d).tar.gz \
    controller/data/ \
    controller/config.json \
    nginx/ssl/ \
    .env

# 恢复备份
tar -xzf backup-20240101.tar.gz
```

## 📝 客户端连接示例

### JavaScript (浏览器)
```javascript
// HTTP API调用
fetch('http://YOUR_SERVER_IP/api/endpoint')
  .then(response => response.json())
  .then(data => console.log(data));

// HTTPS API调用（需要用户接受证书）
fetch('https://YOUR_SERVER_IP/api/endpoint')
  .then(response => response.json())
  .then(data => console.log(data));

// WebSocket连接
const ws = new WebSocket('ws://YOUR_SERVER_IP/ws');
// 或 HTTPS WebSocket
const wss = new WebSocket('wss://YOUR_SERVER_IP/ws');
```

### Python客户端
```python
import requests
import websocket

# HTTP请求
response = requests.get('http://YOUR_SERVER_IP/api/endpoint')

# HTTPS请求（忽略证书验证）
response = requests.get('https://YOUR_SERVER_IP/api/endpoint', verify=False)

# WebSocket连接
ws = websocket.WebSocket()
ws.connect('ws://YOUR_SERVER_IP/ws')
```

## 🎯 生产环境建议

1. **使用反向代理**: nginx提供更好的性能和安全性
2. **启用访问日志**: 监控API使用情况
3. **设置速率限制**: 防止API滥用
4. **定期更新**: 保持Docker镜像和依赖最新
5. **监控资源**: 使用docker stats监控容器资源使用
6. **备份策略**: 定期备份数据和配置文件

通过以上配置，你的ChatGPT Controller服务器将同时支持HTTP和HTTPS访问，适合在纯IP环境下部署使用。
