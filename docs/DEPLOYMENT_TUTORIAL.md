# ChatGPT Controller Server Deployment Tutorial

This tutorial explains how to deploy the ChatGPT Controller as a server using Docker, exposing both HTTP API (port 8000) and WebSocket (port 8765) services.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Development Setup](#development-setup)
3. [Production Deployment](#production-deployment)
4. [Server Configuration](#server-configuration)
5. [SSL/TLS Setup](#ssltls-setup)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Troubleshooting](#troubleshooting)

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- Ports 8000 and 8765 available on your server

### 1. Clone and Build

```bash
# Clone the repository
git clone <your-repo-url>
cd chatgpt-forward-plugin

# Build and start the services
docker-compose up -d
```

### 2. Verify Services

```bash
# Check if services are running
docker-compose ps

# Check logs
docker-compose logs chatgpt-controller

# Test HTTP API
curl http://localhost:8000/health

# Test WebSocket (using wscat if installed)
wscat -c ws://localhost:8765
```

## Development Setup

### Basic Development Environment

```bash
# Start only the controller service
docker-compose up chatgpt-controller

# Or build and run with live logs
docker-compose up --build chatgpt-controller
```

### Environment Variables

Create a `.env` file in the project root:

```env
# Server Configuration
API_HOST=0.0.0.0
WS_HOST=0.0.0.0
LOG_LEVEL=DEBUG

# Optional: Database settings
DATABASE_URL=sqlite:///app/data/chatgpt_controller.db
```

### Volume Mounts for Development

The docker-compose.yml already includes volume mounts for:
- `./controller/data:/app/data` - Persistent data storage
- `./controller/logs:/app/logs` - Log files
- `./controller/config.json:/app/config.json:ro` - Configuration file

## Production Deployment

### 1. Enable Production Profile

```bash
# Start with nginx reverse proxy
docker-compose --profile production up -d
```

### 2. Configure Domain and SSL

Edit `nginx/nginx.conf` and replace `your-domain.com` with your actual domain:

```nginx
server_name your-domain.com;
```

### 3. SSL Certificate Setup

#### Option A: Let's Encrypt with Certbot

```bash
# Install certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Copy certificates to nginx volume
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem
```

#### Option B: Self-signed Certificate (Development)

```bash
# Create SSL directory
mkdir -p nginx/ssl

# Generate self-signed certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/key.pem \
    -out nginx/ssl/cert.pem \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
```

### 4. Firewall Configuration

```bash
# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# For direct access (optional)
sudo ufw allow 8000/tcp
sudo ufw allow 8765/tcp
```

## Server Configuration

### Resource Limits

Add resource limits to docker-compose.yml:

```yaml
services:
  chatgpt-controller:
    # ... other configuration
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
```

### Environment-specific Configuration

Create environment-specific compose files:

```bash
# docker-compose.prod.yml
version: '3.8'
services:
  chatgpt-controller:
    environment:
      - LOG_LEVEL=WARNING
      - API_HOST=0.0.0.0
      - WS_HOST=0.0.0.0
    restart: always
```

Run with:
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## Monitoring and Maintenance

### Health Checks

The services include built-in health checks:

```bash
# Check health status
docker-compose ps
curl http://localhost:8000/health
```

### Log Management

```bash
# View logs
docker-compose logs -f chatgpt-controller

# Rotate logs (add to crontab)
docker-compose exec chatgpt-controller logrotate /etc/logrotate.conf
```

### Backup Data

```bash
# Backup data directory
tar -czf backup-$(date +%Y%m%d).tar.gz controller/data/

# Restore data
tar -xzf backup-20240101.tar.gz
```

### Updates

```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose up --build -d

# Or rolling update
docker-compose up -d --no-deps chatgpt-controller
```

## Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8000
sudo netstat -tulpn | grep :8765

# Kill the process or change ports in docker-compose.yml
```

#### Permission Issues
```bash
# Fix ownership
sudo chown -R $USER:$USER controller/data controller/logs

# Or run with proper permissions
docker-compose exec chatgpt-controller chown -R appuser:appuser /app/data /app/logs
```

#### WebSocket Connection Issues
```bash
# Check if WebSocket is accessible
wscat -c ws://your-server:8765

# Check nginx WebSocket proxy configuration
docker-compose logs nginx
```

#### SSL Certificate Issues
```bash
# Test SSL configuration
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Check certificate expiry
openssl x509 -in nginx/ssl/cert.pem -text -noout | grep "Not After"
```

### Debug Mode

Enable debug mode:

```bash
# Set environment variable
export LOG_LEVEL=DEBUG

# Or modify docker-compose.yml
environment:
  - LOG_LEVEL=DEBUG

# Restart services
docker-compose restart chatgpt-controller
```

### Performance Tuning

#### For High Traffic

```yaml
# In docker-compose.yml
services:
  chatgpt-controller:
    environment:
      - WORKERS=4  # Increase worker processes
      - MAX_CLIENTS=50  # Increase max WebSocket clients
```

#### Nginx Optimization

```nginx
# In nginx.conf
worker_processes auto;
worker_connections 2048;

# Increase rate limits
limit_req_zone $binary_remote_addr zone=api:10m rate=50r/s;
```

## Security Considerations

1. **Use HTTPS in production** - Always enable SSL/TLS
2. **Firewall configuration** - Only expose necessary ports
3. **Regular updates** - Keep Docker images and dependencies updated
4. **Rate limiting** - Configure appropriate rate limits in nginx
5. **Access logs** - Monitor access patterns for suspicious activity
6. **Non-root user** - The Dockerfile already uses a non-root user

## Support

For issues and questions:
1. Check the logs: `docker-compose logs chatgpt-controller`
2. Verify configuration: `docker-compose config`
3. Test connectivity: Use curl/wscat to test endpoints
4. Review this tutorial for common solutions

## Next Steps

- Set up monitoring with Prometheus/Grafana
- Implement log aggregation with ELK stack
- Configure automated backups
- Set up CI/CD pipeline for automated deployments
