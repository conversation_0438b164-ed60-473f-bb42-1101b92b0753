#!/bin/bash

# Test script for ChatGPT Controller Server
# Tests both HTTP and HTTPS access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
SERVER_IP="localhost"
TEST_WEBSOCKET=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            SERVER_IP="$2"
            shift 2
            ;;
        --ws|--websocket)
            TEST_WEBSOCKET=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --ip IP_ADDRESS    Server IP address (default: localhost)"
            echo "  --ws, --websocket  Test WebSocket connections (requires wscat)"
            echo "  -h, --help         Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                        # Test localhost"
            echo "  $0 --ip *************     # Test specific IP"
            echo "  $0 --ws                   # Include WebSocket tests"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

print_status "Testing ChatGPT Controller Server at: $SERVER_IP"
echo "========================================================"

# Test functions
test_http_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    print_status "Testing $description: $url"
    
    if response=$(curl -s -w "%{http_code}" -o /tmp/response.txt "$url" 2>/dev/null); then
        status_code="${response: -3}"
        if [ "$status_code" = "$expected_status" ]; then
            print_success "✓ $description - Status: $status_code"
            return 0
        else
            print_warning "⚠ $description - Unexpected status: $status_code (expected: $expected_status)"
            return 1
        fi
    else
        print_error "✗ $description - Connection failed"
        return 1
    fi
}

test_https_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    print_status "Testing $description: $url"
    
    if response=$(curl -k -s -w "%{http_code}" -o /tmp/response.txt "$url" 2>/dev/null); then
        status_code="${response: -3}"
        if [ "$status_code" = "$expected_status" ]; then
            print_success "✓ $description - Status: $status_code"
            return 0
        else
            print_warning "⚠ $description - Unexpected status: $status_code (expected: $expected_status)"
            return 1
        fi
    else
        print_error "✗ $description - Connection failed"
        return 1
    fi
}

test_websocket() {
    local url=$1
    local description=$2
    
    print_status "Testing $description: $url"
    
    if command -v wscat >/dev/null 2>&1; then
        if timeout 5 wscat -c "$url" -x 'ping' >/dev/null 2>&1; then
            print_success "✓ $description - Connection successful"
            return 0
        else
            print_warning "⚠ $description - Connection failed or timeout"
            return 1
        fi
    else
        print_warning "⚠ wscat not installed, skipping WebSocket test"
        return 1
    fi
}

# Test results counters
total_tests=0
passed_tests=0

run_test() {
    total_tests=$((total_tests + 1))
    if "$@"; then
        passed_tests=$((passed_tests + 1))
    fi
}

echo ""
print_status "Testing HTTP endpoints..."
echo "----------------------------------------"

# Test HTTP endpoints
run_test test_http_endpoint "http://$SERVER_IP/health" "HTTP Health Check"
run_test test_http_endpoint "http://$SERVER_IP/api/" "HTTP API Root" 301
run_test test_http_endpoint "http://$SERVER_IP:8000/health" "Direct HTTP API Health"

echo ""
print_status "Testing HTTPS endpoints..."
echo "----------------------------------------"

# Test HTTPS endpoints
run_test test_https_endpoint "https://$SERVER_IP/health" "HTTPS Health Check"
run_test test_https_endpoint "https://$SERVER_IP/api/" "HTTPS API Root" 301

echo ""
print_status "Testing direct API access..."
echo "----------------------------------------"

# Test direct API access (bypassing nginx)
run_test test_http_endpoint "http://$SERVER_IP:8000" "Direct API Server"

# Test WebSocket if requested
if [ "$TEST_WEBSOCKET" = true ]; then
    echo ""
    print_status "Testing WebSocket connections..."
    echo "----------------------------------------"
    
    run_test test_websocket "ws://$SERVER_IP/ws" "HTTP WebSocket"
    run_test test_websocket "wss://$SERVER_IP/ws" "HTTPS WebSocket"
    run_test test_websocket "ws://$SERVER_IP:8765" "Direct WebSocket"
fi

# Test SSL certificate info
echo ""
print_status "SSL Certificate Information..."
echo "----------------------------------------"

if openssl s_client -connect "$SERVER_IP:443" -servername "$SERVER_IP" </dev/null 2>/dev/null | openssl x509 -noout -text 2>/dev/null | grep -E "(Subject:|Not After|DNS:|IP Address:)" >/dev/null 2>&1; then
    print_success "✓ SSL certificate is valid and accessible"
    echo "Certificate details:"
    openssl s_client -connect "$SERVER_IP:443" -servername "$SERVER_IP" </dev/null 2>/dev/null | openssl x509 -noout -text 2>/dev/null | grep -E "(Subject:|Not After|DNS:|IP Address:)" | sed 's/^/  /'
else
    print_warning "⚠ Could not retrieve SSL certificate information"
fi

# Summary
echo ""
echo "========================================================"
print_status "Test Summary"
echo "----------------------------------------"
echo "Total tests: $total_tests"
echo "Passed: $passed_tests"
echo "Failed: $((total_tests - passed_tests))"

if [ $passed_tests -eq $total_tests ]; then
    print_success "All tests passed! ✓"
    echo ""
    print_status "Your server is accessible via:"
    echo "  - HTTP:  http://$SERVER_IP/api/"
    echo "  - HTTPS: https://$SERVER_IP/api/ (accept certificate warning)"
    echo "  - Direct: http://$SERVER_IP:8000"
    
    if [ "$TEST_WEBSOCKET" = true ]; then
        echo "  - WebSocket HTTP:  ws://$SERVER_IP/ws"
        echo "  - WebSocket HTTPS: wss://$SERVER_IP/ws"
        echo "  - WebSocket Direct: ws://$SERVER_IP:8765"
    fi
    
    exit 0
else
    print_warning "Some tests failed. Check the output above for details."
    echo ""
    print_status "Troubleshooting tips:"
    echo "  1. Ensure the server is running: docker-compose ps"
    echo "  2. Check logs: docker-compose logs"
    echo "  3. Verify firewall settings"
    echo "  4. For HTTPS issues, regenerate SSL certificate: ./generate-ssl-cert.sh --force"
    
    exit 1
fi
